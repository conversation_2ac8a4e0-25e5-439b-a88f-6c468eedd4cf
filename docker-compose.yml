services:
  web:
    image: node:22-alpine
    command: sh -c "npm install && npm start"
    working_dir: /app
    volumes:
      - ./web:/app
      - ./web/node_modules:/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    environment:
      REACT_APP_BACKEND_URL: 'http://localhost:4000'
    stdin_open: true
  backend:
    build:
      context: .
      dockerfile: docker-compose-backend.Dockerfile
    command: gow run main.go
    tty: true
    working_dir: /app
    volumes:
      - ./backend:/app
    ports:
     - "4000:4000"
    depends_on:
      - mongo
    environment:
      MONGODB_HOST: mongodb
      MONGODB_USER: root
      MONGODB_PASS: password123

  mongo:
    image: mongo:6.0.2
    container_name: mongodb
    restart: always
    ports:
      - "6000:27017"
    volumes:
      - mongodb:/data/db
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: password123

volumes:
  mongodb:
