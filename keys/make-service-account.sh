#!/bin/bash

# Initial sample from https://gist.github.com/innovia/fbba8259042f71db98ea8d4ad19bd708

SERVICE_ACCOUNT_NAME="silta-dashboard-sa"
SERVICE_ACCOUNT_NAMESPACE="kube-system"
# Reuse cluster-admin role
CLUSTER_ROLE="cluster-admin"
CLUSTER_ROLEBINDING_NAME="silta-dashboard"

KUBECFG_FILE_NAME="k8s-${SERVICE_ACCOUNT_NAME}-${SERVICE_ACCOUNT_NAMESPACE}-conf"

create_service_account() {
    echo -e "\\nCreating a service account in ${SERVICE_ACCOUNT_NAMESPACE} namespace: ${SERVICE_ACCOUNT_NAME}"
    kubectl create sa "${SERVICE_ACCOUNT_NAME}" --namespace "${SERVICE_ACCOUNT_NAMESPACE}"
}

remove_sa_secret() {
    echo -e -n "\\n* Removing old SA secret and waiting for a new one..."
    kubectl delete secret --namespace "${SERVICE_ACCOUNT_NAMESPACE}" "${SECRET_NAME}"

    # sleep 5
    printf "  done, will sleep for 5 seconds while new secret is created"
}

get_secret_name_from_service_account() {
    echo -e "\\n* Getting secret of service account ${SERVICE_ACCOUNT_NAME} on ${SERVICE_ACCOUNT_NAMESPACE}"
    SECRET_NAME=$(kubectl get sa "${SERVICE_ACCOUNT_NAME}" --namespace="${SERVICE_ACCOUNT_NAMESPACE}" -o json | jq -r '.secrets[].name')
    echo "  Secret name: ${SECRET_NAME}"
}

# get_secret_name_from_service_account() {
#     SECRET_NAME="${SERVICE_ACCOUNT_NAME}-secret"
# }

# 1.24+ only
# https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/#manually-create-a-long-lived-api-token-for-a-serviceaccount

create_new_sa_token() {
    echo -e "\\nCreating a new token for service account ${SERVICE_ACCOUNT_NAME} on ${SERVICE_ACCOUNT_NAMESPACE}"
    # Create new long lived token for service account
    SECRET_NAME="${SERVICE_ACCOUNT_NAME}-secret"
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Secret
metadata:
    name: ${SECRET_NAME}
    namespace: ${SERVICE_ACCOUNT_NAMESPACE}
    annotations:
        kubernetes.io/service-account.name: ${SERVICE_ACCOUNT_NAME}
type: kubernetes.io/service-account-token
EOF
}

extract_ca_crt_from_secret() {
    echo -e -n "\\n* Extracting ca.crt from secret..."
    kubectl get secret --namespace "${SERVICE_ACCOUNT_NAMESPACE}" "${SECRET_NAME}" -o json | jq \
    -r '.data["ca.crt"]' | base64 -d > "ca.crt"
    printf "done"
}

get_user_token_from_secret() {
    echo -e -n "\\n* Getting user token from secret..."
    USER_TOKEN=$(kubectl get secret --namespace "${SERVICE_ACCOUNT_NAMESPACE}" "${SECRET_NAME}" -o json | jq -r '.data["token"]' | base64 -d)
    printf "done"
}

set_kube_config_values() {
    context=$(kubectl config current-context)
    echo -e "\\n* Setting current context to: $context"

    CLUSTER_NAME=$(kubectl config get-contexts "$context" | awk '{print $3}' | tail -n 1)
    echo "  Cluster name: ${CLUSTER_NAME}"

    ENDPOINT=$(kubectl config view \
    -o jsonpath="{.clusters[?(@.name == \"${CLUSTER_NAME}\")].cluster.server}")
    echo "  Endpoint: ${ENDPOINT}"

    # Set up the config
    echo -e "\\n* Preparing k8s-${SERVICE_ACCOUNT_NAME}-${SERVICE_ACCOUNT_NAMESPACE}-conf"
    echo -n "  Setting a cluster entry in kubeconfig..."
    kubectl config set-cluster "${CLUSTER_NAME}" \
    --kubeconfig="${KUBECFG_FILE_NAME}" \
    --server="${ENDPOINT}" \
    --certificate-authority="ca.crt" \
    --embed-certs=true

    echo -n "* Setting token credentials entry in kubeconfig..."
    kubectl config set-credentials \
    "${SERVICE_ACCOUNT_NAME}-${SERVICE_ACCOUNT_NAMESPACE}-${CLUSTER_NAME}" \
    --kubeconfig="${KUBECFG_FILE_NAME}" \
    --token="${USER_TOKEN}"

    echo -n "* Setting a context entry in kubeconfig..."
    kubectl config set-context \
    "${SERVICE_ACCOUNT_NAME}-${SERVICE_ACCOUNT_NAMESPACE}-${CLUSTER_NAME}" \
    --kubeconfig="${KUBECFG_FILE_NAME}" \
    --cluster="${CLUSTER_NAME}" \
    --user="${SERVICE_ACCOUNT_NAME}-${SERVICE_ACCOUNT_NAMESPACE}-${CLUSTER_NAME}" \
    --namespace="${SERVICE_ACCOUNT_NAMESPACE}"

    echo -n "* Setting the current-context in the kubeconfig file..."
    kubectl config use-context "${SERVICE_ACCOUNT_NAME}-${SERVICE_ACCOUNT_NAMESPACE}-${CLUSTER_NAME}" \
    --kubeconfig="${KUBECFG_FILE_NAME}"
}

create_service_account
get_secret_name_from_service_account
remove_sa_secret
sleep 5
get_secret_name_from_service_account
create_new_sa_token
extract_ca_crt_from_secret
get_user_token_from_secret
set_kube_config_values

rm ca.crt

echo -e "\\nDone!"

echo "1. you should not have any permissions by default. This should say 'no' (unless clusterrolebinding was created previously):"
echo "KUBECONFIG=${KUBECFG_FILE_NAME} kubectl get pods"
KUBECONFIG=${KUBECFG_FILE_NAME} kubectl auth can-i get pods


# (optionally) Create admin cluster role
# echo "2. Creating ${CLUSTER_ROLE} cluster role"
# kubectl apply -f - <<EOF
# apiVersion: rbac.authorization.k8s.io/v1
# kind: ClusterRole
# metadata:
#   name: ${CLUSTER_ROLE}
# rules:
# - apiGroups: ["*"]
#   resources: ["*"]
#   verbs: ["*"]
# EOF

# Creating cluster role binding for service account
echo "2. Creating ${SERVICE_ACCOUNT_NAMESPACE}:${SERVICE_ACCOUNT_NAME} SA cluster role binding to ${CLUSTER_ROLE} role"
kubectl create clusterrolebinding ${CLUSTER_ROLEBINDING_NAME} --clusterrole=${CLUSTER_ROLE} \
--namespace ${SERVICE_ACCOUNT_NAMESPACE} --serviceaccount=${SERVICE_ACCOUNT_NAMESPACE}:${SERVICE_ACCOUNT_NAME}

# Verifying
echo "3. you should be able to see pods now, this should say 'yes':"
KUBECONFIG=${KUBECFG_FILE_NAME} kubectl auth can-i get pods -n kube-system

# Kubeconfig instructions
echo "Use this kubeconfig file: '${KUBECFG_FILE_NAME}', store it somewhere safe and delete it when you're done."
