body {
  font-size: 14px;
  margin: 0;
  padding: 10px;
  font-weight: 500;
  font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,
    Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5715;
}

a {
  background-color: initial;
  color: #1890ff;
  cursor: pointer;
  outline: none;
  text-decoration: none;
  transition: color .3s;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

.menu {
  display: flex;
  list-style-type: none;
  justify-content: flex-end;
}

.menu li {
  margin: 0 10px;
  cursor: pointer;
}

.menu li span {
  color:#40a9ff;
}

.ant-table-wrapper .ant-table-thead > tr > th {
  font-weight: 500;
}

.ant-drawer-header {
  background-color:rgb(34,34,34);
  border-radius: 0;
}

.ant-drawer-title {
  color: rgb(214, 214, 214);
}

.ant-drawer .ant-drawer-body {
  background-color: rgb(34,34,34);
  color: rgb(214, 214, 214);
  padding:0 0 50px 0;
}

ul.compact-list {
  margin: 0;
  padding: 0;
}

ul.compact-list li {
  list-style-type:none;
}

span.link {
  color:#40a9ff;
  cursor: pointer;
}

p.text-warning {
  color: #f5222d;
}

.recommendations-list .resource {
  font-weight: bold;
}

.recommendations-list .recommendation ul {
  padding-left: 20px; 
}

.recommendations-list .recommendation li {
  list-style-type: none; 
}

.recommendations-list .recommendation pre {
  color: #5e5e5e;
  font-size: 0.9em;
  line-height: 1.3em;
  margin: 5px;
  padding: 0 10px;
  border-left: #ececec solid 1px;
}

.recommendations-list .recommendation span.info {
  display: block;
  font-style: italic;
  font-size: 0.8em;
  padding-left: 5px;
}

.release-notes {
  line-height: 1.3em;
}

.table-actions a {
  padding-right: 10px;
}

/* Custom scrollbar for log output */
.sync-log {
  /* Firefox */
  scrollbar-width: thick;
  scrollbar-color: #888 #f1f1f1;
}

/* Webkit browsers (Chrome, Safari, Edge) */
.sync-log::-webkit-scrollbar {
  width: 16px;
  height: 16px;
}

.sync-log::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 8px;
}

.sync-log::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 8px;
  border: 2px solid #f1f1f1;
}

.sync-log::-webkit-scrollbar-thumb:hover {
  background: #555;
}