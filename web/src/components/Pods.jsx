/* eslint no-console: "off" */
/* eslint no-duplicate-case: "off" */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Table, Drawer, Input, Button } from 'antd';
import Highlighter from 'react-highlight-words';
import { LazyLog, ScrollFollow } from 'react-lazylog';
import 'antd/es/table/style';
import { SearchOutlined } from '@ant-design/icons';
import { timeAgo } from '../utils/dates';

const Pods = props => {

  useEffect(() => {
    // Ensure props.cluster, props.namespace, and props.release are available before fetching
    if (props.cluster && props.namespace && props.release && props.backendURL) {
      fetchData();
    }
  }, [props.cluster, props.namespace, props.release, props.backendURL]);

  // const [Props] = useState(props); // Removed
  const [state, setState] = useState({loading: true, data: []});
  const [searchText, setSearchText] = useState('');
  const [drawerVisibility, setDrawerVisiblity] = useState(false);
  const [logUrl, setLogUrl] = useState();

  const showDrawer = (url) => {
    setLogUrl(url);
    setDrawerVisiblity(true);
  };

  const onDrawerClose = () => {
    setDrawerVisiblity(false);
  };

  const logFetchOptions = {
    credentials: 'include',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Access-Control-Allow-Credentials': true,
    },
  };

  const handleNotAuthenticated = () => {
    // Assuming backendURL is directly on props. If it was Props.backendURL.backendURL, this needs adjustment.
    // For now, assuming it's props.backendURL
    const backendUrlString = props.backendURL ? props.backendURL.toString() : '';
    setState({ authenticated: false });
    window.open(`${backendUrlString}/auth/logout`, '_self');
  };
  
  const getColumnSearchProps = dataIndex => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={(input) => {
            if (input != null) {
              input.focus();
            }
          }}
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys, confirm)}
          style={{ width: 188, marginBottom: 8, display: 'block' }}
        />
        <Button
          type="primary"
          onClick={() => handleSearch(selectedKeys, confirm)}
          icon={<SearchOutlined />}
          size="small"
          style={{ width: 90, marginRight: 8 }}
        />
        <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
          Reset
        </Button>
      </div>
    ),
    filterIcon: filtered => (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    ),
    onFilter: (value, record) =>
      record[dataIndex]
        .toString()
        .toLowerCase()
        .includes(value.toLowerCase()),
    render: text => (
      <Highlighter
        highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
        searchWords={[searchText]}
        autoEscape
        textToHighlight={text.toString()}
      />
    ),
  });

  const handleSearch = (selectedKeys, confirm) => {
    confirm();
    setSearchText(selectedKeys[0]);
  };

  const handleReset = clearFilters => {
    clearFilters();
    setSearchText('');
  };

  function ContainerState(container) {
    // const isLoggedIn = props.isLoggedIn;
    // if (isLoggedIn) {    return <UserGreeting />;  }  return <GuestGreeting />;}

    if (container.state && container.state.running) {
      return 'Running';
    }
    if (container.state && container.state.waiting) {
      return container.state.waiting.reason;
    }
    if (container.state && container.state.terminated) {
      return container.state.terminated.reason;
    }

    return '';
  }

  const fetchData = () => {
    
    setState({ loading: true });
    
    const cluster = props.cluster;
    const namespace = props.namespace;
    const release = props.release;

    // Fetch release pods
    fetch(`${props.backendURL}/api/release/${cluster}/${namespace}/${release}/pods`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status === 200) return response.json();
      if (response.status === 401) handleNotAuthenticated();
      return response.json();
    })
    .then(responseJson => {
      setState({
        loading: false,
        data: Object.values(responseJson),
      });
    })
    .catch(error => {
      console.log(error);
    });
  };

  const columns = [
    {
      title: 'Pod',
      dataIndex: 'name',
      defaultSortOrder: 'ascend',
      sorter: (a, b) => (a.name > b.name ? 1:-1),
      width: '20%',
      ...getColumnSearchProps('name'),
    },
    {
      title: 'Containers',
      dataIndex: 'containers',
      width: '20%',
      render: (containers, pod) => (
        <ul className="compact-list">
          {
            pod.resource.spec.initContainers?.map((container, index) => (
                <li key={`${container}-container`} className={`initcontainer container-${index}`}>
                  Init:{container.name}
                </li>
            ))
          }
          { 
            containers.map((container, index) => (
              <li key={`${container}-container`} className={`container container-${index}`}>
                {container}
              </li>
            ))
          }
        </ul>
      ),
    },
    {
      title: 'State',
      key: 'state',
      width: '10%',
      render: (containers, pod) => (
        <ul className="compact-list">
          { 
            pod.resource.status.initContainerStatuses?.map((container, index) => ( 
              <li key={`${container.name}-state`} className={`initcontainer container-${index}`}>
                Init:{ ContainerState(container) }
              </li> 
            ))
          }
          { pod.resource.status.containerStatuses?.map((container, index) => (
            <li key={`${container.name}-state`} className={`container container-${index}`}>
              { ContainerState(container) }
            </li>
            ))
          }
        </ul>
      ),
    },
    {
      title: 'Age',
      key: 'age',
      width: '10%',
      render: (containers, pod) => (
        <ul className="compact-list">
          { pod.resource.status.initContainerStatuses?.map((container, index) => (
              <li key={`${container.name}-age`} className={`initcontainer container-${index}`}>
                {container.state && container.state.running && container.state.running.startedAt ? timeAgo(container.state.running.startedAt) : container.ready }
              </li>
            ))
          }
          { pod.resource.status.containerStatuses?.map((container, index) => (
            <li key={`${container.name}-age`} className={`container container-${index}`}>
              {container.state && container.state.running && container.state.running.startedAt ? timeAgo(container.state.running.startedAt) : container.ready }
            </li>
            )) 
          }
        </ul>
      ),
    },
    {
      title: 'Resource requests',
      key: 'resource_requests',
      width: '15%',
      render: (containers, pod) => (
        <ul className="compact-list"> { 
          pod.resource.spec.containers.map((container, index) => (
            <li key={container.name} className={`container container-${index}`}>
              {container.resources.requests && container.resources.requests.cpu ? container.resources.requests.cpu: '∞'} CPU, {container.resources.requests && container.resources.requests.memory ? container.resources.requests.memory : '∞'} RAM
            </li>
            )) 
          }
        </ul>
      ),
    },
    {
      title: 'Resource limits',
      key: 'resource_limits',
      width: '15%',
      render: (containers, pod) => (
        <ul className="compact-list">
          { pod.resource.spec.containers.map((container, index) => (
              <li key={container.name} className={`container container-${index}`}>
                {container.resources.limits && container.resources.limits.cpu ? container.resources.limits.cpu: '∞'} CPU, {container.resources.limits && container.resources.limits.memory ? container.resources.limits.memory : '∞'} RAM
              </li>
            )) 
          }
        </ul>
      ),
    },
    {
      title: 'Operations',
      dataIndex: 'containers',
      width: '20%',
      render: (containers, pod) => (
        <ul className="compact-list">
          <span className="link" aria-hidden="true" onClick={() => showDrawer(`${props.backendURL}/api/events/${pod.cluster}/${pod.namespace}/${pod.name}`)} key={`events-${pod.name}`}>
            Pod events log
          </span>
          { pod.resource.status.initContainerStatuses?.map((container, index) => (
              <li key={container.name} className={`container container-${index}`}>
                <span className="link" aria-hidden="true" onClick={() => showDrawer(`${props.backendURL}/api/logs/${pod.cluster}/${pod.namespace}/${pod.name}/${container.name}`)} key={`log-${pod.name}-${container.name}`}>
                  Init:{container.name} logs
                </span>
              </li>
            ))
          }
          { containers.map((container, index) => (
              <li key={container} className={`container container-${index}`}>
                {/* <Link to={'/logs/' + pod.cluster + '/' + pod.namespace + '/' + pod.name + '/' + container}>{container} logs</Link> */}
                <span className="link" aria-hidden="true" onClick={() => showDrawer(`${props.backendURL}/api/logs/${pod.cluster}/${pod.namespace}/${pod.name}/${container}`)} key={`log-${pod.name}-${container}`}>
                  {container} logs
                </span>
              </li>
            ))
          }
          {/* <Link to={props.backendURL + '/api/pod/' + release.cluster.toString() + '/'+ release.namespace.toString() + '/' +  release.name.toString() + '/kill'}>Kill pod</Link> */}
        </ul>
      ),
    },
  ];

  return (
    <div>
      <Table
          rowKey={record => record.name}
          pagination={false}
          loading={state.loading}
          dataSource={state.data}
          columns={columns}
      />
      <Drawer
          height="500"
          placement="bottom"
          closable={false}
          onClose={onDrawerClose}
          open={drawerVisibility}
        >
          <div style={{ height: 450 }}>
            <ScrollFollow
              startFollowing
              // render={({ onScroll, follow, startFollowing, stopFollowing }) => (
              render={() => (
                <LazyLog fetchOptions={logFetchOptions} extraLines={1} enableSearch url={logUrl} selectableLines />
              )}
            />
          </div>
        </Drawer>
    </div>
  );
};

Pods.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
  }),
  // authenticated: PropTypes.bool,
  // backendURL: PropTypes.string,
};

export default Pods;
