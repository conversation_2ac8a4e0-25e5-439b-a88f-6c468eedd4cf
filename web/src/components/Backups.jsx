/* eslint no-console: "off" */
/* eslint no-duplicate-case: "off" */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Link, useParams } from 'react-router-dom';
import { DownOutlined, SearchOutlined } from '@ant-design/icons';
import { message, List, Spin, Table, Input, Button } from 'antd';
import 'antd/es/table/style';

const Backups = props => {

  useEffect(() => {
    fetchData();
  }, []);

  const [searchText, setSearchText] = useState('');
  const [state, setState] = useState({loading: true, schedule:"", backups: []});
 
  const { cluster, namespace, release } = useParams();
  
  const handleNotAuthenticated = () => {
    const { backendURL } = props;
    setState({ authenticated: false });
    window.open(`${backendURL}/auth/logout`, '_self');
  };

  const fetchData = () => {
    
    setState({ loading: true });

    // Fetch backup list from backend
    fetch(`${props.backendURL}/api/release/${cluster}/${namespace}/${release}/backups`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status === 200) return response.text();
      if (response.status === 401) handleNotAuthenticated();
      return response.json();
    })
    .then(responseBody => {
      if (responseBody === null) {
        setState({
          loading: false,
          schedule: {},
          backups: [],
        });
        return;
      }
      // try to parse response as json
      try {
        const responseJson = JSON.parse(responseBody);
        setState({
          loading: false,
          schedule: responseJson.schedule,
          backups: Object.values(responseJson.backups),
        });
        return;
      } catch (e) {
        setState({
          loading: false,
          schedule: {},
          backups: [],
        });
        return;
      }
    })
    .catch(error => {
      console.log(error);
      setState({
        loading: false,
        schedule: {},
        backups: [],
      });
    });
  };

  const startBackup = () => {
    fetch(`${props.backendURL}/api/release/${cluster}/${namespace}/${release}/backups/start`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status === 200) return response.text();
      if (response.status === 401) handleNotAuthenticated();
      return response.json();
    })
    .then(responseBody => {
      if (responseBody == null) {
        error;
      }

      // try to parse response as json
      let responseJson;
      try {
        responseJson = JSON.parse(responseBody);
      } catch (e) {
        error;
      }

      if (responseJson.status != "ok") {
        error;
      }

      message.info(`Backup started, job id: ${responseJson.job}`);
      
      // Reload backup list
      fetchData();
    })
    .catch(error => {
      message.error("Failed to start backup");
    });
  };
  
  const handleSearch = (selectedKeys, confirm) => {
    confirm();
    setSearchText(selectedKeys[0]);
  };

  const handleReset = clearFilters => {
    clearFilters();
    setSearchText('');
  };

  const getColumnSearchProps = dataIndex => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={(input) => {
            if (input != null) {
              input.focus();
            }
          }}
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys, confirm)}
          style={{ width: 188, marginBottom: 8, display: 'block' }}
        />
        <Button
          type="primary"
          onClick={() => handleSearch(selectedKeys, confirm)}
          icon={<SearchOutlined />}
          size="small"
          style={{ width: 90, marginRight: 8 }}
        />
        <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
          Reset
        </Button>
      </div>
    ),
    filterIcon: filtered => (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    ),
    onFilter: (value, record) => (
      record[dataIndex]
        .toString()
        .toLowerCase()
        .includes(value.toLowerCase())
    ),
  });

  const columns = [
    {
      title: 'Directory',
      dataIndex: 'Name',
      defaultSortOrder: 'descend',
      sorter: (a, b) => (a.Name > b.Name ? 1:-1),
      width: '30%',
      ...getColumnSearchProps('Name'),
      render: (text, record) => (
        <>
        { record.Name }
        </>
      ),
    },
    {
      title: 'Objects',
      dataIndex: 'Content',
      render: (text, backup) => (
        <ul className="compact-list">
          { backup.Content && backup.Content.map((item, index) => (
              <li key={item.Name} className={`backup-object backup-object-${index}`}>
                <a href={`${props.backendURL}/api/backups/${cluster}/${namespace}/${release}/${backup.Name}/${item.Name}`}> 
                  { item.Name }
                </a>
              </li>
            )) 
          }
        </ul>
      ),
    },
    {
      title: 'Size',
      dataIndex: 'Size',
      width: '15%',
      render: (text, backup) => (
        <ul className="compact-list">
          { backup.Content && backup.Content.map((item, index) => (
              <li key={item.Name} className={`backup-size backup-size-${index}`}>
                { formatBytes(item.Size) }
              </li>
            )) 
          }
        </ul>
      ),
    },
  ];
 
  return (
    <div>
      Backups are automatically taken using the cron configuration: { state.schedule || !state.loading || <Spin/> }<br />
      You can edit this value in your silta.yml configuration file.
      {
        state.schedule != "" && state.schedule?.includes('~') && (
          <div>
            Note: "~" gets replaced with a random digit between 0 and 9 to prevent having all cron jobs at once.
          </div>
        )
      }
      <br />
      <div className="table-actions">
        <Link onClick={event => {event.preventDefault(); fetchData(); }} to="javascript:;">
          Reload
        </Link>
        <Link onClick={event => {event.preventDefault(); startBackup(); }} to="javascript:;">
          Back up now
        </Link>
      </div>
      <Table
        rowKey={backup => backup.Name}
        pagination={false}
        loading={state.loading}
        dataSource={state.backups}
        columns={columns}
      />
    </div>
  );
};

Backups.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
  }),
  backendURL: PropTypes.string,
};

// Source: https://web.archive.org/web/20120507054320/http://codeaid.net/javascript/convert-size-in-bytes-to-human-readable-format-(javascript)
function formatBytes(bytes, decimals = 2) {
  if (!+bytes) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
};

export default Backups;
