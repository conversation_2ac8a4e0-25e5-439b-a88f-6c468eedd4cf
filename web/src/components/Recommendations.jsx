/* eslint no-console: "off" */
/* eslint no-duplicate-case: "off" */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { List } from 'antd';
import 'antd/es/table/style';

const Recommendations = props => {

  useEffect(() => {
    if (props.cluster && props.namespace && props.release && props.backendURL) {
      fetchData();
    }
  }, [props.cluster, props.namespace, props.release, props.backendURL]);

  // const [Props] = useState(props); // Removed
  const [state, setState] = useState({loading: true, c: false, recommendations: []});
  
  const handleNotAuthenticated = () => {
    const backendUrlString = props.backendURL ? props.backendURL.toString() : '';
    setState({ authenticated: false });
    window.open(`${backendUrlString}/auth/logout`, '_self');
  };

  const fetchData = () => {
    
    setState({ loading: true });
    
    const cluster = props.cluster;
    const namespace = props.namespace;
    const release = props.release;

    // Fetch release pods
    fetch(`${props.backendURL}/api/release/${cluster}/${namespace}/${release}/recommendations`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status === 200) return response.text();
      if (response.status === 401) handleNotAuthenticated();
      return response.json();
    })
    .then(responseBody => {
      if (responseBody === null) {
        setState({
          loading: false,
          recommendations: [],
          c: false,
        });
        return;
      }
      // try to parse response as json
      try {
        const responseJson = JSON.parse(responseBody);
        setState({
          recommendations: Object.values(responseJson.recommendations),
          loading: false,
          c: responseJson.c,
        });
        return;
      } catch (e) {
        setState({
          loading: false,
          recommendations: [],
          c: false,
        });
        return;
      }
    })
    .catch(error => {
      console.log(error);
    });
  };

 
  return (
    <div>
      <p><em><strong>Beta note:</strong> The recommendations might not be completely accurate, so use common sense ;)</em></p>
      Recommended resource request data is based on maximum observed usage over the past 7 days. 
      If the site has not been visited or under a heavy load, the adjusted configuration may be too 
      conservative and result in a performance degradation.<br/>
      { state.c == true ? (
          <div className="info">* Estimated costs are approximate and don't include any discounts or related costs such as networking, storage, shared services, and others. Suggestions are based on <a href="https://www.kubecost.com/">Kubecost</a> pricing model and may differ from actual costs.</div>
        ) : null
      }
      <List
        className="recommendations-list"
        loading={state.loading}
        itemLayout="vertical"
        dataSource={state.recommendations}
        renderItem={(suggestion) => (
          <List.Item className="recommendation">
            <div className="resource">{suggestion.Resource} / {suggestion.Container}</div>
            <ul>
              <li>
                Current request: cpu:{suggestion.CurrentRequest.cpu}, memory:{suggestion.CurrentRequest.memory}
              </li>
              <li>
                Recommended request: cpu:{suggestion.RecommendedRequest.cpu}, memory:{suggestion.RecommendedRequest.memory}
              </li>
              { state.c == true ? (
                <li>
                  Estimated monthly savings: cpu:{Number(suggestion.EstimatedMonthlySavings.cpu).toFixed(2)}€, memory:{Number(suggestion.EstimatedMonthlySavings.memory).toFixed(2)}€, total:{(Number(suggestion.EstimatedMonthlySavings.cpu) + Number(suggestion.EstimatedMonthlySavings.memory)).toFixed(2)}€
                {(suggestion.EstimatedMonthlySavings.cpu < 0 || suggestion.EstimatedMonthlySavings.memory < 0) && (
                  <div className="info">* negative estimate means current assignment is smaller than the actual maximum (spiked) resource usage.</div>
                )}
                </li>
                ) : null
              }
              { suggestion.Snippet != '' && (
              <li>
                Adjusted configuration:
                <pre>
                  <code>{suggestion.Snippet}</code>
                </pre>
              </li>
              )}
            </ul>
          </List.Item>
        )}
      />
    </div>
  );
};

Recommendations.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
  }),
};

export default Recommendations;
