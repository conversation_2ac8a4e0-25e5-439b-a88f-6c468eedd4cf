/* eslint no-console: "off" */
/* eslint no-duplicate-case: "off" */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Spin } from 'antd';
import 'antd/es/table/style';

const Linkify = ({children})=> {
  const isUrl = word => {
      const urlPattern = /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/gm;
      return word.match(urlPattern);
  };

  if (!children) return null;

  // replace newlines with <br />
  children = children.replace(/(?:\r\n|\r|\n)/g, ' <br /> ');
  
  // split on spaces, newlines, and tabs
  const words = children.split(' ');
  const formatedWords = words.map((w, i) => isUrl(w) ? `<a href="${w}" target="_blank">${w}</a>`: w);
  let html = formatedWords.join(' ');

  // replace tabs with whitespaces
  html = html.replace(/  /g, '&nbsp;&nbsp;');

  return (<span dangerouslySetInnerHTML={{__html: html}} />);
};

const ReleaseNotes = props => {

  useEffect(() => {
    if (props.cluster && props.namespace && props.release && props.backendURL) {
      fetchData();
    }
  }, [props.cluster, props.namespace, props.release, props.backendURL]);

  // const [Props] = useState(props); // Removed
  const [state, setState] = useState({loading: true, data: []});
  
  const handleNotAuthenticated = () => {
    const backendUrlString = props.backendURL ? props.backendURL.toString() : '';
    setState({ authenticated: false });
    window.open(`${backendUrlString}/auth/logout`, '_self');
  };

  const fetchData = () => {
    
    setState({ loading: true });
    
    const cluster = props.cluster;
    const namespace = props.namespace;
    const release = props.release;

    // Fetch release pods
    fetch(`${props.backendURL}/api/release/${cluster}/${namespace}/${release}/notes`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status === 200) return response.text();
      if (response.status === 401) handleNotAuthenticated();
      return "";
    })
    .then(responseText => {
      if (responseText === null) {
        setState({
          loading: false,
          data: [],
        });
        return;
      }
      setState({
        loading: false,
        data: responseText,
      });
    })
    .catch(error => {
      console.log(error);
    });
  };

 
  return (
    <div className="release-notes">
      { state.loading ? <Spin /> : null }
    { state.data == "" ? "No release notes available" : 
    <Linkify>
      {state.data}
    </Linkify>
    }
    </div>
  );
};

ReleaseNotes.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
  }),
};

export default ReleaseNotes;
