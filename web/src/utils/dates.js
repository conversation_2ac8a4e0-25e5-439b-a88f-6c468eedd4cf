/**
 * 
 * @param {string} current 
 * @param {string} previous 
 */
function timeDifference(current, previous) {

    const msPerMinute = 60 * 1000;
    const msPerHour = msPerMinute * 60;
    const msPerDay = msPerHour * 24;
    const msPerMonth = msPerDay * 30;
    const msPerYear = msPerDay * 365;

    const elapsed = current - previous;

    if (elapsed < msPerMinute) {
         return `${Math.round(elapsed/1000)}s`;   
    }

    if (elapsed < msPerHour) {
         return `${Math.round(elapsed/msPerMinute)}min`;   
    }

    if (elapsed < msPerDay ) {
         return `${Math.round(elapsed/msPerHour)}h`;   
    }

    if (elapsed < msPerMonth) {
        return `~${Math.round(elapsed/msPerDay)}d`;   
    }

    if (elapsed < msPerYear) {
        return `~${Math.round(elapsed/msPerMonth)}months`;   
    }

    return `~${Math.round(elapsed/msPerYear )}y`;
}

/**
 * 
 * @param {string} datetime Ex: 2020-10-05T23:45:46.000Z
 */
export const timeAgo = (datetime) => {
    const now = new Date();
    const was = new Date(datetime);
    return timeDifference(now.getTime(), was.getTime());
};
