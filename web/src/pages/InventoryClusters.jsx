import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { Table, Input, Button, Space, Tag } from 'antd';
import { SearchOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';

const InventoryClusters = (Props) => {
  const [state, setState] = useState({
    loading: false,
    data: [],
  });

  const [searchText, setSearchText] = useState('');
  const [searchedColumn, setSearchedColumn] = useState('');
  const searchInput = useRef(null);

  useEffect(() => {
    fetchData();
  }, []);

  const handleNotAuthenticated = () => {
    window.location.href = '/';
  };

  const fetchData = () => {
    setState({ loading: true, data: [] });

    fetch(`${Props.backendURL}/api/inventory/clusters`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status === 200) return response.json();
      if (response.status === 401) handleNotAuthenticated();
      if (response.status === 403) {
        console.log('Access forbidden - admin role required');
        return [];
      }
      return response.json();
    })
    .then(responseJson => {
      setState({
        loading: false,
        data: responseJson || [],
      });
    })
    .catch(error => {
      console.log(error);
      setState({
        loading: false,
        data: [],
      });
    });
  };

  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters) => {
    clearFilters();
    setSearchText('');
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={searchInput}
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            Search
          </Button>
          <Button
            onClick={() => clearFilters && handleReset(clearFilters)}
            size="small"
            style={{ width: 90 }}
          >
            Reset
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    ),
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
        : '',
    filterDropdownProps: {
      onOpenChange: (visible) => {
        if (visible) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    render: (text) =>
      searchedColumn === dataIndex ? (
        <Highlighter
          highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
          searchWords={[searchText]}
          autoEscape
          textToHighlight={text ? text.toString() : ''}
        />
      ) : (
        text
      ),
  });

  const columns = [
    {
      title: 'Enabled',
      dataIndex: 'enabled',
      key: 'enabled',
      width: '10%',
      sorter: (a, b) => (a.enabled === b.enabled ? 0 : a.enabled ? -1 : 1),
      render: (enabled) => (
        enabled ? (
          <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '18px' }} />
        ) : (
          <CloseCircleOutlined style={{ color: '#ff4d4f', fontSize: '18px' }} />
        )
      ),
    },
    {
      title: 'Cluster Name',
      dataIndex: 'name',
      key: 'name',
      defaultSortOrder: 'ascend',
      sorter: (a, b) => (a.name > b.name ? 1 : -1),
      width: '25%',
      ...getColumnSearchProps('name'),
      render: (name, record) => (
        <span>
          <Tag color={record.labelColor || 'default'}>
            {name.toUpperCase()}
          </Tag>
        </span>
      ),
    },
    {
      title: 'Address',
      dataIndex: 'server',
      key: 'server',
      sorter: (a, b) => (a.server > b.server ? 1 : -1),
      width: '30%',
    },
    {
      title: 'Version',
      dataIndex: 'kubernetesVersion',
      key: 'kubernetesVersion',
      sorter: (a, b) => {
        const versionA = a.kubernetesVersion || '';
        const versionB = b.kubernetesVersion || '';
        return versionA > versionB ? 1 : -1;
      },
      width: '15%',
      render: (version) => version || '-',
    },
    {
      title: 'Silta cluster',
      dataIndex: 'siltaClusterVersion',
      key: 'siltaClusterVersion',
      sorter: (a, b) => {
        const versionA = a.siltaClusterVersion || '';
        const versionB = b.siltaClusterVersion || '';
        return versionA > versionB ? 1 : -1;
      },
      width: '20%',
      render: (version, record) => {
        if (!version) return '-';
        if (!record.siltaClusterDeployed) return version;
        const date = new Date(record.siltaClusterDeployed);
        const formattedDate = date.toLocaleString();
        return `${version} (${formattedDate})`;
      },
    },
    {
      title: 'Inventory update',
      dataIndex: 'updated',
      key: 'updated',
      sorter: (a, b) => {
        const dateA = a.updated || '';
        const dateB = b.updated || '';
        return dateA > dateB ? 1 : -1;
      },
      width: '15%',
      render: (updated) => {
        if (!updated) return '-';
        const date = new Date(updated);
        return date.toLocaleString();
      },
    },
  ];

  return (
    <div>
      <h1>Cluster Inventory</h1>
      <Table
        rowKey={(record) => record.name}
        pagination={false}
        loading={state.loading}
        dataSource={state.data}
        columns={columns}
      />
    </div>
  );
};

InventoryClusters.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
  }),
  backendURL: PropTypes.string,
};

export default InventoryClusters;

