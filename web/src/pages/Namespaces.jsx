/* eslint no-console: "off" */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { Table, Tag, Input, Button } from 'antd';
import Highlighter from 'react-highlight-words';
import 'antd/es/table/style';
import { SearchOutlined } from '@ant-design/icons';

const Namespaces = props => {

  useEffect(() => {
    fetchData();
  }, [props]);

  const [Props] = useState(props);
  const [state, setState] = useState({loading: true, data: []});
  const [searchText, setSearchText] = useState('');

  const handleNotAuthenticated = () => {
    const { backendURL } = Props.backendURL;
    setState({ authenticated: false });
    window.open(`${backendURL}/auth/logout`, '_self');
  };
  
  const getColumnSearchProps = dataIndex => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={(input) => {
            if (input != null) {
              input.focus();
            }
          }}
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys, confirm)}
          style={{ width: 188, marginBottom: 8, display: 'block' }}
        />
        <Button
          type="primary"
          onClick={() => handleSearch(selectedKeys, confirm)}
          icon={<SearchOutlined />}
          size="small"
          style={{ width: 90, marginRight: 8 }}
        />
        <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
          Reset
        </Button>
      </div>
    ),
    filterIcon: filtered => (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    ),
    onFilter: (value, record) =>
      record[dataIndex]
        .toString()
        .toLowerCase()
        .includes(value.toLowerCase()),
    render: text => (
      <Link to={`/namespaces/${text.toString()}`}><Highlighter
        highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
        searchWords={[searchText]}
        autoEscape
        textToHighlight={text.toString()}
      /></Link>
    ),
  });

  const handleSearch = (selectedKeys, confirm) => {
    confirm();
    setSearchText(selectedKeys[0]);
  };

  const handleReset = clearFilters => {
    clearFilters();
    setSearchText('');
  };

  const fetchData = () => {
    
    setState({ loading: true });

    fetch(`${Props.backendURL}/api/namespaces`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status === 200) return response.json();
      if (response.status === 401) handleNotAuthenticated();
      return response.json();
    })
    .then(responseJson => {
      setState({
        loading: false,
        data: responseJson,
      });
    })
    .catch(error => {
      console.log(error);
    });
  };

  const columns = [
    {
      title: 'Namespace',
      dataIndex: 'name',
      defaultSortOrder: 'ascend',
      sorter: (a, b) => (a.name > b.name ? 1:-1),
      width: '70%',
      ...getColumnSearchProps('name'),
    },
    {
      title: 'Clusters',
      dataIndex: 'clusters',
      render: (clusters) => (
        <span>
          {clusters.map(cluster => {
            let color = '#a85032';
            if (cluster === 'local') color = 'orange';
            if (cluster === 'silta_finland') color = 'green';
            if (cluster === 'silta_finland_production_a') color = 'purple';
            if (cluster === 'silta_dev') color = 'geekblue';
            if (cluster === 'silta_test') color = 'volcano';
            if (cluster === 'hus_dev') color = '#004b87';
            if (cluster === 'hus_stage') color = '#004b87';
            if (cluster === 'hus_prod') color = '#004b87';
            if (cluster === 'vaisala_dev') color = 'darkcyan';
            if (cluster === 'vaisala_prod') color = 'darkorchid';
            return (
              <Tag color={color} key={cluster}>
              {cluster.toUpperCase()}
            </Tag>
            );
          })}
        </span>),
      width: '30%',
    },
  ];

  return (
    <div>
      <h1>Namespaces</h1> 
      <Table
        rowKey={record => record.name}
        pagination={false}
        loading={state.loading}
        dataSource={state.data}
        columns={columns}
      />
    </div>
  );
};

Namespaces.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
  }),
  // authenticated: PropTypes.bool,
  // backendURL: PropTypes.string
};

export default Namespaces;
