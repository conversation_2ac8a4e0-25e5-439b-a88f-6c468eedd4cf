/* eslint no-console: "off" */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Link, useParams } from 'react-router-dom';
import Highlighter from 'react-highlight-words';

import { ExclamationCircleFilled, DownOutlined, SearchOutlined } from '@ant-design/icons';
import { Tooltip, Modal, Space, Dropdown, message, Table, Tag, Input, Popconfirm, Button, Checkbox } from 'antd';
import 'antd/es/table/style';

const { confirm } = Modal;

const Releases = props => {

  const { namespace } = useParams();

  useEffect(() => {
    fetchData();
  }, [namespace, props.backendURL]);

  // const [Props] = useState(props); // This line is problematic as Props will become stale. Direct props usage is better.
  const [state, setState] = useState({loading: true, data: []});
  const [searchText, setSearchText] = useState('');
  const [syncModal, setSyncModal] = useState({ visible: false, selectedForm: 'syncOptionsForm', selectedRelease: '', data: []});

  const handleNotAuthenticated = () => {
    // Assuming backendURL is directly on props, not nested. If it's props.backendURL.backendURL, adjust accordingly.
    const backendURL = props.backendURL;
    setState({ authenticated: false });
    window.open(`${backendURL}/auth/logout`, '_self');
  };
  
  const getColumnSearchProps = dataIndex => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={(input) => {
            if (input != null) {
              input.focus();
            }
          }}
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys, confirm)}
          style={{ width: 188, marginBottom: 8, display: 'block' }}
        />
        <Button
          type="primary"
          onClick={() => handleSearch(selectedKeys, confirm)}
          icon={<SearchOutlined />}
          size="small"
          style={{ width: 90, marginRight: 8 }}
        />
        <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
          Reset
        </Button>
      </div>
    ),
    filterIcon: filtered => (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    ),
    onFilter: (value, record) => (
      record[dataIndex]
        .toString()
        .toLowerCase()
        .includes(value.toLowerCase())
    ),
    render: (text, release) => (
      <Link to={`/release/${release.cluster.toString()}/${release.namespace.toString()}/${release.release.toString()}`}><Highlighter
        highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
        searchWords={[searchText]}
        autoEscape
        textToHighlight={text.toString()}
      /></Link>
    ),
  });

  const handleSearch = (selectedKeys, confirm) => {
    confirm();
    setSearchText(selectedKeys[0]);
  };

  const handleReset = clearFilters => {
    clearFilters();
    setSearchText('');
  };

  const fetchData = () => {
    
    setState({ loading: true });
    
    const requestUrl = `${props.backendURL.toString()}/api/namespace/${namespace}/releases`;
    
    fetch(requestUrl, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status === 200) return response.json();
      if (response.status === 401) handleNotAuthenticated();
      return response.json();
    })
    .then(responseJson => {
      setState({
        loading: false,
        data: Object.values(responseJson),
      });
    })
    .catch(error => {
      console.log(error);
      handleNotAuthenticated();
    });
  };

  const operationsItems = [
    {
      label: 'View',
      key: 'view',
    },
    {
      label: 'Synchronize data',
      key: 'sync',
    },
    {
      label: 'Uninstall',
      key: 'uninstall',
      danger: true,
    },
  ];

  const handleOperationsItemsClick = (event, record) => {
    // "view" event redirects to the release page
    if (event == 'view') {
      // Open view page
      window.location = `/release/${record.cluster}/${record.namespace}/${record.release}`;
    }
    // "uninstall" event shows a confirmation message
    if (event == 'sync') {
      showSyncOptionsModal(record);      
    }
    // "uninstall" event shows a confirmation message
    if (event == 'uninstall') {
      uninstallConfirmation(record);
    }
  };

  const uninstallConfirmation = (record) => {
  
    // Restricted releases list
    const restrictedReleases = ['production', 'prod', 'master', 'main', 'staging', 'stage'];

    // if restrictedReleases is record.release or starts with "record.release-", disable release removal
    for (let i = 0; i < restrictedReleases.length; i++) {
      let restrictedRelease = "".concat(restrictedReleases[i], "-");
      if (record.release.startsWith(restrictedRelease) || record.release == restrictedReleases[i]) {
        message.error('This release is protected. Ask system administrator to uninstall it.');
        return;
      }
    }
    
    confirm({
      title: 'Do you want to uninstall release?',
      icon: <ExclamationCircleFilled />,
      content: (
        <>
          <p>Do You really want to uninstall release <b>{record.release}</b> from namespace <b>{record.namespace}</b> in cluster <b>{record.cluster}</b>?</p>
          <p className="text-warning">This is irreversible!</p>
        </>
      ),
      okText: 'Yes, uninstall it',
      onOk() {
        message.info('Deleting release (this may take a while)');

        // Replace table with loader
        setState({ loading: true });
  
        // Create POST request to endpoint
        const removeReleasesRequestUrl = `${props.backendURL.toString()}/api/release/uninstall`;
        fetch(removeReleasesRequestUrl, {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Access-Control-Allow-Credentials': true,
          },
          body: `cluster=${record.cluster}&namespace=${record.namespace}&release=${record.release}`,
        })
        .then(response => {
          if (response.status == 200) {
            message.success('Release uninstalled');    
            // Reload release list
            fetchData();
          } 
          else {
            message.error('Error deleting release');
            fetchData();
          }
        })
        .catch(error => { 
          console.log(error); 
          message.error('Error deleting release');  
          fetchData();
        });
      },
    });
  };

  const columns = [
    {
      title: 'Release',
      dataIndex: 'release',
      defaultSortOrder: 'ascend',
      sorter: (a, b) => (a.release > b.release ? 1:-1),
      width: '55%',
      ...getColumnSearchProps('release'),
    },
    {
      title: 'Cluster',
      dataIndex: 'cluster',
      sorter: (a, b) => (a.cluster > b.cluster ? 1:-1),
      render: (cluster) => {
        let color = '#a85032';
        if (cluster === 'local') color = 'orange';
        if (cluster === 'silta_finland') color = 'green';
        if (cluster === 'silta_finland_production_a') color = 'purple';
        if (cluster === 'silta_dev') color = 'geekblue';
        if (cluster === 'silta_test') color = 'volcano';
        if (cluster === 'hus-dev') color = '#004b87';
        if (cluster === 'hus-stage') color = '#004b87';
        if (cluster === 'hus-prod') color = '#004b87';
        if (cluster === 'vaisala_dev') color = 'darkcyan';
        if (cluster === 'vaisala_prod') color = 'darkorchid';
        return (
          <span><Tag color={color} key={cluster}>
            {cluster.toUpperCase()}
          </Tag></span>
        );
      },
      width: '15%',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      defaultSortOrder: 'ascend',
      sorter: (a, b) => (a.status > b.status ? 1:-1),
      width: '15%',
      render: (text, record) => (
        <>
        { record.status == 'unknown' 
          ? <Tooltip placement="topLeft" title="Release listing timed out, release status unknown">-</Tooltip> 
          : record.status }
        </>
      ),
    },
    {
      title: 'Operations',
      dataIndex: 'containers',
      width: '15%',
      render: (text, record) => (
        <Space>
        <a onClick={() => handleOperationsItemsClick('view', record)}>
          View
        </a> 
        <Dropdown
          menu={{
            onClick: (e) => handleOperationsItemsClick(e.key, record),
            items: operationsItems,
          }}
          onClick={(e) => e.preventDefault()}
          trigger={['click']}
        >
          <DownOutlined />
        </Dropdown>
        </Space>
      ),
    },
  ];

  const releaseID = (cluster, namespace, release) => `${cluster}/${namespace}/${release}`;

  const showSyncOptionsModal = (release) => {

    const syncReleasesSyncStatusUrl = `${props.backendURL.toString()}/api/release/${release.cluster}/${release.namespace}/${release.release}/sync/status`;
    
    // if release sync is in progress, print status and return
    fetch(syncReleasesSyncStatusUrl, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Access-Control-Allow-Credentials': true,
      },
    })

    .then(response => {
      if (response.status === 200) return response.json();
      if (response.status === 401) handleNotAuthenticated();
      return false;
    })

    .then(responseJson => {
      // if release sync is in progress, print status and return
      // Store responseJson to syncModal.data
      if (responseJson.status != '') {
        setSyncModal({ visible: true, selectedForm: 'syncStatusForm', selectedRelease: release, data: responseJson });
      }
      else {
        setSyncModal({ visible: true, selectedForm: 'syncOptionsForm', selectedRelease: release, data: responseJson });
      }
    })
    .catch(error => {
      console.log(error);
    });
  };

  const hideSyncOptionsModal = () => {
    setSyncModal({ visible: false });
  };

  const startSync = (release) => {
    // Get selected release
    const sourceRelease = document.getElementById('sourceRelease').value;
    // Get extra sync options.
    const syncDatabase = document.getElementById('syncDatabase').checked;
    const syncFiles = document.getElementById('syncFiles').checked;
    // Create POST request to endpoint
    const syncReleasesRequestUrl = `${props.backendURL.toString()}/api/release/${release.cluster}/${release.namespace}/${release.release}/sync/start`;
    if (!(syncDatabase || syncFiles)) {
      message.error(`Please select at least one type of data to sync!`, 5);
      return;
    }
    fetch(syncReleasesRequestUrl, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Access-Control-Allow-Credentials': true,
      },
      body: `sourceRelease=${sourceRelease}&syncDatabase=${syncDatabase}&syncFiles=${syncFiles}`,
    })
    
    .then(response => {
      if (response.status == 200) {
        message.success('Release synchronization started');
        showSyncOptionsModal(release);
      } 
      else {
        message.error('Error syncing releases');
      }
    })
    .catch(error => { 
      console.log(error); 
      message.error('Error syncing releases');  
    });
  };

  const cancelSync = (release) => {
    // Create POST request to endpoint
    const syncReleasesRequestUrl = `${props.backendURL.toString()}/api/release/${release.cluster}/${release.namespace}/${release.release}/sync/cancel`;
    fetch(syncReleasesRequestUrl, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status == 200) {
        message.success('Release synchronization canceled');        
        setSyncModal({ visible: false});
      }
      else {
        message.error('Error cancelling release sync');
      }
    })
    .catch(error => {
      console.log(error);
      message.error('Error cancelling release sync');
    });
  };

  const acknowledgeSyncStatus = (release) => {
    // Create POST request to endpoint
    const syncReleasesRequestUrl = `${props.backendURL.toString()}/api/release/${release.cluster}/${release.namespace}/${release.release}/sync/ack`;
    fetch(syncReleasesRequestUrl, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status == 200) {
        message.success('Release synchronization status acknowledged');
        setSyncModal({ visible: false});
      }
      else {
        message.error('Error acknowledging release sync');
      }
    })
    .catch(error => {
      console.log(error);
      message.error('Error acknowledging release sync status');
    });
  };
  
  const syncOptionsForm = (release) => (
    state.data === undefined ? null :
    <div>
      <p>Release synchronization will copy data from source release and replace all data on the destination. This operation is irreversible!</p>
      <p>Note: This process runs referenceData commands, so data will be shuffled according to gdprDump configuration of source deployment.</p>
      <p>Select source release</p>
      <select id="sourceRelease" className="form-control">
      {
        state.data.map((sourceRelease, sourceIndex) => (
          // skip current release
          sourceRelease.release == release.release ? null :
          // add release to dropdown
          <option key={sourceIndex} value={releaseID(sourceRelease.cluster, sourceRelease.namespace, sourceRelease.release)}>{sourceRelease.cluster} / {sourceRelease.release}</option>
        ))
      }
      </select>
      <p>Destination release</p>
      <select id="targetRelease" className="form-control">
        <option value={releaseID(release.cluster, release.namespace, release.release)}>{release.cluster} / {release.release}</option>
      </select>
      <div>
        <p>Types of data to sync</p>
        <Checkbox id="syncDatabase" defaultChecked>Database</Checkbox>
        <Checkbox id="syncFiles" defaultChecked>Files</Checkbox>
      </div>
    </div>
  );

  const syncStatusForm = (release) => (
    <div>
      <p>Status: { syncModal.data ? syncModal.data.status : "unavailable"}</p>
      { syncModal.data && syncModal.data.pid ? <p>Sync process ID: {syncModal.data.pid}</p> : null }
      <pre
        className="sync-log"
        style={{
          width: "100%",
          marginRight: 8,
          whiteSpace: "pre-wrap",
          overflow: "auto",
          borderRadius: "4px"
        }}>
        { syncModal.data? syncModal.data.output : null}
      </pre>
    </div>
  );

  const syncOptionsFormFooter = (release) => [
    <Popconfirm
      title="Final confirmation"
      description="Are you sure?"
      onConfirm={() => startSync(release)}
      okText="Yes"
      cancelText="No"
      key="sync"
    >
      <Button key="sync" danger>
        Sync
      </Button>
    </Popconfirm>,
      
    <Button key="back" type="primary" onClick={() => hideSyncOptionsModal()}>
      Cancel
    </Button>,
  ];

  const syncStatusFormFooter = (release) => [
    // If sync is in progress, show "stop sync" button, otherwise show "acknowledge" button
    syncModal.data && syncModal.data.status == 'running' || syncModal.data && syncModal.data.status == 'pending' ?
      <Popconfirm
        title="Are you sure you want to cancel synchronization?"
        onConfirm={() => cancelSync(release)}
        okText="Yes"
        cancelText="No"
        key="stop"
      >
        <Button key="stop" danger>
          Stop
        </Button>
      </Popconfirm>
      :
      <Button key="acknowledge" type="primary" onClick={() => acknowledgeSyncStatus(release)}>
        Acknowledge
      </Button>,
    <Button key="refresh" type="primary" onClick={() => showSyncOptionsModal(release)}>
      Refresh
    </Button>,
    <Button key="back" type="primary" onClick={() => hideSyncOptionsModal()}>
      Close
    </Button>,
  ];

  return (
    <div>
      <h1>Releases in {namespace}</h1>
      <p className="breadcrumb">
        <Link to="/namespaces">
          Namespaces
        </Link>
        &nbsp;&gt;&nbsp; 
        {namespace}
      </p>
      <Modal 
        title="Release synchronization" 
        open={syncModal.visible}
        onCancel={() => hideSyncOptionsModal()}
        onOk={() => hideSyncOptionsModal()}
        footer={syncModal.selectedForm === 'syncOptionsForm' ? syncOptionsFormFooter(syncModal.selectedRelease) : syncStatusFormFooter(syncModal.selectedRelease)}
      >
        {syncModal.selectedForm === 'syncOptionsForm' ? syncOptionsForm(syncModal.selectedRelease) : syncStatusForm(syncModal.selectedRelease)}
      </Modal>
      <Table
        rowKey={record => record.key}
        pagination={false}
        loading={state.loading}
        dataSource={state.data}
        columns={columns}
      />
    </div>
  );
};

Releases.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
  }),
  // authenticated: PropTypes.bool,
  // backendURL: PropTypes.string,
};

export default Releases;
