import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Tabs } from 'antd';
import { Link, useParams } from 'react-router-dom';
import Pods from '../components/Pods';
import ReleaseNotes from '../components/ReleaseNotes';
import Recommendations from '../components/Recommendations';
import Backups from '../components/Backups';
import 'antd/es/table/style';

const Release = props => {
  const { cluster, namespace, release } = useParams();
  const [releaseInfo, setReleaseInfo] = useState({ loading: true, release: {} });

  useEffect(() => {
    fetchData();
  }, []);

  const tabItems = [
    {
      label: 'Pods',
      key: 'pods',
      children: (
        <Pods
          {...props}
          authenticated={props.authenticated}
          user={props.user}
          backendURL={props.backendURL}
          cluster={cluster}
          namespace={namespace}
          release={release}
        />
      ),
    },
    {
      label: 'Notes',
      key: 'releasenotes',
      children: (
        <ReleaseNotes
          {...props}
          authenticated={props.authenticated}
          user={props.user}
          backendURL={props.backendURL}
          cluster={cluster}
          namespace={namespace}
          release={release}
        />
      ),
    },
    props.user.githubOrgScope === 'internal' && {
      label: 'Recommendations (beta)',
      key: 'recommendations',
      children: (
        <Recommendations
          {...props}
          authenticated={props.authenticated}
          user={props.user}
          backendURL={props.backendURL}
          cluster={cluster}
          namespace={namespace}
          release={release}
        />
      ),
    },
    !releaseInfo.loading && releaseInfo.release && releaseInfo.release.values?.backup?.enabled && {
      label: 'Backups',
      key: 'backups',
      children: (
        <Backups
          {...props}
          authenticated={props.authenticated}
          user={props.user}
          backendURL={props.backendURL}
        />
      ),
    },
  ].filter(Boolean);

  const fetchData = () => {
    setReleaseInfo({ loading: true, release: {} });

    const requestUrl = `${props.backendURL.toString()}/api/release/${cluster.toString()}/${namespace.toString()}/${release.toString()}`;

    fetch(requestUrl, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status === 200) return response.json();
      return response.json();
    })
    .then(responseJson => {
      setReleaseInfo({
        loading: false,
        release: Object(responseJson),
      });
    })
    .catch(error => {
      console.log(error);
      setReleaseInfo({
        loading: false,
        release: {},
      });
    });
  };

  return (
    <div>
      <h1>Release {release}</h1>
      <p className="breadcrumb">
        <Link to="/namespaces">
          Namespaces
        </Link>
        &nbsp;>&nbsp;
        <Link to={`/namespaces/${namespace}`}>
          {namespace}
        </Link>
        {' > '}
        {release}
      </p>
      <Tabs items={tabItems} />
    </div>
  );
};

Release.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
    githubOrgScope: PropTypes.string,
    roles: PropTypes.arrayOf(PropTypes.string),
  }),
  authenticated: PropTypes.bool,
  backendURL: PropTypes.string,
};

export default Release;
