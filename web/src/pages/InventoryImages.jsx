import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { Table, Input, Button, Space, Tabs, Tag } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';

const InventoryImages = (Props) => {
  const [allImagesState, setAllImagesState] = useState({
    loading: false,
    data: [],
  });

  const [popularImagesState, setPopularImagesState] = useState({
    loading: false,
    data: [],
  });

  const [searchText, setSearchText] = useState('');
  const [searchedColumn, setSearchedColumn] = useState('');
  const searchInput = useRef(null);

  useEffect(() => {
    fetchAllImages();
    fetchPopularImages();
  }, []);

  const handleNotAuthenticated = () => {
    window.location.href = '/';
  };

  const fetchAllImages = () => {
    setAllImagesState({ loading: true, data: [] });

    fetch(`${Props.backendURL}/api/inventory/images`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status === 200) return response.json();
      if (response.status === 401) handleNotAuthenticated();
      if (response.status === 403) {
        console.log('Access forbidden - admin role required');
        return [];
      }
      return response.json();
    })
    .then(responseJson => {
      setAllImagesState({
        loading: false,
        data: responseJson || [],
      });
    })
    .catch(error => {
      console.log(error);
      setAllImagesState({
        loading: false,
        data: [],
      });
    });
  };

  const fetchPopularImages = () => {
    setPopularImagesState({ loading: true, data: [] });

    fetch(`${Props.backendURL}/api/inventory/images/popular`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status === 200) return response.json();
      if (response.status === 401) handleNotAuthenticated();
      if (response.status === 403) {
        console.log('Access forbidden - admin role required');
        return [];
      }
      return response.json();
    })
    .then(responseJson => {
      setPopularImagesState({
        loading: false,
        data: responseJson || [],
      });
    })
    .catch(error => {
      console.log(error);
      setPopularImagesState({
        loading: false,
        data: [],
      });
    });
  };

  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters) => {
    clearFilters();
    setSearchText('');
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={searchInput}
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            Search
          </Button>
          <Button
            onClick={() => clearFilters && handleReset(clearFilters)}
            size="small"
            style={{ width: 90 }}
          >
            Reset
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    ),
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
        : '',
    filterDropdownProps: {
      onOpenChange: (visible) => {
        if (visible) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    render: (text) =>
      searchedColumn === dataIndex ? (
        <Highlighter
          highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
          searchWords={[searchText]}
          autoEscape
          textToHighlight={text ? text.toString() : ''}
        />
      ) : (
        text
      ),
  });

  // Popular images columns
  const popularColumns = [
    {
      title: 'Image',
      dataIndex: 'url',
      key: 'url',
      sorter: (a, b) => (a.url > b.url ? 1 : -1),
      width: '70%',
      render: (url) => (
        <code style={{ fontSize: '12px' }}>{url}</code>
      ),
    },
    {
      title: 'Count',
      dataIndex: 'count',
      key: 'count',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a.count - b.count,
      width: '30%',
      
    },
  ];

  // All images columns
  const allImagesColumns = [
    {
      title: 'Cluster',
      dataIndex: 'cluster',
      key: 'cluster',
      sorter: (a, b) => (a.cluster > b.cluster ? 1 : -1),
      width: '10%',
      ...getColumnSearchProps('cluster'),
    },
    {
      title: 'Namespace',
      dataIndex: 'namespace',
      key: 'namespace',
      sorter: (a, b) => (a.namespace > b.namespace ? 1 : -1),
      width: '12%',
      ...getColumnSearchProps('namespace'),
      render: (namespace) => (
        <Link to={`/namespaces/${namespace}`}>{namespace}</Link>
      ),
    },
    {
      title: 'Release',
      dataIndex: 'parsedReleaseName',
      key: 'parsedReleaseName',
      sorter: (a, b) => (a.parsedReleaseName > b.parsedReleaseName ? 1 : -1),
      width: '12%',
      ...getColumnSearchProps('parsedReleaseName'),
      render: (parsedReleaseName, record) => {
        if (!parsedReleaseName) return '';
        return (
          <Link to={`/release/${record.cluster}/${record.namespace}/${parsedReleaseName}`}>
            {parsedReleaseName}
          </Link>
        );
      },
    },
    {
      title: 'Resource name',
      dataIndex: 'resourceName',
      key: 'resourceName',
      sorter: (a, b) => (a.resourceName > b.resourceName ? 1 : -1),
      width: '12%',
      ...getColumnSearchProps('resourceName'),
    },
    {
      title: 'Image',
      dataIndex: 'url',
      key: 'url',
      sorter: (a, b) => (a.url > b.url ? 1 : -1),
      width: '30%',
      ...getColumnSearchProps('url'),
      render: (url) => (
        <code style={{ fontSize: '11px' }}>{url}</code>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'source',
      key: 'source',
      sorter: (a, b) => (a.source > b.source ? 1 : -1),
      width: '14%',
      filters: [
        { text: 'Deployment', value: 'deployment' },
        { text: 'StatefulSet', value: 'statefulset' },
        { text: 'DaemonSet', value: 'daemonset' },
        { text: 'Job', value: 'job' },
        { text: 'CronJob', value: 'cronjob' },
      ],
      onFilter: (value, record) => record.source === value,
      render: (source) => {
        let color = 'default';
        if (source === 'deployment') color = 'blue';
        if (source === 'statefulset') color = 'green';
        if (source === 'daemonset') color = 'purple';
        if (source === 'job') color = 'orange';
        if (source === 'cronjob') color = 'volcano';
        return <Tag color={color}>{source}</Tag>;
      },
    },
  ];

  const tabItems = [
    {
      key: 'popular',
      label: 'Popular',
      children: (
        <div>
          <p>Most frequently used container images across all clusters.</p>
          <Table
            rowKey={(record, index) => `${record.url}-${index}`}
            pagination={{ pageSize: 20, showSizeChanger: false }}
            loading={popularImagesState.loading}
            dataSource={popularImagesState.data}
            columns={popularColumns}
          />
        </div>
      ),
    },
    {
      key: 'all',
      label: 'All',
      children: (
        <div>
          <p>Complete list of all container images in use.</p>
          <Table
            rowKey={(record, index) => `${record.cluster}-${record.namespace}-${record.parsedReleaseName}-${record.resourceName}-${record.url}-${record.source}-${index}`}
            pagination={{ pageSize: 50, showSizeChanger: false }}
            loading={allImagesState.loading}
            dataSource={allImagesState.data}
            columns={allImagesColumns}
          />
        </div>
      ),
    },
  ];

  return (
    <div>
      <h1>Image Inventory</h1>
      <Tabs defaultActiveKey="popular" items={tabItems} />
    </div>
  );
};

InventoryImages.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
  }),
  backendURL: PropTypes.string,
};

export default InventoryImages;

