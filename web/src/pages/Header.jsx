import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import {
  FolderOutlined,
  UserOutlined,
  LinkOutlined,
  LogoutOutlined,
} from '@ant-design/icons';
import { Menu } from 'antd';

import React from 'react';

const Header = (props) => {
  const roles = props.user.roles || []; 
  const userMenuItems = [
    {
      label: <Link to="/namespaces">Namespaces</Link>,
      key: 'namespaces',
      icon: <FolderOutlined />,
    },
    // if user roles contain "admin", show inventory
    roles.includes('admin') && {
      label: <Link to="/inventory/clusters">Inventory</Link>,
      key: 'inventory',
      icon: <LinkOutlined />,
      children: [
        {
          label: <Link to="/inventory/clusters">Clusters</Link>,
          key: 'inventory-clusters',
          icon: <LinkOutlined />,
        },
        {
          label: <Link to="/inventory/components">Components</Link>,
          key: 'inventory-components',
          icon: <LinkOutlined />,
        },
        {
          label: <Link to="/inventory/images">Images</Link>,
          key: 'inventory-images',
          icon: <LinkOutlined />,
        }
      ],
    },
    {
      label:
        props.user.githubOrgScope == 'internal' ? (
          <Link to="/profile">{props.user.name}</Link>
        ) : (
          props.user.name
        ),
      key: 'profile',
      icon: <UserOutlined />,
      children: [
        props.user.githubOrgScope == 'internal' && {
          label: <Link to="/profile">Accounts</Link>,
          key: 'accounts',
          icon: <LinkOutlined />,
        },
        {
          label: <a href={`${props.backendURL}/auth/logout`}>Logout</a>,
          key: 'logout',
          icon: <LogoutOutlined />,
        },
      ],
    },
  ];

  return (
    <div className="menu">
      {props.authenticated && <Menu mode="horizontal" items={userMenuItems} />}
    </div>
  );
};

Header.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
  }),
  authenticated: PropTypes.bool,
  backendURL: PropTypes.string,
};

export default Header;
