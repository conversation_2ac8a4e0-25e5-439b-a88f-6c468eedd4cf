import React from 'react';
import PropTypes from 'prop-types';

class PasswordLoginPage extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      password: '',
      error: null,
    };
    this.handlePasswordChange = this.handlePasswordChange.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
  }

  handlePasswordChange(event) {
    this.setState({ password: event.target.value, error: null });
  }

  handleSubmit(event) {
    event.preventDefault();
    fetch(`${this.props.backendURL}/auth/password`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ password: this.state.password }),
    })
      .then(response => {
        if (response.ok) {
          window.location.href = '/namespaces';
          return;
        }
        // If response is not ok, parse the json and throw an error
        return response.json().then(errorBody => {
          throw new Error(errorBody.message || 'Authentication failed');
        });
      })
      .catch(error => {
        this.setState({ error: error.message });
        console.error(error);
      });
  }

  render() {
    return (
      <div>
        <h1>Password Login</h1>
        {this.state.error && <p style={{ color: 'red' }}>{this.state.error}</p>}
        <form onSubmit={this.handleSubmit}>
          <label>
            Password:
            <input type="password" value={this.state.password} onChange={this.handlePasswordChange} />
          </label>
          <input type="submit" value="Submit" />
        </form>
      </div>
    );
  }
}

PasswordLoginPage.propTypes = {
  backendURL: PropTypes.string,
};

export default PasswordLoginPage;