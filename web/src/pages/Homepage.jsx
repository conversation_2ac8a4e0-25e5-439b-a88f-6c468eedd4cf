import React from 'react';
import PropTypes from 'prop-types';

class HomePage extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      loginMethods: [],
    };
  }

  componentDidMount() {
    if (!this.props.authenticated) {
      fetch(`${this.props.backendURL}/auth/login/methods`)
        .then(response => response.json())
        .then(data => this.setState({ loginMethods: data }));
    }
  }

  render() {
    return (
      <div>
        {!this.props.authenticated ? (
          <div>
            <h1>Silta Dashboard</h1>
            { this.state.loginMethods.length === 0 ? (
              <p>No login methods available.</p>
            ) : (
              this.state.loginMethods.map(method => {
                if (method === 'github') {
                return <p key={method}><a href={`${this.props.backendURL}/auth/github`}>Log in with Github</a></p>;
              }
              if (method === 'password') {
                return <p key={method}><a href={'/auth/password'}>Log in with password</a></p>;
              }
              return null;
            }
            ))}
          </div>
        ) : (
          <div>
            <h1>Welcome {this.props.user.name}!</h1>
          </div>
        )}
      </div>
    );
  }
}

HomePage.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
  }),
  authenticated: PropTypes.bool,
  backendURL: PropTypes.string,
};


export default HomePage;
