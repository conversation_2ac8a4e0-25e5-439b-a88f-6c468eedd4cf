import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { Table, Input, Button, Space } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';

// Component to render collapsible namespace list
const NamespaceCell = ({ namespaces }) => {
  const [expanded, setExpanded] = useState(false);

  if (!namespaces || namespaces.length === 0) {
    return '-';
  }

  return (
    <div>
      <a
        onClick={() => setExpanded(!expanded)}
        style={{ cursor: 'pointer', userSelect: 'none' }}
      >
        {namespaces.length} namespace{namespaces.length !== 1 ? 's' : ''}
      </a>
      {expanded && (
        <div style={{
          marginTop: '8px',
          paddingLeft: '8px',
          borderLeft: '2px solid #1890ff',
          display: 'flex',
          flexDirection: 'column',
          gap: '4px'
        }}>
          {namespaces.map((ns, idx) => (
            <Link key={idx} to={`/namespaces/${ns.namespace}`} style={{ display: 'block' }}>
              {ns.namespace} ({ns.cluster})
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

NamespaceCell.propTypes = {
  namespaces: PropTypes.array,
};

const InventoryComponents = (Props) => {
  const [componentsState, setComponentsState] = useState({
    loading: false,
    data: [],
  });

  const [searchText, setSearchText] = useState('');
  const [searchedColumn, setSearchedColumn] = useState('');
  const searchInput = useRef(null);

  // Filter states
  const [serviceNameFilter, setServiceNameFilter] = useState('');
  const [namespaceNameFilter, setNamespaceNameFilter] = useState('');

  useEffect(() => {
    fetchComponents();
  }, [serviceNameFilter, namespaceNameFilter]);

  const handleNotAuthenticated = () => {
    window.location.href = '/';
  };

  const fetchComponents = () => {
    setComponentsState({ loading: true, data: [] });

    // Build query parameters
    const params = new URLSearchParams();
    if (serviceNameFilter) params.append('serviceName', serviceNameFilter);
    if (namespaceNameFilter) params.append('namespaceName', namespaceNameFilter);

    const queryString = params.toString();
    const url = `${Props.backendURL}/api/inventory/components${queryString ? '?' + queryString : ''}`;

    fetch(url, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Access-Control-Allow-Credentials': true,
      },
    })
    .then(response => {
      if (response.status === 200) return response.json();
      if (response.status === 401) handleNotAuthenticated();
      if (response.status === 403) {
        console.log('Access forbidden - admin role required');
        return [];
      }
      return response.json();
    })
    .then(responseJson => {
      setComponentsState({
        loading: false,
        data: responseJson || [],
      });
    })
    .catch(error => {
      console.log(error);
      setComponentsState({
        loading: false,
        data: [],
      });
    });
  };

  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters) => {
    clearFilters();
    setSearchText('');
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={searchInput}
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            Search
          </Button>
          <Button
            onClick={() => clearFilters && handleReset(clearFilters)}
            size="small"
            style={{ width: 90 }}
          >
            Reset
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    ),
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
        : '',
    filterDropdownProps: {
      onOpenChange: (visible) => {
        if (visible) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    render: (text) =>
      searchedColumn === dataIndex ? (
        <Highlighter
          highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
          searchWords={[searchText]}
          autoEscape
          textToHighlight={text ? text.toString() : ''}
        />
      ) : (
        text
      ),
  });

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      defaultSortOrder: 'ascend',
      sorter: (a, b) => (a.name > b.name ? 1 : -1),
      width: '20%',
      ...getColumnSearchProps('name'),
    },
    {
      title: 'Version',
      dataIndex: 'parsedVersion',
      key: 'parsedVersion',
      sorter: (a, b) => (a.parsedVersion > b.parsedVersion ? 1 : -1),
      width: '10%',
    },
    {
      title: 'Source',
      dataIndex: 'source',
      key: 'source',
      sorter: (a, b) => {
        const sourceA = a.source || '';
        const sourceB = b.source || '';
        return sourceA > sourceB ? 1 : -1;
      },
      width: '10%',
      render: (source) => source || '-',
    },
    {
      title: 'Images',
      dataIndex: 'images',
      key: 'images',
      width: '25%',
      render: (images) => (
        <div style={{ whiteSpace: 'pre-line', fontSize: '11px', fontFamily: 'monospace' }}>
          {images && images.length > 0 ? images.join('\n') : '-'}
        </div>
      ),
    },
    {
      title: 'Namespaces',
      dataIndex: 'namespaces',
      key: 'namespaces',
      sorter: (a, b) => {
        const aFirst = a.namespaces && a.namespaces.length > 0 ? a.namespaces.length : 0;
        const bFirst = b.namespaces && b.namespaces.length > 0 ? b.namespaces.length : 0;
        return aFirst - bFirst;
      },
      width: '15%',
      render: (namespaces) => <NamespaceCell namespaces={namespaces} />,
    },
  ];

  return (
    <div>
      <h1>Component Inventory</h1>
      <p>Aggregated list of enabled components from all releases.</p>
      
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Input
            placeholder="Filter by service name"
            value={serviceNameFilter}
            onChange={(e) => setServiceNameFilter(e.target.value)}
            style={{ width: 200 }}
            allowClear
          />
          <Input
            placeholder="Filter by namespace"
            value={namespaceNameFilter}
            onChange={(e) => setNamespaceNameFilter(e.target.value)}
            style={{ width: 200 }}
            allowClear
          />
        </Space>
      </div>

      <Table
        rowKey={(record) => `${record.name}-${record.parsedVersion}`}
        pagination={{ pageSize: 50, showSizeChanger: false }}
        loading={componentsState.loading}
        dataSource={componentsState.data}
        columns={columns}
      />
    </div>
  );
};

InventoryComponents.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
  }),
  backendURL: PropTypes.string,
};

export default InventoryComponents;

