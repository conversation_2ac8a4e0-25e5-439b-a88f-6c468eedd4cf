/* eslint no-console: "off" */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Avatar, List } from 'antd';
import 'antd/es/table/style';
import { UserOutlined } from '@ant-design/icons';

const Profile = props => {
  const [Props] = useState(props);

  return (
    <div>
      <h1>Accounts</h1>
      {props.user.githubOrgScope == 'internal' ? null : <b>Rolebindings are not generated for external users. You are an external user.</b>}
      <p>Link your accounts to enable automated cluster access mapping when using kubectl cli.</p>
      <List itemLayout="horizontal">
        <List.Item>
            <List.Item.Meta
              avatar={<Avatar size="large" icon={<UserOutlined />} src={props.user.githubProfileImageUrl} />}
              title="Github"
              description={
                <>
                  <div>Screen name: {props.user.githubScreenName}</div>
                  <div>ID: {props.user.githubId}</div>
                </>
              }
            />
          </List.Item>
        <List.Item extra={props.user.googleId ? <a href={`${props.backendURL}/auth/google/unlink`}>Unlink</a> : <a href={`${props.backendURL}/auth/google`}>Link</a>}>
            <List.Item.Meta
              avatar={<Avatar size="large" icon={<UserOutlined />} src={props.user.googlePictureUrl} />}
              title="Google"
              description={
                props.user.googleId 
                  ? (
                    <>
                      <div>Email: {props.user.googleEmail}</div>
                      <div>ID: {props.user.googleId}</div>
                    </>
                  ) 
                  : <>Not linked, <a href={`${props.backendURL}/auth/google`}>click here to link.</a></>
              }
            />
          </List.Item>
      </List>
    </div>
  );
};

Profile.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
  }),
  // authenticated: PropTypes.bool,
  // backendURL: PropTypes.string
};

export default Profile;
