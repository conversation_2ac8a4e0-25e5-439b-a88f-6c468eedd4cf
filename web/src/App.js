import React, { Component } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import PropTypes from 'prop-types';

import Header from './pages/Header';
import HomePage from './pages/Homepage';
import Profile from './pages/Profile';
import Namespaces from './pages/Namespaces';
import Releases from './pages/Releases';
import Release from './pages/Release';
import PasswordLoginPage from './pages/PasswordLoginPage';
import InventoryClusters from './pages/InventoryClusters';
import InventoryImages from './pages/InventoryImages';
import InventoryComponents from './pages/InventoryComponents';

// https://u.ant.design/v5-for-19 
// https://github.com/ant-design/v5-patch-for-react-19
// This patch is necessary to fix compatibility issues with React 19.
import '@ant-design/v5-patch-for-react-19';

const backendURL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:4000';

/* eslint-disable react/no-unused-state */
class App extends Component {
  constructor(props) {
    super(props);

    this.state = {
      user: {},
      error: null,
      authenticated: false,
    };
  }

  componentDidMount() {
    // Fetch does not send cookies. So you should add credentials: 'include'
    fetch(`${backendURL}/auth/login/success`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Access-Control-Allow-Credentials': true,
      },
    })
      .then(response => {
        if (response.status === 200) return response.json();
        throw new Error('failed to authenticate user');
      })
      .then(responseJson => {
        this.setState({
          authenticated: true,
          user: responseJson.user,
        });
      })
      .catch(error => {
        this.setState({
          authenticated: false,
          error: `Failed to authenticate user: ${error}`,
        });
      });
  }

  render() {
    return (
      <Router>
        <Header
          authenticated={this.state.authenticated}
          user={this.state.user}
          backendURL={backendURL}
        />
        <div>
          {this.state.authenticated ? (
            <Routes>
              <Route
                path="/"
                element={<HomePage
                  authenticated={this.state.authenticated}
                  user={this.state.user}
                  backendURL={backendURL}
                />}
              />
              <Route
                path="/profile"
                element={<Profile
                    authenticated={this.state.authenticated}
                    user={this.state.user}
                    backendURL={backendURL}
                  />}
              />
              <Route
                path="/namespaces"
                element={<Namespaces
                    authenticated={this.state.authenticated}
                    user={this.state.user}
                    backendURL={backendURL}
                  />}
              />
              <Route
                path="/namespaces/:namespace"
                element={<Releases
                    authenticated={this.state.authenticated}
                    user={this.state.user}
                    backendURL={backendURL}
                  />}
              />
              <Route
                path="/release/:cluster/:namespace/:release"
                element={<Release
                    authenticated={this.state.authenticated}
                    user={this.state.user}
                    backendURL={backendURL}
                  />}
              />
              <Route
                path="/inventory/clusters"
                element={<InventoryClusters
                    authenticated={this.state.authenticated}
                    user={this.state.user}
                    backendURL={backendURL}
                  />}
              />
              <Route
                path="/inventory/images"
                element={<InventoryImages
                    authenticated={this.state.authenticated}
                    user={this.state.user}
                    backendURL={backendURL}
                  />}
              />
              <Route
                path="/inventory/components"
                element={<InventoryComponents
                    authenticated={this.state.authenticated}
                    user={this.state.user}
                    backendURL={backendURL}
                  />}
              />
            </Routes>
          )
          : (
           <Routes>
             <Route
               path="/auth/password"
               element={<PasswordLoginPage
                 backendURL={backendURL}
               />}
             />
             <Route
               path="*"
               element={<HomePage
                 authenticated={this.state.authenticated}
                 user={this.state.user}
                 backendURL={backendURL}
               />}
             />
           </Routes>
         )}
        </div>
      </Router>
    );
  }
}

App.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string,
    githubProfileImageUrl: PropTypes.string,
    githubId: PropTypes.string,
    githubScreenName: PropTypes.string,
    _id: PropTypes.string,
  }),
};

export default App;
