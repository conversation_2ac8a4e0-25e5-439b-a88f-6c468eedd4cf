{"name": "silta-dashboard-web", "version": "0.1.0", "private": true, "scripts": {"start": "node scripts/start.js", "build": "node scripts/build.js", "stylelint": "./node_modules/.bin/stylelint ./src"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@babel/core": "^7.27.4", "@pmmmwh/react-refresh-webpack-plugin": "^0.6.1", "@svgr/webpack": "^8.1.0", "antd": "^5.26.2", "babel-jest": "^30.0.2", "babel-loader": "^10.0.0", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.1.0", "bfj": "^9.1.2", "browserslist": "^4.25.1", "camelcase": "^8.0.0", "case-sensitive-paths-webpack-plugin": "^2.4.0", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.2", "dotenv": "^16.5.0", "dotenv-expand": "^12.0.2", "eslint-config-react-app": "^7.0.1", "file-loader": "^6.2.0", "fs-extra": "^11.3.0", "html-webpack-plugin": "^5.6.3", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.3", "jest-resolve": "^30.0.2", "jest-watch-typeahead": "^3.0.1", "mini-css-extract-plugin": "^2.9.2", "nodemon": "^3.1.10", "nth-check": "^2.1.1", "postcss": "^8.5.6", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^8.1.1", "postcss-normalize": "^13.0.1", "postcss-preset-env": "^10.2.3", "prompts": "^2.4.2", "prop-types": "^15.8.1", "react": "^19.1.0", "react-app-polyfill": "^3.0.0", "react-dev-utils": "^12.0.1", "react-dom": "^19.1.0", "react-highlight-words": "^0.21.0", "react-lazylog": "^4.5.3", "react-refresh": "^0.17.0", "react-router-dom": "^7.6.2", "resolve": "^1.22.10", "resolve-url-loader": "^5.0.0", "sass-loader": "^16.0.5", "semver": "^7.7.2", "source-map-loader": "^5.0.0", "style-loader": "^4.0.0", "tailwindcss": "^4.1.11", "terser-webpack-plugin": "^5.3.14", "webpack": "^5.99.9", "webpack-dev-middleware": "^7.4.2", "webpack-dev-server": "^5.2.2", "webpack-manifest-plugin": "^5.0.1", "workbox-webpack-plugin": "^7.3.0"}, "devDependencies": {"stylelint": "^16.21.0", "stylelint-config-recommended": "^16.0.0"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "standard": {"parser": "babel-es<PERSON>"}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": [], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true}, "babel": {"presets": ["react-app"]}}