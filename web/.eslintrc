{
  "extends": ["eslint-config-react-app/base"],
  "env": {
    "jest": true,
    "browser": true,
    "node": true,
    "es6": true
  },
  "globals": {
    "page": true,
    "browser": true,
    "context": true,
    "jestPuppeteer": true
  },
  "rules": {
    "arrow-body-style": [2, "as-needed"],
    "class-methods-use-this": 0,
    "comma-dangle": [2, "always-multiline"],
    "import/imports-first": 0,
    "import/newline-after-import": 0,
    "import/no-dynamic-require": 0,
    "import/no-extraneous-dependencies": 0,
    "import/no-named-as-default": 0,
    "no-underscore-dangle": 0,
    "import/no-webpack-loader-syntax": 0,
    "import/prefer-default-export": 0,
    "jsx-a11y/anchor-is-valid": 0,
    "jsx-a11y/aria-props": 2,
    "jsx-a11y/heading-has-content": 0,
    "jsx-a11y/label-has-associated-control": [
      2,
      {
        // NOTE: If this error triggers, either disable it or add
        // your custom components, labels and attributes via these options
        // See https://github.com/evcohen/eslint-plugin-jsx-a11y/blob/master/docs/rules/label-has-associated-control.md
        "controlComponents": ["Input"]
      }
    ],
    "jsx-a11y/label-has-for": 0,
    "jsx-a11y/html-has-lang": 0,
    "jsx-a11y/mouse-events-have-key-events": 2,
    "jsx-a11y/role-has-required-aria-props": 2,
    "jsx-a11y/role-supports-aria-props": 2,
    "jsx-quotes": [2, "prefer-double"],
    "max-len": 0,
    "newline-per-chained-call": 0,
    "no-confusing-arrow": 0,
    "no-console": 1,
    "no-unused-vars": 1,
    "no-use-before-define": 0,
    "no-nested-ternary": 0,
    "prefer-template": 2,
    "react/destructuring-assignment": 0,
    "react/jsx-closing-tag-location": 0,
    "react/forbid-prop-types": 0,
    "react/jsx-first-prop-new-line": [2, "multiline"],
    "react/jsx-filename-extension": 0,
    "react/jsx-no-target-blank": 0,
    "react/jsx-uses-vars": 2,
    "react/jsx-props-no-spreading": 0,
    "react/require-default-props": 0,
    "react/require-extension": 0,
    "react/self-closing-comp": 0,
    "react/sort-comp": 0,
    "require-yield": 0,
    "eol-last": 1,
    "semi": [2, "always"],
    "quotes": [1, "single"]
  }
}
