# OAuth setup

Dashboard requires Github App setup for dashboard login and Google Oauth application 
setup for account mapping, cluster RBAC creation. 

**Creating a GitHub App:**
- Go to Github Apps list in organisation profile (`https://github.com/organizations/[orgname]/settings/apps`)
- Click "New Github App"
- Enter following data (for local environment, differs for production use):
  - Homepage URL: `http://[silta-domain]`. This is not an important field, callback urls are.
  - Callback URL: `http://localhost:4000/auth/github/callback` (add more as needed)
  - Expire user authorization tokens: `false` (not sure how long is the token valid. If the code is changed so that it uses App ID and App Secret instead of token, this can be set to true)
  - Request user authorization (OAuth) during installation: `false`
  - Enable Device Flow: `false`
  - Webhook: 
    - Active: `false`
  - Permissions:
    - Repository permissions:
      - Metadata: Read only
    - Organization permissions:
      - Members: read only
      - Projects: read only
    - Account permissions: none
  - Where can this GitHub App be installed: Only on this account

- Put Client ID and Client Secret in `APP_CONFIG_JSON` as `github_client_id` and `github_client_secret` 
- Generate a private key for the app and put it in `APP_CONFIG_JSON` as `github_app_private_key`
- Install the app to the organisation and put the installation ID in `APP_CONFIG_JSON` as `github_app_installation_id`. You can get installation ID from browser URL.

**Creating an Google OAuth 2.0 credentials:**
- Go to "APIs & Services" > "Credentials" in GCP project You want to set this up (`https://console.cloud.google.com/apis/credentials?project=[project-id]`). 
- Click "CREATE CREDENTIALS" > "OAuth client ID"
- Enter following data (for local environment, differs for production use):
  - Application type: "Web application"
  - Authorized JavaScript origins: `http://localhost:4000`
  - Authorized redirect URIs: `http://localhost:4000/auth/google/callback`
- Put Client ID and Client Secret in `APP_CONFIG_JSON` as `google_client_id` and `google_client_secret`
- Go to "OAuth consent screen" page and set "User type" to "Internal". This will deny linking of non-@org emails to the account. 
