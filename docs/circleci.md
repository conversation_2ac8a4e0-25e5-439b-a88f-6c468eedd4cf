# CircleCI personal token

Dashboard can use CircleCI API to inject access credentials to CircleCI projects. 
You'll need to set up personal access token for CircleCI API. 

**Creating a personal CircleCI access token:**
  - Go to [CircleCI personal access tokens](https://circleci.com/account/api) page.
  - Click "Create New Token".
  - Give the token a name, i.e. "Silta Dashboard".
  - Use the token in `APP_CONFIG_JSON` as `circleci_token`.
