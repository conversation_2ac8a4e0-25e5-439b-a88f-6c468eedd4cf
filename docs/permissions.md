# Permission levels

## Login

Login is handled by Github. Github requires following Oauth scopes from user:
 - repo (Read-only access to code, commit statuses, collaborators, and deployment statuses for public repositories and organizations. Scope "repo:status" is insufficient for repos+affiliation=collaborator, needs full "repos" scope. otherwise /user/repos gets only partial list and user can't see all projects/namespaces)

Application stores user's access token to make API calls on user's behalf for scopes requested (repo). This is required for user to be able to use dashboard without repeated requests to Github.

Only organization members and external contributors are able to log in. 

## Namespace listing, releases, logs and events

Users are able to see all namespaces that have identical project name in pre-configured organisation and where they have at least "Write" (push) access. This includes "Maintain" and "Admin".  

Users are also able to see release listing, logs and events for all namespaces that they have access to.

If project defines external contributors with listed permissions, they are also able to see namespaces.

## Google account linking

Users are able to link Google account to their Github account. This provides ability for dashboard to create rolebindings in configured Kubernetes clusters, allowing kubectl access to clusters. 

## Rolebinding creation for cluster access

Dashboard creates rolebinding in configured Kubernetes clusters for internal users, allowing kubectl access to clusters. 

Rolebinding for external users is not created. 
