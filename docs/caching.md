# Caching

# Github Login data

User data is pulled from Github during login. This data is stored in session and retained until user logs out. If user access to organisation is removed, it still takes up to 1 hour for dashboard to notice this (see "Project list" below). 

If You have to void user access ASAP, You have to manually remove user cache collection (dashboard.usercache.GithubId where GithubId is user's Github ID, not the Username) from MongoDB at the moment. It might be easier to just drop the whole usercache collection.

# Project list

Project (namespaces) listing is cached for 1 hour. This is done to reduce number of API calls to Github and speed up dashboard loading. If You know You've been assigned access to new project, You can force cache refresh by logging out and logging in again.

# Releases

Release listing is cached for 2 minutes. Helm release listing is slow, especially for projects with many releases. This is done to speed up dashboard loading.
Note: If helm listing times out within 10 seconds, alternative release listing is used, but the listing is still stored in cache.
