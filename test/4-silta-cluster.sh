#!/bin/bash

set -e
source "$(dirname "$0")/1-env.sh"

MINIKUBE_IP=$(minikube ip)

helm upgrade --install \
cert-manager jetstack/cert-manager \
--namespace cert-manager \
--create-namespace \
--version v1.16.2 \
--set crds.enabled=true \
--set global.logLevel=1 \
--wait

# helm dependency build "./silta-cluster"

helm upgrade --install silta-cluster wunderio/silta-cluster \
--create-namespace \
--namespace silta-cluster \
--set clusterDomain=${CLUSTER_DOMAIN} \
--values "$(dirname "$0")/silta-cluster-minikube.yaml" \
--wait

# Cluster landing page test
#curl --resolve ${CLUSTER_DOMAIN}:443:${MINIKUBE_IP} https://${CLUSTER_DOMAIN} -ILk --fail
#curl --resolve ${CLUSTER_DOMAIN}:80:${MINIKUBE_IP} --resolve ${CLUSTER_DOMAIN}:443:${MINIKUBE_IP} http://${CLUSTER_DOMAIN} -IL --fail