## Cluster setup

This installs a local kubernetes cluster in minikube, sets up silta-cluster and deploys drupal, frontend and simple charts.

1. Run this to set up cluster and deploy drupal, frontend and simple charts

```
./0-all.sh
```

2. Create dashboard service account in cluster.

Note: make sure you are on correct cluster (minikube)
```
../keys/make-service-account.sh
```

This will create a kubeconfig file (i.e. `k8s-silta-dashboard-sa-kube-system-conf`) that you'll need to add to configuration in next step.

3. Create dashboard configuration file.

- Copy `backend/conf/example.conf.json` to `backend/conf/conf.json`
- Edit values in clusters section
```
"cluster": {
  "type": "minikube",
  "name": "local",
  "caData": "LS0tLS1...S0tCg==",
  "server": "https://************:8443",
  "insecureSkipTLSVerify": false
},
"user": {
  "name": "silta-dashboard-sa-kube-system-minikube",
  "token": "eyJhbGc...ZInA"
},
```
 - `caData`: value from `clusters[0].cluster.certificate-authority-data`
- `server`:
    - For Linux users: value from `clusters[0].cluster.server`
    - For MaxOS users: use value from `clusters[0].cluster.server` but replace server IP with `host.docker.internal`, keep the port number. I.e: `https://host.docker.internal:54302`
 - `user.name`: value from `users[0].name`
 - `user.token`: value from `users[0].user.token`

 4. Run dashboard

 ```
 docker-compose up
 ```

## Cluster removal

```
minikube delete
```
