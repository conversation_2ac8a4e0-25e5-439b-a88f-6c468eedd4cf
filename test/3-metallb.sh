#!/bin/bash

set -e
source "$(dirname "$0")/1-env.sh"

MINIKUBE_IP=$(minikube ip)

##############
# MetalLB setup
# https://github.com/kubernetes/minikube/issues/10307#issuecomment-1024575716

METALLB_IP_START=${MINIKUBE_IP}
METALLB_IP_END=${MINIKUBE_IP}

minikube addons enable metallb
sleep 10

# Patch MetalLB config with updated IP address range
kubectl apply -f - -n metallb-system << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: config
  namespace: metallb-system
data:
  config: |
    address-pools:
      - name: default
        protocol: layer2
        addresses:
        - ${METALLB_IP_START}-${METALLB_IP_END}
EOF

# Patch MetalLB images to use the correct registry
# Workaround for https://github.com/metallb/metallb/issues/1862
# Remove once this is tagged and released (> v1.29.0)
# https://github.com/kubernetes/minikube/pull/16056
# image="quay.io/metallb/controller:v0.9.6@sha256:6932cf255dd7f06f550c7f106b9a206be95f847ab8cb77aafac7acd27def0b00"
# kubectl scale -n metallb-system deployment/controller --replicas=0
# kubectl patch deployment -n metallb-system controller --type=json -p='[{"op": "replace", "path": "/spec/template/spec/containers/0/image", "value": "'${image}'"}]'
# kubectl scale -n metallb-system deployment/controller --replicas=1
# image="quay.io/metallb/speaker:v0.9.6@sha256:7a400205b4986acd3d2ff32c29929682b8ff8d830837aff74f787c757176fa9f"
# kubectl patch daemonset -n metallb-system speaker --type=json -p='[{"op": "replace", "path": "/spec/template/spec/containers/0/image", "value": "'${image}'"}]'

sleep 5

NAMESPACE=metallb-system
APP=metallb
TIMEOUT=30s

function metallb_logs() {
    echo "Timed out waiting for ${COMPONENT} to become ready"
    kubectl get events -n ${NAMESPACE}
    kubectl logs --sort-by='.metadata.creationTimestamp' -l app=${APP} -l component=${COMPONENT} -n ${NAMESPACE}
    exit 1
}

for COMPONENT in controller speaker
do
    kubectl wait \
        --for condition=ready pod \
        -l app=${APP} -l component=${COMPONENT} \
        -n ${NAMESPACE} \
        --timeout=${TIMEOUT} || metallb_logs
done
