#!/bin/bash

set -eo pipefail

K8S_PROJECT_REPO_DIR=/home/<USER>/project

mkdir -p ~/.local/bin
export PATH=$PATH:~/.local/bin

# Installing buildah since docker daemon is not available in the CI environment
if ! command -v buildah &> /dev/null; then
    sudo apt-get update && sudo apt-get install -y podman
    echo 'unqualified-search-registries = ["docker.io"]' | sudo tee -a /etc/containers/registries.conf
fi

# Cloning project repository       
echo 'Cloning repository...'
if [ ! -d ${K8S_PROJECT_REPO_DIR}/.git ]; then 
    git clone http://github.com/wunderio/simple-project-k8s.git ${K8S_PROJECT_REPO_DIR}
fi


# Building images
# Can't use docker build here because it requires a running docker daemon, which is not available in the CI environment
echo 'Building Docker images...'

sudo podman build \
  --tag ${SIMPLE_NGINX_IMAGE} \
  --file ${K8S_PROJECT_REPO_DIR}/silta/nginx.Dockerfile \
  ${K8S_PROJECT_REPO_DIR}/hello

# sudo buildah login -u auth -p broken --tls-verify=false registry.minikube.local.wdr.io:80

sudo podman push --tls-verify=false ${SIMPLE_NGINX_IMAGE}
