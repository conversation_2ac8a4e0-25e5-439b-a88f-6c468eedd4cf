traefik:
  replicas: 1
  ssl:
    enabled: true
  service:
    annotations:
      metallb.universe.tf/allow-shared-ip: "shared"
  # metallb shared ip works only with "Cluster" TP
  externalTrafficPolicy: Cluster

ssl:
  enabled: true
  email: <EMAIL>
  issuer: selfsigned
  
csi-rclone:
  enabled: true
  params:
    remote: "s3"
    remotePath: "projectname"
    
    # Minio as S3 provider
    s3-provider: "Minio"
    s3-endpoint: "http://silta-cluster-minio:9000"
    # Default credentials of minio chart https://github.com/minio/charts/blob/master/minio/values.yaml
    s3-access-key-id: "YOURACCESSKEY"
    s3-secret-access-key: "YOURSECRETKEY"
  # nodePlugin:
  #   kubeletBasePath: "/var/snap/microk8s/common/var/lib/kubelet"

minio:
  enabled: true
  resources:
    requests:
      memory: 512M
  persistence:
    size: 5Gi

gitAuth:
  enabled: true
  port: 2222
  keyserver:
    enabled: false
  authorizedKeys: []
  annotations:
    metallb.universe.tf/allow-shared-ip: "shared"
  # metallb shared ip works only with "Cluster" TP 
  externalTrafficPolicy: Cluster
  
sshKeyServer:
  enabled: false

# Deployment remover
deploymentRemover:
  enabled: false

docker-registry:
  enabled: true
  secrets:
    htpasswd: false

