#!/bin/bash

set -e
source "$(dirname "$0")/1-env.sh"

################################################################
## Silta CLI
if ! command -v silta &> /dev/null; then
    echo "Silta CLI not found. Installing..."
    if [[ "$(uname)" == "Darwin" ]]; then
        brew install wunderio/tap/silta-cli
    elif [[ "$(uname)" == "Linux" ]]; then
        mkdir -p ~/.local/bin
        
        # Latest tagged release
        latest_release_url=$(curl -s https://api.github.com/repos/wunderio/silta-cli/releases/latest | jq -r '.assets[] | .browser_download_url | select(endswith("linux-amd64.tar.gz"))')
        curl -sL $latest_release_url | tar xz -C ~/.local/bin
        chmod +x ~/.local/bin/silta
    fi

    echo "Silta CLI installed successfully."
fi

( set -x; silta version )

################################################################
## Helm
if ! command -v helm &> /dev/null; then
    echo "Helm not found. Installing..."
    if [[ "$(uname)" == "Darwin" ]]; then
        brew install helm
    elif [[ "$(uname)" == "Linux" ]]; then
        mkdir -p ~/.local/bin
        mkdir -p /tmp/helm

        # Install Helm 3
        HELM_VERSION=v3.16.3
        curl -o /tmp/helm.tar.gz https://get.helm.sh/helm-${HELM_VERSION}-linux-amd64.tar.gz \
        && tar -zxvf /tmp/helm.tar.gz -C /tmp \
        && mv /tmp/linux-amd64/helm ~/.local/bin/helm
        rm -rf /tmp/helm.tar.gz /tmp/linux-amd64
        chmod +x ~/.local/bin/helm
    fi
    echo "Helm installed successfully."
fi

( set -x; helm version )

helm plugin list | grep -q 'unittest' || helm plugin install https://github.com/helm-unittest/helm-unittest --version 0.5.1

helm repo list | grep -q 'https://storage.googleapis.com/charts.wdr.io' || helm repo add wunderio https://storage.googleapis.com/charts.wdr.io
helm repo list | grep -q 'https://charts.jetstack.io' || helm repo add jetstack https://charts.jetstack.io
helm repo list | grep -q 'https://agents.instana.io/helm' || helm repo add instana https://agents.instana.io/helm
helm repo list | grep -q 'https://kubernetes-sigs.github.io/nfs-subdir-external-provisioner' || helm repo add nfs-subdir-external-provisioner https://kubernetes-sigs.github.io/nfs-subdir-external-provisioner
helm repo list | grep -q 'https://helm.twun.io' || helm repo add twun https://helm.twun.io
helm repo list | grep -q 'https://charts.bitnami.com/bitnami' || helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo list | grep -q 'https://storage.googleapis.com/charts.wdr.io' || helm repo add wunderio https://storage.googleapis.com/charts.wdr.io
helm repo list | grep -q 'https://percona.github.io/percona-helm-charts/' || helm repo add percona https://percona.github.io/percona-helm-charts
helm repo list | grep -q 'https://helm.elastic.co' || helm repo add elastic https://helm.elastic.co
helm repo list | grep -q 'https://jouve.github.io/charts/' || helm repo add jouve https://jouve.github.io/charts/
helm repo list | grep -q 'https://codecentric.github.io/helm-charts' || helm repo add codecentric https://codecentric.github.io/helm-charts
helm repo list | grep -q 'https://kubernetes.github.io/ingress-nginx' || helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo list | grep -q 'https://helm.nginx.com/stable' || helm repo add nginx-stable https://helm.nginx.com/stable

helm repo update

################################################################
## Minikube

# Available minikube kubernetes version list:
# "minikube config defaults kubernetes-version"
# and https://kubernetes.io/releases/patch-releases/
MINIKUBE_KUBERNETES_VERSION=${MINIKUBE_KUBERNETES_VERSION:-latest}

CLUSTER_DOCKER_REGISTRY=registry.${CLUSTER_DOMAIN}:80

if ! command -v minikube &> /dev/null; then
    echo "Minikube not found. Installing..."
    if [[ "$(uname)" == "Darwin" ]]; then
        brew install minikube
    elif [[ "$(uname)" == "Linux" ]]; then
        mkdir -p ~/.local/bin
        curl -Lo ~/.local/bin/minikube https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64 && chmod +x ~/.local/bin/minikube
    fi
fi

( set -x; minikube version )

if [[ "$(uname)" == "Darwin" ]]; then
# On MacOS backend service needs to use host.docker.internal for communications with minikube. This is due to the way
# docker and it's networking works on MacOS.
# Add host.docker.internal to apiservernames to solve TLS issues
  minikube start \
  --kubernetes-version "${MINIKUBE_KUBERNETES_VERSION}" \
  --insecure-registry "${CLUSTER_DOCKER_REGISTRY}" \
  --cni auto \
  --wait all \
  --apiserver-names host.docker.internal
elif [[ "$(uname)" == "Linux" ]]; then
  minikube start \
  --kubernetes-version "${MINIKUBE_KUBERNETES_VERSION}" \
  --insecure-registry "${CLUSTER_DOCKER_REGISTRY}" \
  --cni auto \
  --wait all
fi
