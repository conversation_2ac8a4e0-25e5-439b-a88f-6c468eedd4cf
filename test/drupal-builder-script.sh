#!/bin/bash

set -eo pipefail

K8S_PROJECT_REPO_DIR=/home/<USER>/project

mkdir -p ~/.local/bin
export PATH=$PATH:~/.local/bin

# Installing podman since docker daemon is not available in the CI environment
if ! command -v podman &> /dev/null; then
    sudo apt-get update && sudo apt install -y podman
    echo 'unqualified-search-registries = ["docker.io"]' | sudo tee -a /etc/containers/registries.conf
fi

# Cloning project repository       
echo 'Cloning drupal-project-k8s repository...'
if [ ! -d ${K8S_PROJECT_REPO_DIR}/.git ]; then 
    git clone http://github.com/wunderio/drupal-project-k8s.git ${K8S_PROJECT_REPO_DIR}
fi

# Composer install
echo 'Running Composer install...'
composer install -n --prefer-dist --ignore-platform-reqs --optimize-autoloader -d ${K8S_PROJECT_REPO_DIR}

# Building images
# Can't use docker build here because it requires a running docker daemon, which is not available in the CI environment
echo 'Building Docker images...'
sudo podman build \
--tag ${NGINX_IMAGE} \
--file ${K8S_PROJECT_REPO_DIR}/silta/nginx.Dockerfile \
${K8S_PROJECT_REPO_DIR}/web


sudo podman build \
  --tag ${PHP_IMAGE} \
  --file ${K8S_PROJECT_REPO_DIR}/silta/php.Dockerfile \
  ${K8S_PROJECT_REPO_DIR}

sudo podman build \
  --tag ${SHELL_IMAGE} \
  --file ${K8S_PROJECT_REPO_DIR}/silta/shell.Dockerfile \
  ${K8S_PROJECT_REPO_DIR}


# sudo buildah login -u auth -p broken --tls-verify=false registry.minikube.local.wdr.io:80

sudo podman push --tls-verify=false ${NGINX_IMAGE}
sudo podman push --tls-verify=false ${PHP_IMAGE}
sudo podman push --tls-verify=false ${SHELL_IMAGE}
