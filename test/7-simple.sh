#!/bin/bash
set -eo pipefail
source "$(dirname "$0")/1-env.sh"

MINIKUBE_IP=$(minikube ip)
K8S_NAMESPACE="simple-project-k8s"
SIMPLE_NGINX_IMAGE=${CLUSTER_DOCKER_REGISTRY}/simple-project-k8s/test-simple-nginx:latest

# Create namespace
kubectl create namespace "${K8S_NAMESPACE}" || true

# Create pod for running the test
if kubectl get pod test-deploy -n "$K8S_NAMESPACE" >/dev/null
then
    kubectl delete pod test-deploy -n "$K8S_NAMESPACE" --wait
fi

kubectl run test-deploy \
    --namespace "$K8S_NAMESPACE" \
    --image=wunderio/silta-cicd:circleci-php8.3-node22-composer2-v1 \
    --restart=Never \
    -- /bin/sh -c "while [ ! -f /tmp/done ]; do sleep 1; done"

# Wait for the pod to be ready
if ! kubectl wait --for=condition=ready pod -l run=test-deploy -n "$K8S_NAMESPACE" --timeout=5m; then
    echo "Pod test-deploy did not become ready"
    exit 1
fi

# Copy the builder script to the pod
kubectl cp "$(dirname "$0")/simple-builder-script.sh" "$K8S_NAMESPACE/test-deploy:/tmp/builder-script.sh"

# execute the builder script in the pod
kubectl exec -it "test-deploy" -n "$K8S_NAMESPACE" -- /bin/sh -c "CLUSTER_DOMAIN=${CLUSTER_DOMAIN} CLUSTER_DOCKER_REGISTRY=${CLUSTER_DOCKER_REGISTRY} SIMPLE_NGINX_IMAGE=${SIMPLE_NGINX_IMAGE} /tmp/builder-script.sh"

# Deploy
silta ci release deploy \
    --release-name test \
    --chart-name simple \
    --branchname test \
    --silta-environment-name test \
    --cluster-domain "${CLUSTER_DOMAIN}" \
    --cluster-type minikube \
    --namespace simple-project-k8s \
    --nginx-image-url ${SIMPLE_NGINX_IMAGE} \
    --helm-flags "--set ssl.issuer=selfsigned" \
    --deployment-timeout 15m

# Web request test
#curl http://test.simple-project-k8s.${CLUSTER_DOMAIN} \
#    --user silta:demo --location-trusted \
#    --head --insecure --location \
#    --resolve test.simple-project-k8s.${CLUSTER_DOMAIN}:80:${MINIKUBE_IP} \
#    --resolve test.simple-project-k8s.${CLUSTER_DOMAIN}:443:${MINIKUBE_IP} \
#    --retry 5 --retry-delay 5 \
#    --fail

# Cleanup: create a file to signal that the job is done
kubectl exec -it "test-deploy" -n "$K8S_NAMESPACE" -- /bin/sh -c "touch /tmp/done"
