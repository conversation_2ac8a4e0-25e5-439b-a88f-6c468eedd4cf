#!/bin/bash
set -eo pipefail
source "$(dirname "$0")/1-env.sh"

MINIKUBE_IP=$(minikube ip)
K8S_NAMESPACE="frontend-project-k8s"
HELLO_IMAGE=${CLUSTER_DOCKER_REGISTRY}/frontend-project-k8s/test-frontend-hello:latest
WORLD_IMAGE=${CLUSTER_DOCKER_REGISTRY}/frontend-project-k8s/test-frontend-world:latest

# Create namespace
kubectl create namespace "${K8S_NAMESPACE}" || true

# Create pod for running the test
if kubectl get pod test-deploy -n "$K8S_NAMESPACE" >/dev/null
then
    kubectl delete pod test-deploy -n "$K8S_NAMESPACE" --wait
fi

kubectl run test-deploy \
    --namespace "$K8S_NAMESPACE" \
    --image=wunderio/silta-cicd:circleci-php8.3-node22-composer2-v1 \
    --restart=Never \
    -- /bin/sh -c "while [ ! -f /tmp/done ]; do sleep 1; done"

# Wait for the pod to be ready
if ! kubectl wait --for=condition=ready pod -l run=test-deploy -n "$K8S_NAMESPACE" --timeout=5m; then
    echo "Pod test-deploy did not become ready"
    exit 1
fi

# Copy the builder script to the pod
kubectl cp "$(dirname "$0")/frontend-builder-script.sh" "$K8S_NAMESPACE/test-deploy:/tmp/builder-script.sh"

# execute the builder script in the pod
kubectl exec -it "test-deploy" -n "$K8S_NAMESPACE" -- /bin/sh -c "CLUSTER_DOMAIN=${CLUSTER_DOMAIN} CLUSTER_DOCKER_REGISTRY=${CLUSTER_DOCKER_REGISTRY} HELLO_IMAGE=${HELLO_IMAGE} WORLD_IMAGE=${WORLD_IMAGE} /tmp/builder-script.sh"

# Deploy
silta ci release deploy \
--release-name test \
--chart-name frontend \
--branchname test \
--silta-environment-name test \
--cluster-domain "${CLUSTER_DOMAIN}" \
--cluster-type minikube \
--namespace frontend-project-k8s \
--helm-flags "--set ssl.issuer=selfsigned --set services.hello.image=${HELLO_IMAGE} --set services.world.image=${WORLD_IMAGE} --set services.hello.exposedRoute=/hello --set services.world.exposedRoute=/world" \
--deployment-timeout 15m

# Web request test
#curl http://test.frontend-project-k8s.${CLUSTER_DOMAIN}/hello \
#    --user silta:demo --location-trusted \
#    --head --insecure --location \
#    --resolve test.frontend-project-k8s.${CLUSTER_DOMAIN}:80:${MINIKUBE_IP} \
#    --resolve test.frontend-project-k8s.${CLUSTER_DOMAIN}:443:${MINIKUBE_IP} \
#    --retry 5 --retry-delay 5 \
#    --fail

#curl http://test.frontend-project-k8s.${CLUSTER_DOMAIN}/world \
#    --user silta:demo --location-trusted \
#    --head --insecure --location \
#    --resolve test.frontend-project-k8s.${CLUSTER_DOMAIN}:80:${MINIKUBE_IP} \
#    --resolve test.frontend-project-k8s.${CLUSTER_DOMAIN}:443:${MINIKUBE_IP} \
#    --retry 5 --retry-delay 5 \
#    --fail


# Cleanup: create a file to signal that the job is done
kubectl exec -it "test-deploy" -n "$K8S_NAMESPACE" -- /bin/sh -c "touch /tmp/done"
