#!/bin/bash
set -eo pipefail
source "$(dirname "$0")/1-env.sh"

MINIKUBE_IP=$(minikube ip)

K8S_NAMESPACE="drupal-project-k8s"
NGINX_IMAGE=${CLUSTER_DOCKER_REGISTRY}/drupal-project-k8s/test-drupal-nginx:latest
PHP_IMAGE=${CLUSTER_DOCKER_REGISTRY}/drupal-project-k8s/test-drupal-php:latest
SHELL_IMAGE=${CLUSTER_DOCKER_REGISTRY}/drupal-project-k8s/test-drupal-shell:latest

kubectl create namespace "${K8S_NAMESPACE}" || true

# Create pod for running the test
if kubectl get pod test-deploy -n "$K8S_NAMESPACE" >/dev/null
then
    kubectl delete pod test-deploy -n "$K8S_NAMESPACE" --wait
fi

kubectl run test-deploy \
    --namespace "$K8S_NAMESPACE" \
    --image=wunderio/silta-cicd:circleci-php8.3-node22-composer2-v1 \
    --restart=Never \
    -- /bin/sh -c "while [ ! -f /tmp/done ]; do sleep 1; done"
# Wait for the pod to be ready
if ! kubectl wait --for=condition=ready pod -l run=test-deploy -n "$K8S_NAMESPACE" --timeout=5m; then
    echo "Pod test-deploy did not become ready"
    exit 1
fi

# Copy the builder script to the pod
kubectl cp "$(dirname "$0")/drupal-builder-script.sh" "$K8S_NAMESPACE/test-deploy:/tmp/builder-script.sh"

# execute the builder script in the pod
kubectl exec -it "test-deploy" -n "$K8S_NAMESPACE" -- /bin/sh -c "CLUSTER_DOMAIN=${CLUSTER_DOMAIN} CLUSTER_DOCKER_REGISTRY=${CLUSTER_DOCKER_REGISTRY} NGINX_IMAGE=${NGINX_IMAGE} PHP_IMAGE=${PHP_IMAGE} SHELL_IMAGE=${SHELL_IMAGE} /tmp/builder-script.sh"

# Container does not have kubeconfig, let's run the deployment command directly
echo 'Deploying Drupal project...'
silta ci release deploy \
    --release-name test \
    --chart-name drupal \
    --branchname test \
    --silta-environment-name test \
    --nginx-image-url "${NGINX_IMAGE}" \
    --php-image-url "${PHP_IMAGE}" \
    --shell-image-url "${SHELL_IMAGE}" \
    --cluster-domain "${CLUSTER_DOMAIN}" \
    --cluster-type minikube \
    --db-root-pass rootpw \
    --db-user-pass dbpw \
    --gitauth-username test \
    --gitauth-password test \
    --namespace ${K8S_NAMESPACE} \
    --helm-flags "--set ssl.issuer=selfsigned" \
    --deployment-timeout 15m

echo 'Running Drupal installation...'
kubectl exec -it deploy/test-shell -n ${K8S_NAMESPACE} -- drush si -y

# Test the deployment by checking the landing page
#echo "Testing the deployment by checking the landing page..."
# Web request test
#curl http://test.drupal-project-k8s.${CLUSTER_DOMAIN} \
#    --user silta:demo --location-trusted \
#    --head --insecure --location \
#    --resolve test.drupal-project-k8s.${CLUSTER_DOMAIN}:80:${MINIKUBE_IP} \
#    --resolve test.drupal-project-k8s.${CLUSTER_DOMAIN}:443:${MINIKUBE_IP} \
#    --retry 5 --retry-delay 5 \
#    --fail


# Cleanup: create a file to signal that the job is done
kubectl exec -it "test-deploy" -n "$K8S_NAMESPACE" -- /bin/sh -c "touch /tmp/done"


