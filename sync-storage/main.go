package main

import (
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/rclone/rclone/fs/config/obscure"
	"golang.org/x/net/webdav"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var ReleaseSync *mongo.Collection
var Database *mongo.Database

func getEnv(key, fallback string) string {
	if value, ok := os.LookupEnv(key); ok {
		return value
	}
	return fallback
}

// CacheEntry represents an entry in the authorization cache
type CacheEntry struct {
	Secret    string
	Timestamp time.Time
}

// authCache stores the authorization details with a timestamp
var authCache = make(map[string]CacheEntry)
var cacheMutex = &sync.Mutex{}

// checkCache checks the cache for a valid entry
func checkCache(key string) (string, bool) {
	cacheMutex.Lock()
	defer cacheMutex.Unlock()

	entry, exists := authCache[key]
	if !exists || time.Since(entry.Timestamp) > 15*time.Minute {
		// Entry does not exist or is older than 15 minutes
		return "", false
	}
	return entry.Secret, true
}

// updateCache updates the cache with a new entry
func updateCache(key, secret string) {
	cacheMutex.Lock()
	defer cacheMutex.Unlock()

	authCache[key] = CacheEntry{
		Secret:    secret,
		Timestamp: time.Now(),
	}
}

// cleanupCache removes expired entries from the cache
func cleanupCache() {
	for {
		time.Sleep(15 * time.Minute) // Wait for 15 minutes before each cleanup
		now := time.Now()

		cacheMutex.Lock()
		for key, entry := range authCache {
			if now.Sub(entry.Timestamp) > 15*time.Minute {
				delete(authCache, key)
			}
		}
		cacheMutex.Unlock()
	}
}

func ConnectMongoDB() error {

	MongoURI := "*****************************************"
	if getEnv("MONGODB_HOST", "") != "" {
		MongoURI = fmt.Sprintf("mongodb://%s:%s@%s:27017", getEnv("MONGODB_USER", ""), getEnv("MONGODB_PASS", ""), getEnv("MONGODB_HOST", ""))
	}

	clientOptions := options.Client().ApplyURI(MongoURI)
	mongoClient, err := mongo.Connect(context.TODO(), clientOptions)
	if err != nil {
		log.Println("MongoDB connection failed:", err)
		return err
	}
	// Check the db connection
	err = mongoClient.Ping(context.TODO(), nil)
	if err != nil {
		log.Println("MongoDB ping failed:", err)
		return err
	}
	ReleaseSync = mongoClient.Database("dashboard").Collection("release_sync")
	Database = mongoClient.Database("dashboard")

	return nil
}

func extractAuth(r *http.Request) (string, string, error) {
	auth := strings.SplitN(r.Header.Get("Authorization"), " ", 2)

	if len(auth) != 2 || auth[0] != "Basic" {
		return "", "", fmt.Errorf("authorization failed")
	}

	payload, _ := base64.StdEncoding.DecodeString(auth[1])
	pair := strings.SplitN(string(payload), ":", 2)
	storageConnectionKey := pair[0]
	storageConnectionSecret := pair[1]

	if len(pair) != 2 {
		return "", "", fmt.Errorf("authorization failed")
	}

	return storageConnectionKey, storageConnectionSecret, nil
}

func authHandler(r *http.Request) bool {

	storageConnectionKey, storageConnectionSecret, err := extractAuth(r)
	if err != nil {
		log.Printf("Authorization failed for key '%s', IP: %s\n", storageConnectionKey, r.RemoteAddr)
		return false
	}

	// Check cache
	if secret, ok := checkCache(storageConnectionKey); ok {
		if secret == storageConnectionSecret {
			// log.Printf("Authorization successful for key (cached): '% s', IP: %s\n", storageConnectionKey, r.RemoteAddr)
			return true
		}
	}

	// Find match in MongoDB
	err = ConnectMongoDB()
	if err != nil {
		log.Println("MongoDB connection failed")
		return false
	}

	var rs struct {
		StorageConnectionKey    string `json:"storageConnectionKey" bson:"storageConnectionKey"`
		StorageConnectionSecret string `json:"storageCnnectionSecret" bson:"storageConnectionSecret"`
	}

	err = ReleaseSync.FindOne(context.TODO(), bson.M{"storageConnectionKey": storageConnectionKey}).Decode(&rs)
	if err != nil {
		log.Printf("Authorization failed for key '%s', IP: %s\n", storageConnectionKey, r.RemoteAddr)
		return false
	}

	// Check if the secret matches
	if storageConnectionSecret != obscure.MustReveal(rs.StorageConnectionSecret) {
		log.Printf("Authorization failed for key '%s', IP: %s\n", storageConnectionKey, r.RemoteAddr)
		return false
	}

	// Update cache
	updateCache(storageConnectionKey, storageConnectionSecret)

	log.Printf("Authorization successful for key: '%s', IP: %s\n", storageConnectionKey, r.RemoteAddr)

	return true
}

func requestHandler(w http.ResponseWriter, r *http.Request) {

	storageConnectionKey, _, err := extractAuth(r)
	if err != nil {
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Root directory format is ./mount/<year>/<month>/<date>/<storageConnectionKey>
	rootDir := "./mount/" + time.Now().Format("2006/01/02/") + storageConnectionKey

	// Create root directory if it doesn't exist
	if _, err := os.Stat(rootDir); os.IsNotExist(err) {
		os.MkdirAll(rootDir, 0755)
	}

	// Create a new WebDAV handler for this request
	webdavHandler := &webdav.Handler{
		Prefix:     getEnv("SYNC_STORAGE_ROUTE_PREFIX", ""),
		FileSystem: webdav.Dir(rootDir), // Uses a different subfolder for each storageConnectionKey
		LockSystem: webdav.NewMemLS(),
		// Logger: func(r *http.Request, err error) {
		// 	log.Printf("WebDAV DEBUG: %s %s, IP: %s\n", r.Method, r.URL, r.RemoteAddr)
		// },
	}

	webdavHandler.ServeHTTP(w, r)
}

func main() {

	// Require auth for all requests
	http.Handle("/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if !authHandler(r) {
			w.Header().Set("WWW-Authenticate", `Basic realm="Restricted"`)
			http.Error(w, "authorization failed", http.StatusUnauthorized)
			return
		}
		requestHandler(w, r)
	}))

	// Start the cache cleanup in a separate goroutine
	go cleanupCache()

	log.Println("Starting webdav server on :7000")
	log.Fatal(http.ListenAndServe(":7000", nil))

	// TLS is terminated at the load balancer, no need to generate certificate
	// openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes
	// log.Fatal(http.ListenAndServeTLS(":7000", "cert.pem", "key.pem", nil))

}
