package db

import (
	"context"
	"log"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/wunderio/silta-dashboard/backend/conf"
)

var Users *mongo.Collection
var UserCache *mongo.Collection
var ReleaseSync *mongo.Collection
var InventoryReleases *mongo.Collection
var InventoryImages *mongo.Collection
var Clusters *mongo.Collection
var Database *mongo.Database

func Connect() {

	log.Print("Connecting to MongoDB ... ")

	// Connect to MongoDB
	// var cred options.Credential
	// cred.AuthSource = "admin"
	// cred.Username = conf.MongoUsername
	// cred.Password = conf.MongoPassword
	// cred.AuthMechanism = "SCRAM-SHA-256"

	clientOptions := options.Client().ApplyURI(conf.MongoURI)

	mongoClient, err := mongo.Connect(context.TODO(), clientOptions)
	if err != nil {
		log.Fatal(err)
	}

	// Check the db connection
	err = mongoClient.Ping(context.TODO(), nil)
	if err != nil {
		log.Fatal(err)
	}

	Users = mongoClient.Database("dashboard").Collection("users")
	UserCache = mongoClient.Database("dashboard").Collection("usercache")
	ReleaseSync = mongoClient.Database("dashboard").Collection("release_sync")
	InventoryReleases = mongoClient.Database("dashboard").Collection("inventory-releases")
	InventoryImages = mongoClient.Database("dashboard").Collection("inventory-images")
	Clusters = mongoClient.Database("dashboard").Collection("clusters")
	Database = mongoClient.Database("dashboard")

	log.Println("connected!")
}
