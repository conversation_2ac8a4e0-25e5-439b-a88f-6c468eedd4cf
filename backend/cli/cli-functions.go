package cli

import (
	"context"
	"log"
	"time"

	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	helmAction "helm.sh/helm/v3/pkg/action"
)

// TODO: import silta-cli common function when released. This is a 1:1 copy of internal/common/ciReleaseFunctions
// UninstallRelease removes a Helm release and related resources
func UninstallHelmRelease(kubernetesClient *kubernetes.Clientset, helmClient *helmAction.Configuration, namespace string, releaseName string, deletePVCs bool) error {

	// Do not bail when release removal fails, remove related resources anyway.
	log.Printf("Removing release %s in %s", releaseName, namespace)
	uninstall := helmAction.NewUninstall(helmClient)
	uninstall.KeepHistory = false // Remove release secrets as well
	uninstall.DisableHooks = false
	uninstall.Timeout = 300 * time.Second // seconds, adjust as needed
	uninstall.Wait = true                 // Wait for resources to be deleted
	uninstall.DeletionPropagation = string(v1.DeletePropagationForeground)

	resp, err := uninstall.Run(releaseName)
	if err != nil {
		log.Printf("Failed to remove helm release: %s", err)
	} else {
		if resp != nil && resp.Info != "" {
			log.Printf("Helm uninstall info: %s", resp.Info)
		}
	}

	// Delete related jobs
	selectorLabels := []string{
		"release",
		"app.kubernetes.io/instance",
	}

	for _, l := range selectorLabels {
		selector := l + "=" + releaseName
		list, _ := kubernetesClient.BatchV1().Jobs(namespace).List(context.TODO(), v1.ListOptions{
			LabelSelector: selector,
		})
		for _, v := range list.Items {
			log.Printf("Removing job: %s", v.Name)
			propagationPolicy := v1.DeletePropagationBackground
			kubernetesClient.BatchV1().Jobs(namespace).Delete(context.TODO(), v.Name, v1.DeleteOptions{PropagationPolicy: &propagationPolicy})
		}
	}

	if deletePVCs {

		// Find and remove related PVC's by release name label
		PVC_client := kubernetesClient.CoreV1().PersistentVolumeClaims(namespace)

		selectorLabels = []string{
			"app",
			"release",
			"app.kubernetes.io/instance",
		}

		for _, l := range selectorLabels {
			selector := l + "=" + releaseName
			if l == "app" {
				selector = l + "=" + releaseName + "-es"
			}
			list, _ := PVC_client.List(context.TODO(), v1.ListOptions{
				LabelSelector: selector,
			})

			for _, v := range list.Items {
				log.Printf("Removing PVC: %s", v.Name)
				PVC_client.Delete(context.TODO(), v.Name, v1.DeleteOptions{})
			}
		}
	}

	return nil
}
