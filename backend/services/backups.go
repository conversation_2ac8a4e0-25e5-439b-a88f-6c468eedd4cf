package services

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	// Rclone imports

	_ "github.com/rclone/rclone/backend/all" // import all backends
	_ "github.com/rclone/rclone/cmd/mount"
	_ "github.com/rclone/rclone/cmd/mountlib"
	"github.com/rclone/rclone/fs"
	"github.com/rclone/rclone/fs/config/configmap"
	"github.com/rclone/rclone/fs/operations"
	_ "github.com/rclone/rclone/fs/operations" // import operations/* rc commands
	_ "github.com/rclone/rclone/fs/sync"       // import sync/*
	"github.com/rclone/rclone/vfs/vfscommon"

	"github.com/rclone/rclone/vfs"

	batchv1 "k8s.io/api/batch/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

type FsListOpFile struct {
	// Inherit all fields from operations.ListJSONItem
	operations.ListJSONItem
	// Rewrite ModTime to string
	ModTime string         `json:"ModTime"`
	Content []FsListOpFile `json:"Content"`
}

type FsListOpResponse struct {
	List []FsListOpFile `json:"list"`
}

func GetBackupsList(clusterKey string, namespaceName string, releaseName string) ([]FsListOpFile, error) {

	releaseInfo, err := GetReleaseInfo(clusterKey, namespaceName, releaseName)
	if err != nil {
		return []FsListOpFile{}, err
	}

	// if chart name is neither drupal nor frontend, return empty json
	if releaseInfo.Chart.Name() != "drupal" && releaseInfo.Chart.Name() != "frontend" {
		return []FsListOpFile{}, nil
	}

	clientset, err := GetKubeClient(clusterKey)
	if err != nil {
		log.Printf("Error getting kube client: %s", err)
		return []FsListOpFile{}, err
	}

	// try to read backup storageclass from release values
	if releaseInfo.Values["backup"] == nil {
		return []FsListOpFile{}, nil
	}

	storageClass := releaseInfo.Values["backup"].(map[string]interface{})["storageClassName"]
	csiDriverName := releaseInfo.Values["backup"].(map[string]interface{})["csiDriverName"]

	// Note: NFS may or may not be supported
	// See: https://github.com/rclone/rclone/blob/b9207e57274cd6e7c488c5bd751a058ba0f8b3b9/cmd/serve/nfs/server.go

	if storageClass == "silta-shared" && csiDriverName == "csi-rclone" {
		// read connection info from csi rclone secret in silta-cluster namespace
		rcloneSecret, err := clientset.CoreV1().Secrets("silta-cluster").Get(context.TODO(), "rclone-secret", metav1.GetOptions{})
		if err != nil {
			log.Printf("Error getting rclone secret: %s", err)
			return []FsListOpFile{}, err
		}

		pvcMountPath := getPvcMountPath(clientset, namespaceName, releaseName)
		mountPath := fmt.Sprintf("%s/%s", namespaceName, pvcMountPath)

		dirRemote := fmt.Sprintf("%s/%s/", rcloneSecret.Data["remotePath"], mountPath)

		// Copy rcloneSecret.Data to dashedParams
		dashedParams := map[string]string{}

		// Special case: set defaults for s3 remote
		if string(rcloneSecret.Data["remote"]) == "s3" {
			dashedParams["s3-chunk-size"] = "5Mi"
			dashedParams["s3-copy-cutoff"] = "5Gi"
		}

		for key, value := range rcloneSecret.Data {
			// Skip custom, non-spec, fields
			if key == "remote" || key == "remotePath" || key == "configData" || key == "remotePathSuffix" {
				continue
			}
			dashedParams[key] = string(value)
		}

		fsInfo, err := fs.Find(string(rcloneSecret.Data["remote"]))
		if err != nil {
			log.Fatalf("Failed to find remote type: %v", err)
			return []FsListOpFile{}, nil
		}

		// Create configmap from parameters
		m := configmap.Simple{}
		for k, v := range normalizeParams(fsInfo, dashedParams) {
			m.Set(k, v)
		}

		// Create new filesystem
		f, err := fsInfo.NewFs(context.Background(), "remote-name", dirRemote, m)
		if err != nil {
			log.Printf("Failed to create remote: %v", err)
			return []FsListOpFile{}, nil
		}

		opt := vfscommon.Options{
			ReadOnly:  true,
			CacheMode: vfscommon.CacheModeOff,
		}
		rVFS := vfs.New(f, &opt)

		// list the directory
		dirEntries, err := rVFS.ReadDir("/")
		if err != nil {
			fmt.Printf("Error reading dir: %v\n", err)
			return []FsListOpFile{}, nil
		}

		fmt.Printf("Backups %+v\n", dirEntries)

		backupList := []FsListOpFile{}
		for _, dirEntry := range dirEntries {
			if dirEntry.IsDir() {

				backup := FsListOpFile{
					ListJSONItem: operations.ListJSONItem{
						Name:    dirEntry.Name(),
						Path:    dirEntry.Name(),
						Size:    dirEntry.Size(),
						IsDir:   dirEntry.IsDir(),
						ModTime: operations.Timestamp{When: dirEntry.ModTime()},
					},
					ModTime: dirEntry.ModTime().Format(time.RFC3339),
				}

				// List subdirectory
				subDirEntries, err := rVFS.ReadDir(fmt.Sprintf("/%s", dirEntry.Name()))
				if err != nil {
					continue
				}
				if len(subDirEntries) > 0 {
					for _, subDirEntry := range subDirEntries {
						// Skip directories
						if subDirEntry.IsDir() {
							continue
						}
						backup.Content = append(backup.Content, FsListOpFile{
							ListJSONItem: operations.ListJSONItem{
								Name:    subDirEntry.Name(),
								Path:    fmt.Sprintf("%s/%s", dirEntry.Name(), subDirEntry.Name()),
								Size:    subDirEntry.Size(),
								IsDir:   subDirEntry.IsDir(),
								ModTime: operations.Timestamp{When: subDirEntry.ModTime()},
							},
							ModTime: subDirEntry.ModTime().Format(time.RFC3339),
						})
					}
				}

				backupList = append(backupList, backup)
			}
		}

		return backupList, nil
	}

	return []FsListOpFile{}, nil
}

// normalizeParams converts parameters with type prefix and dashes
// to the format expected by rclone internals
func normalizeParams(fsInfo *fs.RegInfo, dashedParams map[string]string) map[string]string {
	remotePrefix := fsInfo.Prefix

	normalized := make(map[string]string)
	prefix := remotePrefix + "-"

	for k, v := range dashedParams {

		if k == remotePrefix {
			normalized[k] = v
			continue
		}

		// Remove remote type prefix if present
		key := k
		if len(k) > len(prefix) && k[:len(prefix)] == prefix {
			key = k[len(prefix):]
		}

		// Convert dashes to underscores
		key = strings.ReplaceAll(key, "-", "_")

		normalized[key] = v
	}

	return normalized
}

func StartBackup(clusterKey string, namespaceName string, releaseName string) (string, error) {
	releaseInfo, err := GetReleaseInfo(clusterKey, namespaceName, releaseName)
	if err != nil {
		return "", err
	}

	// if chart name is neither drupal nor frontend, return empty json
	if releaseInfo.Chart.Name() != "drupal" && releaseInfo.Chart.Name() != "frontend" {
		return "", nil
	}

	clientset, err := GetKubeClient(clusterKey)
	if err != nil {
		log.Printf("Error getting kube client: %s", err)
		return "", err
	}

	cronJobName := fmt.Sprintf("%s-backup", releaseName)

	// read backup cronjob and create a new job from it
	cronJob, err := clientset.BatchV1().CronJobs(namespaceName).Get(context.TODO(), cronJobName, metav1.GetOptions{})
	if err != nil {
		log.Printf("Error getting cronjob: %s", err)
		return "", err
	}

	jobTemplate := cronJob.Spec.JobTemplate.DeepCopy()

	// create a new job from the cronjob template
	job := &batchv1.Job{
		ObjectMeta: jobTemplate.ObjectMeta,
		Spec:       jobTemplate.Spec,
	}

	job.Name = fmt.Sprintf("%s-manual-%d", cronJobName, time.Now().Unix())

	// Never restart the job
	job.Spec.Template.Spec.RestartPolicy = "Never"

	// Remove job resource 2 hours after completion
	twoHours := int32(2 * 60 * 60)
	job.Spec.TTLSecondsAfterFinished = &twoHours

	// create the job
	_, err = clientset.BatchV1().Jobs(namespaceName).Create(context.TODO(), job, metav1.CreateOptions{})
	if err != nil {
		log.Printf("Error creating job: %s", err)
		return "", err
	}

	return job.Name, nil
}

func getPvcMountPath(clientset *kubernetes.Clientset, namespaceName string, releaseName string) string {
	// Read from PVC metadata "storage.silta/storage-path" annotation, fall back to relaseName/backups if empty
	backupPvcName := fmt.Sprintf("%s-backup2", releaseName)
	backupPvc, err := clientset.CoreV1().PersistentVolumeClaims(namespaceName).Get(context.TODO(), backupPvcName, metav1.GetOptions{})
	if err != nil {
		// Retry the old backup pvc name
		backupPvcName = fmt.Sprintf("%s-backup", releaseName)
		backupPvc, err = clientset.CoreV1().PersistentVolumeClaims(namespaceName).Get(context.TODO(), backupPvcName, metav1.GetOptions{})
		if err != nil {
			log.Printf("Error getting PVC: %s", err)
			return ""
		}
	}
	backupSubPath := backupPvc.Annotations["storage.silta/storage-path"]
	if backupSubPath == "" {
		backupSubPath = fmt.Sprintf("%s/%s", releaseName, "backups")
	}

	return backupSubPath
}

func StartBackupItemDownload(clusterKey string, namespaceName string, releaseName string, filePath string, w http.ResponseWriter, r *http.Request) error {

	releaseInfo, err := GetReleaseInfo(clusterKey, namespaceName, releaseName)
	if err != nil {
		return err
	}

	// if chart name is neither drupal nor frontend, return empty json
	if releaseInfo.Chart.Name() != "drupal" && releaseInfo.Chart.Name() != "frontend" {
		return nil
	}

	clientset, err := GetKubeClient(clusterKey)
	if err != nil {
		log.Printf("Error getting kube client: %s", err)
		return err
	}

	// try to read backup storageclass from release values
	if releaseInfo.Values["backup"] == nil {
		return nil
	}

	storageClass := releaseInfo.Values["backup"].(map[string]interface{})["storageClassName"]
	csiDriverName := releaseInfo.Values["backup"].(map[string]interface{})["csiDriverName"]

	if storageClass == "silta-shared" && csiDriverName == "csi-rclone" {

		// read connection info from csi rclone secret in silta-cluster namespace.
		rcloneSecret, err := clientset.CoreV1().Secrets("silta-cluster").Get(context.TODO(), "rclone-secret", metav1.GetOptions{})
		if err != nil {
			log.Printf("Error getting rclone secret: %s", err)
			return err
		}

		pvcMountPath := getPvcMountPath(clientset, namespaceName, releaseName)
		mountPath := fmt.Sprintf("%s/%s", namespaceName, pvcMountPath)

		dirRemote := fmt.Sprintf("%s/%s/", rcloneSecret.Data["remotePath"], mountPath)

		// Copy rcloneSecret.Data to dashedParams
		dashedParams := map[string]string{}

		// Special case: set defaults for s3 remote
		if string(rcloneSecret.Data["remote"]) == "s3" {
			dashedParams["s3-chunk-size"] = "5Mi"
			dashedParams["s3-copy-cutoff"] = "5Gi"
		}

		for key, value := range rcloneSecret.Data {
			// Skip custom, non-spec, fields
			if key == "remote" || key == "remotePath" || key == "configData" || key == "remotePathSuffix" {
				continue
			}
			dashedParams[key] = string(value)
		}

		fsInfo, err := fs.Find(string(rcloneSecret.Data["remote"]))
		if err != nil {
			log.Printf("Failed to find remote type: %v", err)
			return err
		}

		// Create configmap from parameters
		m := configmap.Simple{}
		for k, v := range normalizeParams(fsInfo, dashedParams) {
			m.Set(k, v)
		}

		// Create new filesystem
		f, err := fsInfo.NewFs(context.Background(), "remote-name", dirRemote, m)
		if err != nil {
			log.Printf("Failed to create remote: %v", err)
			return err
		}

		// Create rclone fs config from args
		rVFS := vfs.New(f, &vfscommon.Options{
			ReadOnly:  true,
			CacheMode: vfscommon.CacheModeOff,
		})

		item, err := rVFS.Stat(filePath)
		if err == vfs.ENOENT {
			fs.Infof(filePath, "%s: File not found", r.RemoteAddr)
			http.Error(w, "File not found", http.StatusNotFound)
			return nil
		} else if err != nil {
			http.Error(w, "Failed to find file", http.StatusNotFound)
			return nil
		}
		if !item.IsFile() {
			http.Error(w, "Not a file", http.StatusNotFound)
			return nil
		}
		entry := item.DirEntry()
		if entry == nil {
			http.Error(w, "Can't open file being written", http.StatusNotFound)
			return nil
		}
		obj := entry.(fs.Object)
		file := item.(*vfs.File)

		// Set content length if we know how long the object is
		knownSize := obj.Size() >= 0
		if knownSize {
			w.Header().Set("Content-Length", strconv.FormatInt(item.Size(), 10))
		}

		// Set content type
		mimeType := fs.MimeType(r.Context(), obj)
		if mimeType == "application/octet-stream" && path.Ext(filePath) == "" {
			// Leave header blank so http server guesses
		} else {
			w.Header().Set("Content-Type", mimeType)
		}

		// Set the Last-Modified header to the timestamp
		w.Header().Set("Last-Modified", file.ModTime().UTC().Format(http.TimeFormat))

		// If HEAD no need to read the object since we have set the headers
		if r.Method == "HEAD" {
			return nil
		}

		// open the object
		in, err := file.Open(os.O_RDONLY)
		if err != nil {
			http.Error(w, "Failed to open file", http.StatusNotFound)
			return nil
		}
		defer func() {
			err := in.Close()
			if err != nil {
				fs.Errorf(filePath, "Failed to close file: %v", err)
			}
		}()

		w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", path.Base(filePath)))
		w.Header().Set("Accept-Ranges", "bytes")

		// Serve the file
		if knownSize {
			http.ServeContent(w, r, filePath, item.ModTime(), in)
		} else {
			// http.ServeContent can't serve unknown length files
			if rangeRequest := r.Header.Get("Range"); rangeRequest != "" {
				http.Error(w, "Can't use Range: on files of unknown length", http.StatusRequestedRangeNotSatisfiable)
				return nil
			}
			n, err := io.Copy(w, in)
			if err != nil {
				fs.Errorf(obj, "Didn't finish writing GET request (wrote %d/unknown bytes): %v", n, err)
				return nil
			}
		}

		return nil

	}

	return nil
}
