package services

import (
	"cmp"
	"context"
	"encoding/base64"
	"fmt"
	"io/ioutil"
	"log"
	"reflect"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/schemas"

	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/discovery"
	"k8s.io/client-go/discovery/cached/memory"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/restmapper"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/clientcmd/api"

	helmAction "helm.sh/helm/v3/pkg/action"
)

type apiNamespaceResponseStruct struct {
	Name     string   `json:"name"`
	Clusters []string `json:"clusters"`
}

type apiPodsResponseStruct struct {
	Name         string     `json:"name"`
	Namespace    string     `json:"namespace"`
	Cluster      string     `json:"cluster"`
	ClusterColor string     `json:"clusterColor"`
	Resource     corev1.Pod `json:"resource"`
	Containers   []string   `json:"containers"`
}
type apiResourceResponseStruct struct {
	Name         string                  `json:"name"`
	Namespace    string                  `json:"namespace"`
	Cluster      string                  `json:"cluster"`
	ClusterColor string                  `json:"clusterColor"`
	Type         string                  `json:"type"`
	Statefulset  appsv1.StatefulSet      `json:"statefulset"`
	Deployment   appsv1.Deployment       `json:"deployment"`
	Daemonset    appsv1.DaemonSet        `json:"daemonset"`
	Job          batchv1.Job             `json:"job"`
	Cronjob      batchv1.CronJob         `json:"cronjob"`
	Pod          corev1.Pod              `json:"resource"`
	Pods         []apiPodsResponseStruct `json:"pods"`
}

type KubeConfigCluster struct {
	Server                   string `json:"server" yaml:"server"`
	CertificateAuthorityData string `json:"certificate-authority-data" yaml:"certificate-authority-data"`
}

type KubeConfigClusters struct {
	Name    string            `json:"name" yaml:"name"`
	Cluster KubeConfigCluster `json:"cluster" yaml:"cluster"`
}

type KubeConfigContext struct {
	Cluster   string `json:"cluster" yaml:"cluster"`
	Namespace string `json:"namespace" yaml:"namespace"`
	User      string `json:"user" yaml:"user"`
}

type KubeConfigContexts struct {
	Name    string `json:"name" yaml:"name"`
	Context KubeConfigContext
}

type KubeConfigUser struct {
	Token string `json:"token" yaml:"token"`
}

type KubeConfigUsers struct {
	Name string         `json:"name" yaml:"name"`
	User KubeConfigUser `json:"user" yaml:"user"`
}

type KubeConfig struct {
	ApiVersion     string               `json:"apiVersion" yaml:"apiVersion"`
	Kind           string               `json:"kind" yaml:"kind"`
	CurrentContext string               `json:"current-context" yaml:"current-context"`
	Clusters       []KubeConfigClusters `json:"clusters" yaml:"clusters"`
	Contexts       []KubeConfigContexts `json:"contexts" yaml:"contexts"`
	Users          []KubeConfigUsers    `json:"users" yaml:"users"`
}

// staticRESTClientGetter just wraps our custom rest.Config
type staticRESTClientGetter struct {
	restConfig *rest.Config
	namespace  string
}

func NewStaticRESTClientGetter(restConfig *rest.Config, namespace string) *staticRESTClientGetter {
	return &staticRESTClientGetter{
		restConfig: restConfig,
		namespace:  namespace,
	}
}

func (s *staticRESTClientGetter) ToRESTConfig() (*rest.Config, error) {
	return s.restConfig, nil
}

func (s *staticRESTClientGetter) ToDiscoveryClient() (discovery.CachedDiscoveryInterface, error) {
	cfg, err := s.ToRESTConfig()
	if err != nil {
		return nil, err
	}
	client, err := discovery.NewDiscoveryClientForConfig(cfg)
	if err != nil {
		return nil, err
	}
	return memory.NewMemCacheClient(client), nil
}

func (s *staticRESTClientGetter) ToRESTMapper() (meta.RESTMapper, error) {
	discoveryClient, err := s.ToDiscoveryClient()
	if err != nil {
		return nil, err
	}
	return restmapper.NewDeferredDiscoveryRESTMapper(discoveryClient), nil
}

func (s *staticRESTClientGetter) ToRawKubeConfigLoader() clientcmd.ClientConfig {
	// Provide a minimal loader that returns the namespace
	// Ensure the namespace is properly set in both the context and overrides
	return clientcmd.NewNonInteractiveClientConfig(
		api.Config{
			Contexts: map[string]*api.Context{
				"default": {
					Namespace: s.namespace,
				},
			},
			CurrentContext: "default",
		},
		"default",
		&clientcmd.ConfigOverrides{
			CurrentContext: "default",
			Context: api.Context{
				Namespace: s.namespace,
			},
		},
		nil,
	)
}

func GetKubeClient(clusterKey string) (*kubernetes.Clientset, error) {

	cluster := conf.KubeClusters[clusterKey]

	CaData, err := base64.StdEncoding.DecodeString(cluster.Cluster.CaData)
	if err != nil {
		log.Printf("Error connecting to cluster: %s", err)
		return nil, err
	}

	// Load kubernetes configuration from cluster and user
	config := &rest.Config{
		Host:        cluster.Cluster.Server,
		BearerToken: cluster.User.Token,
		TLSClientConfig: rest.TLSClientConfig{
			CAData:   CaData,
			Insecure: cluster.Cluster.InsecureSkipTLSVerify,
		},
	}
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		log.Printf("Error connecting to kubernetes cluster: %s", err.Error())
		return nil, err
	}

	return clientset, nil
}

func GetKubePods(selectCluster string, selectNamespace string) []apiPodsResponseStruct {
	pods := []apiPodsResponseStruct{}

	for clusterKey, cluster := range conf.KubeClusters {

		if !cluster.Enabled {
			continue
		}

		if selectCluster == clusterKey || selectCluster == "" {

			clientset, err := GetKubeClient(clusterKey)
			if err != nil {
				log.Println("Error creating kubeconfig:", err)
				continue
			}

			var p *corev1.PodList

			if cluster.TimeoutSeconds == 0 {
				cluster.TimeoutSeconds = conf.KubeConnectionTimeoutSeconds
			}
			timeout := int64(cluster.TimeoutSeconds)
			listOptions := metav1.ListOptions{
				TimeoutSeconds: &timeout,
			}

			if len(selectNamespace) > 0 {
				// Get pods in specific namespace
				p, err = clientset.CoreV1().Pods(selectNamespace).List(context.TODO(), listOptions)
			} else {
				// Get pods in all namespaces
				p, err = clientset.CoreV1().Pods("").List(context.TODO(), listOptions)
			}
			if err != nil {
				log.Println("Error loading pod list:", err)
				continue
			}
			for _, pod := range p.Items {

				containers := []string{}
				for _, container := range pod.Spec.Containers {
					containers = append(containers, container.Name)
				}

				pods = append(pods, apiPodsResponseStruct{
					Name:         pod.Name,
					Namespace:    pod.Namespace,
					Cluster:      clusterKey,
					ClusterColor: GetClusterColor(clusterKey),
					Resource:     pod,
					Containers:   containers,
				})
			}
		}
	}

	return pods
}

func GetKubeStatefulsets(selectCluster string, selectNamespace string) []apiResourceResponseStruct {
	statefulsets := []apiResourceResponseStruct{}

	for clusterKey, cluster := range conf.KubeClusters {

		if !cluster.Enabled {
			continue
		}

		if selectCluster == clusterKey || selectCluster == "" {

			clientset, err := GetKubeClient(clusterKey)
			if err != nil {
				log.Println("Error creating kubeconfig:", err)
				continue
			}

			var s *appsv1.StatefulSetList

			if cluster.TimeoutSeconds == 0 {
				cluster.TimeoutSeconds = conf.KubeConnectionTimeoutSeconds
			}
			timeout := int64(cluster.TimeoutSeconds)
			listOptions := metav1.ListOptions{
				TimeoutSeconds: &timeout,
			}

			if len(selectNamespace) > 0 {
				// Get pods in specific namespace
				s, err = clientset.AppsV1().StatefulSets(selectNamespace).List(context.TODO(), listOptions)
			} else {
				// Get pods in all namespaces
				s, err = clientset.AppsV1().StatefulSets("").List(context.TODO(), listOptions)
			}
			if err != nil {
				log.Println("Error loading statefulset list:", err)
				continue
			}
			for _, statefulset := range s.Items {

				statefulsets = append(statefulsets, apiResourceResponseStruct{
					Name:         statefulset.Name,
					Namespace:    statefulset.Namespace,
					Cluster:      clusterKey,
					ClusterColor: GetClusterColor(clusterKey),
					Type:         "statefulset",
					Statefulset:  statefulset,
				})
			}
		}
	}

	return statefulsets
}

func GetKubeDeployments(selectCluster string, selectNamespace string) []apiResourceResponseStruct {
	deployments := []apiResourceResponseStruct{}

	for clusterKey, cluster := range conf.KubeClusters {

		if !cluster.Enabled {
			continue
		}

		if selectCluster == clusterKey || selectCluster == "" {

			clientset, err := GetKubeClient(clusterKey)
			if err != nil {
				log.Println("Error creating kubeconfig:", err)
				continue
			}

			var s *appsv1.DeploymentList

			if cluster.TimeoutSeconds == 0 {
				cluster.TimeoutSeconds = conf.KubeConnectionTimeoutSeconds
			}
			timeout := int64(cluster.TimeoutSeconds)
			listOptions := metav1.ListOptions{
				TimeoutSeconds: &timeout,
			}

			if len(selectNamespace) > 0 {
				// Get pods in specific namespace
				s, err = clientset.AppsV1().Deployments(selectNamespace).List(context.TODO(), listOptions)
			} else {
				// Get pods in all namespaces
				s, err = clientset.AppsV1().Deployments("").List(context.TODO(), listOptions)
			}
			if err != nil {
				log.Println("Error loading statefulset list:", err)
				continue
			}
			for _, deployment := range s.Items {

				deployments = append(deployments, apiResourceResponseStruct{
					Name:         deployment.Name,
					Namespace:    deployment.Namespace,
					Cluster:      clusterKey,
					ClusterColor: GetClusterColor(clusterKey),
					Type:         "deployment",
					Deployment:   deployment,
				})
			}
		}
	}

	return deployments
}

func GetKubeDaemonsets(selectCluster string, selectNamespace string) []apiResourceResponseStruct {
	daemonsets := []apiResourceResponseStruct{}
	for clusterKey, cluster := range conf.KubeClusters {
		if !cluster.Enabled {
			continue
		}
		if selectCluster == clusterKey || selectCluster == "" {
			clientset, err := GetKubeClient(clusterKey)
			if err != nil {
				log.Println("Error creating kubeconfig:", err)
				continue
			}
			var s *appsv1.DaemonSetList
			if cluster.TimeoutSeconds == 0 {
				cluster.TimeoutSeconds = conf.KubeConnectionTimeoutSeconds
			}
			timeout := int64(cluster.TimeoutSeconds)
			listOptions := metav1.ListOptions{
				TimeoutSeconds: &timeout,
			}
			if len(selectNamespace) > 0 {
				s, err = clientset.AppsV1().DaemonSets(selectNamespace).List(context.TODO(), listOptions)
			} else {
				s, err = clientset.AppsV1().DaemonSets("").List(context.TODO(), listOptions)
			}
			if err != nil {
				log.Println("Error loading daemonset list:", err)
				continue
			}
			for _, daemonset := range s.Items {
				daemonsets = append(daemonsets, apiResourceResponseStruct{
					Name:         daemonset.Name,
					Namespace:    daemonset.Namespace,
					Cluster:      clusterKey,
					ClusterColor: GetClusterColor(clusterKey),
					Type:         "daemonset",
					Daemonset:    daemonset,
				})
			}
		}
	}
	return daemonsets
}

func GetKubeJobs(selectCluster string, selectNamespace string) []apiResourceResponseStruct {
	jobs := []apiResourceResponseStruct{}
	for clusterKey, cluster := range conf.KubeClusters {
		if !cluster.Enabled {
			continue
		}
		if selectCluster == clusterKey || selectCluster == "" {
			clientset, err := GetKubeClient(clusterKey)
			if err != nil {
				log.Println("Error creating kubeconfig:", err)
				continue
			}
			var s *batchv1.JobList
			if cluster.TimeoutSeconds == 0 {
				cluster.TimeoutSeconds = conf.KubeConnectionTimeoutSeconds
			}
			timeout := int64(cluster.TimeoutSeconds)
			listOptions := metav1.ListOptions{
				TimeoutSeconds: &timeout,
			}
			if len(selectNamespace) > 0 {
				s, err = clientset.BatchV1().Jobs(selectNamespace).List(context.TODO(), listOptions)
			} else {
				s, err = clientset.BatchV1().Jobs("").List(context.TODO(), listOptions)
			}
			if err != nil {
				log.Println("Error loading job list:", err)
				continue
			}
			for _, job := range s.Items {
				jobs = append(jobs, apiResourceResponseStruct{
					Name:         job.Name,
					Namespace:    job.Namespace,
					Cluster:      clusterKey,
					ClusterColor: GetClusterColor(clusterKey),
					Type:         "job",
					Job:          job,
				})
			}
		}
	}
	return jobs
}

func GetKubeCronjobs(selectCluster string, selectNamespace string) []apiResourceResponseStruct {
	cronjobs := []apiResourceResponseStruct{}
	for clusterKey, cluster := range conf.KubeClusters {
		if !cluster.Enabled {
			continue
		}
		if selectCluster == clusterKey || selectCluster == "" {
			clientset, err := GetKubeClient(clusterKey)
			if err != nil {
				log.Println("Error creating kubeconfig:", err)
				continue
			}
			var s *batchv1.CronJobList
			if cluster.TimeoutSeconds == 0 {
				cluster.TimeoutSeconds = conf.KubeConnectionTimeoutSeconds
			}
			timeout := int64(cluster.TimeoutSeconds)
			listOptions := metav1.ListOptions{
				TimeoutSeconds: &timeout,
			}
			if len(selectNamespace) > 0 {
				s, err = clientset.BatchV1().CronJobs(selectNamespace).List(context.TODO(), listOptions)
			} else {
				s, err = clientset.BatchV1().CronJobs("").List(context.TODO(), listOptions)
			}
			if err != nil {
				log.Println("Error loading cronjob list:", err)
				continue
			}
			for _, cronjob := range s.Items {
				cronjobs = append(cronjobs, apiResourceResponseStruct{
					Name:         cronjob.Name,
					Namespace:    cronjob.Namespace,
					Cluster:      clusterKey,
					ClusterColor: GetClusterColor(clusterKey),
					Type:         "cronjob",
					Cronjob:      cronjob,
				})
			}
		}
	}
	return cronjobs
}

// Get all namespaces from all clusters
func GetNamespaces() []apiNamespaceResponseStruct {

	namespaces := []apiNamespaceResponseStruct{}

	for clusterKey, cluster := range conf.KubeClusters {

		if !cluster.Enabled {
			continue
		}

		clientset, err := GetKubeClient(clusterKey)
		if err != nil {
			log.Printf("Error connecting to kubernetes cluster: %s", err.Error())
			continue
		}
		if cluster.TimeoutSeconds == 0 {
			cluster.TimeoutSeconds = conf.KubeConnectionTimeoutSeconds
		}
		timeout := int64(cluster.TimeoutSeconds)
		listOptions := metav1.ListOptions{
			TimeoutSeconds: &timeout,
		}
		n, err := clientset.CoreV1().Namespaces().List(context.TODO(), listOptions)
		if err != nil {
			log.Printf("Error listing namespaces: %s", err.Error())
			continue
		}

		// Check if namespace already exists in list, append cluster to existing item if so
		for _, namespace2 := range n.Items {
			nsExists := false
			for i, namespace := range namespaces {
				if namespace.Name == namespace2.Name {
					namespaces[i].Clusters = append(namespaces[i].Clusters, clusterKey)
					nsExists = true
				}
			}
			if !nsExists {
				namespaces = append(namespaces, apiNamespaceResponseStruct{Name: namespace2.Name, Clusters: []string{clusterKey}})
			}
		}
	}

	return namespaces
}

func NamespacesContains(namespaces []apiNamespaceResponseStruct, namespace string) bool {
	for _, ns := range namespaces {
		if ns.Name == namespace {
			return true
		}
	}
	return false
}

func GetAggregatedFieldFromPods(pods []apiPodsResponseStruct, fieldSelector string, returnFieldSelector string) map[string][]string {
	aggregate := make(map[string][]string)

	for _, pod := range pods {

		p := reflect.ValueOf(pod)
		fv := p.FieldByName(fieldSelector)
		if fv.IsValid() {

			// Insert aggregated field if not exists
			if _, ok := aggregate[fv.String()]; !ok {
				aggregate[fv.String()] = []string{}
			}

			// Assign value to aggregated field
			if returnFieldSelector != "" {
				found := false

				rv := p.FieldByName(returnFieldSelector)

				// If value is not already in map, append it
				for _, v := range aggregate[fv.String()] {
					if v == rv.String() {
						found = true
					}
				}
				if !found {
					aggregate[fv.String()] = append(aggregate[fv.String()], rv.String())
				}
			}
		}
	}

	return aggregate
}

func GetNamespaceReleases(selectCluster string, selectNamespace string) []schemas.ReleaseResponseStruct {

	releases := []schemas.ReleaseResponseStruct{}

	for clusterKey, cluster := range conf.KubeClusters {

		if !cluster.Enabled {
			continue
		}

		if selectCluster == clusterKey || selectCluster == "" {

			// Ways to get release list:
			// 1. Use helm to get release list. It's slow and will time out if there are a lot of releases. It lists all releases as expected.
			// 2. List pods in namespace and aggregate on release name from label. This is not 100% accurate as helm releases with no running pods will be missing.
			// 3. List silta-release configmaps in namespace and get release name from label. This is not 100% accurate as only silta chart releases will be listed.
			// 4. List helm secrets in namespace, use only latest version, get release name from label. This is very slow for some reason.

			// We're doing option 1 now, using helm to get the release list.
			r, err := GetReleasesFromHelm(clusterKey, selectNamespace)
			if err == nil {
				releases = append(releases, r...)

			} else {
				log.Printf("Failed to list Helm releases: %s", err)

				// If there's an error loading releases, it's possible helm has timed out.
				// Fall back to listing pods and aggregating on release name.
				// Or, if we're listing all releases, fall back to listing secrets and aggregating on release name.

				// If this was timeout, try one of our fallbacks
				if strings.Contains(err.Error(), "Client.Timeout") {
					// Create release list from silta-release configmaps
					log.Printf("Helm list on %s/%s timed out, falling back to listing silta-release configmaps and aggregating on release name.", clusterKey, selectNamespace)
					releases = append(releases, GetReleasesFromConfigmaps(clusterKey, selectNamespace)...)

					// Create release list from pod listing
					// log.Printf("Helm list on %s/%s timed out, falling back to listing pods and aggregating on release name.", clusterKey, selectNamespace)
					// releases = append(releases, GetReleasesFromPods(clusterKey, selectNamespace)...)
				}
			}
		}
	}

	// Sort releases in alphabetical order
	slices.SortFunc(releases, func(a, b schemas.ReleaseResponseStruct) int {
		return cmp.Compare(a.Release, b.Release)
	})

	return releases
}

func GetReleasesFromHelm(clusterKey string, selectNamespace string) ([]schemas.ReleaseResponseStruct, error) {

	releases := []schemas.ReleaseResponseStruct{}

	helmClient, err := GetHelmClient(clusterKey, selectNamespace)
	if err != nil {
		return releases, err
	}

	// List releases in all states
	releaselisting := helmAction.NewList(helmClient)
	releaselisting.StateMask = helmAction.ListAll
	rels, err := releaselisting.Run()
	if err != nil {
		return releases, err
	}

	for _, release := range rels {
		releaseKey := fmt.Sprintf("%s_%s", release.Name, clusterKey)

		releases = append(releases, schemas.ReleaseResponseStruct{
			Key:          releaseKey,
			Release:      release.Name,
			Namespace:    release.Namespace,
			Status:       release.Labels["status"],
			Version:      release.Version,
			Cluster:      clusterKey,
			ClusterColor: GetClusterColor(clusterKey),
		})
	}

	return releases, nil
}

// Get Releases from pods
func GetReleasesFromConfigmaps(selectCluster string, selectNamespace string) []schemas.ReleaseResponseStruct {

	releases := []schemas.ReleaseResponseStruct{}

	cluster := conf.KubeClusters[selectCluster]

	clientset, err := GetKubeClient(selectCluster)
	if err != nil {
		log.Printf("Error connecting to kubernetes cluster: %s", err.Error())
		return releases
	}

	// Get configmaps in namespace
	if cluster.TimeoutSeconds == 0 {
		cluster.TimeoutSeconds = conf.KubeConnectionTimeoutSeconds
	}
	timeout := int64(cluster.TimeoutSeconds)
	cm, err := clientset.CoreV1().ConfigMaps(selectNamespace).List(context.TODO(), metav1.ListOptions{
		TimeoutSeconds: &timeout,
		LabelSelector:  "release.silta/config=release-configmap",
	})

	if err != nil {
		log.Printf("Error listing configmaps in namespace %s: %s", selectNamespace, err.Error())
	} else {
		// Iterate cm.Items
		for _, cm := range cm.Items {
			releaseName := cm.Labels["release"]

			if releaseName == "" {
				releaseName = "no-release"
			}

			releaseKey := fmt.Sprintf("%s_%s", releaseName, selectCluster)

			// Iterate releases, add release if it doesn't exist
			releaseFound := false
			for _, release := range releases {
				if release.Key == releaseKey {
					releaseFound = true
				}
			}
			if !releaseFound {
				releases = append(releases, schemas.ReleaseResponseStruct{
					Key:          releaseKey,
					Release:      releaseName,
					Namespace:    cm.ObjectMeta.Namespace,
					Status:       "unknown",
					Version:      -1,
					Cluster:      selectCluster,
					ClusterColor: GetClusterColor(selectCluster),
				})
			}
		}
	}

	return releases
}

// Get Releases from pods
func GetReleasesFromPods(selectCluster string, selectNamespace string) []schemas.ReleaseResponseStruct {
	// Get user pods
	pods := GetKubePods(selectCluster, selectNamespace)
	// releasesFromPods := GetAggregatedFieldFromPods(pods, "Namespace", "Cluster")

	releases := []schemas.ReleaseResponseStruct{}

	for _, pod := range pods {
		releaseName := pod.Resource.ObjectMeta.Labels["release"]
		if releaseName == "" {
			releaseName = "no-release"
		}
		// Iterate releases, add release if it doesn't exist
		releaseKey := fmt.Sprintf("%s_%s", releaseName, pod.Cluster)
		releaseFound := false
		for _, release := range releases {
			if release.Key == releaseKey {
				releaseFound = true
			}
		}
		if !releaseFound {
			releases = append(releases, schemas.ReleaseResponseStruct{
				Key:          releaseKey,
				Release:      releaseName,
				Namespace:    pod.Namespace,
				Status:       "unknown",
				Version:      -1,
				Cluster:      pod.Cluster,
				ClusterColor: GetClusterColor(pod.Cluster),
			})
		}
	}

	return releases
}

// Get releases from helm secrets
func GetReleasesFromHelmSecrets(selectCluster string, selectNamespace string) []schemas.ReleaseResponseStruct {
	releases := []schemas.ReleaseResponseStruct{}

	cluster := conf.KubeClusters[selectCluster]

	clientset, err := GetKubeClient(selectCluster)
	if err != nil {
		log.Printf("Error connecting to kubernetes cluster: %s", err.Error())
		return releases
	}

	if cluster.TimeoutSeconds == 0 {
		cluster.TimeoutSeconds = conf.KubeConnectionTimeoutSeconds
	}

	// This is so slow it times out. Multiply timeout by 10 to get around it.
	timeout := int64(cluster.TimeoutSeconds * 10)

	// Get secrets in namespace and filter by field
	secrets, err := clientset.CoreV1().Secrets(selectNamespace).List(context.TODO(), metav1.ListOptions{
		TimeoutSeconds: &timeout,
		LabelSelector:  "owner=helm",
		FieldSelector:  "type=helm.sh/release.v1",
	})

	if err != nil {
		log.Printf("Error listing secrets in namespace %s: %s", selectNamespace, err.Error())
	} else {
		// Iterate secrets.Items
		for _, secret := range secrets.Items {

			releaseName := secret.Labels["name"]
			if releaseName == "" {
				releaseName = "no-release"
			}

			// Sort secrets by version to get correct status and version.
			// Replacing with static values for now
			releaseStatus := "unknown"
			releaseVersion := -1

			releaseKey := fmt.Sprintf("%s_%s", releaseName, selectCluster)

			// Iterate releases, add release if it doesn't exist
			releaseFound := false
			for _, release := range releases {
				if release.Key == releaseKey {
					releaseFound = true
				}
			}

			if !releaseFound {
				releases = append(releases, schemas.ReleaseResponseStruct{
					Key:          releaseKey,
					Release:      releaseName,
					Namespace:    secret.ObjectMeta.Namespace,
					Status:       releaseStatus,
					Version:      releaseVersion,
					Cluster:      selectCluster,
					ClusterColor: GetClusterColor(selectCluster),
				})
			}
		}
	}

	return releases
}

func GetReleasePods(selectCluster string, selectNamespace string, selectRelease string) []apiPodsResponseStruct {
	pods := []apiPodsResponseStruct{}

	p := GetKubePods(selectCluster, selectNamespace)

	for _, pod := range p {
		// Return pods with a certain release label only
		if pod.Resource.ObjectMeta.Labels["release"] == selectRelease ||
			pod.Resource.ObjectMeta.Labels["app.kubernetes.io/instance"] == selectRelease ||
			(pod.Resource.ObjectMeta.Labels["release"] == "" && pod.Resource.ObjectMeta.Labels["app.kubernetes.io/instance"] == "") {
			pods = append(pods, pod)
		}
	}

	return pods
}

func ResourceBelongsToRelease(labels map[string]string, releaseName string) bool {
	return labels["release"] == releaseName ||
		labels["app.kubernetes.io/instance"] == releaseName
	// (labels["release"] == "" && labels["app.kubernetes.io/instance"] == "")
}

func GetReleaseResources(selectCluster string, selectNamespace string, selectRelease string) []apiResourceResponseStruct {
	resources := []apiResourceResponseStruct{}

	r := GetKubeStatefulsets(selectCluster, selectNamespace)

	for _, resource := range r {
		if ResourceBelongsToRelease(resource.Statefulset.ObjectMeta.Labels, selectRelease) {
			resources = append(resources, resource)
		}
	}

	r = GetKubeDeployments(selectCluster, selectNamespace)

	for _, resource := range r {
		if ResourceBelongsToRelease(resource.Deployment.ObjectMeta.Labels, selectRelease) {
			resources = append(resources, resource)
		}
	}

	return resources
}

func GetContainerLog(selectCluster string, selectNamespace string, selectPod string, selectContainer string) string {
	cluster := conf.KubeClusters[selectCluster]

	CaData, err := base64.StdEncoding.DecodeString(cluster.Cluster.CaData)
	if err != nil {
		log.Printf("Error connecting to cluster: %s", err)
		return "[Dashboard Error] Error getting logs: can't read cluster configuration"
	}

	// Load kubernetes configuration from cluster and user
	config := &rest.Config{
		Host:        cluster.Cluster.Server,
		BearerToken: cluster.User.Token,
		TLSClientConfig: rest.TLSClientConfig{
			CAData: CaData,
		},
	}
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		log.Printf("Error connecting to kubernetes cluster: %s", err.Error())
		return "[Dashboard Error] Error getting logs: can't create cluster clientSet"
	}

	// Get container logs
	req := clientset.CoreV1().Pods(selectNamespace).GetLogs(selectPod, &corev1.PodLogOptions{
		Container:  selectContainer,
		Timestamps: true,
		Follow:     false,
	})

	logStream, err := req.Stream(context.TODO())
	if err != nil {
		log.Printf("Error getting container log: %s", err)
		return "[Dashboard Error] Error getting logs: can't get logs from container"
	}

	// Read logs from stream
	logs, err := ioutil.ReadAll(logStream)
	if err != nil {
		log.Printf("Error reading container log: %s", err)
		return "[Dashboard Error] Error getting logs: can't read logs from container"
	}

	var lines []string

	for _, line := range strings.Split(string(logs), "\n") {

		// if line contains whitespace and is not empty
		if strings.ContainsAny(line, " ") && line != "" {

			// extract first word from line, try to parse it as timestamp
			timestamp, message := strings.SplitN(line, " ", 2)[0], strings.SplitN(line, " ", 2)[1]

			// convert timestamp to time.Time
			t, err := time.Parse("2006-01-02T15:04:05.999999999Z", timestamp)
			if err != nil {
				// add line as is
				lines = append(lines, line)
			} else {
				t = t.Local()
				timestamp = t.Format("2006-01-02 15:04:05")
				lines = append(lines, fmt.Sprintf("%s %s", timestamp, message))
			}
		} else {
			lines = append(lines, line)
		}
	}

	return strings.Join(lines, "\n")
}

func GetEventLog(selectCluster string, selectNamespace string, selectPod string) string {
	events := ""

	cluster := conf.KubeClusters[selectCluster]

	clientset, err := GetKubeClient(selectCluster)
	if err != nil {
		log.Printf("Error connecting to kubernetes cluster: %s", err.Error())
		return "[Dashboard Error] Error getting events: can't create cluster clientSet"
	}

	// Get all events
	if cluster.TimeoutSeconds == 0 {
		cluster.TimeoutSeconds = conf.KubeConnectionTimeoutSeconds
	}
	timeout := int64(cluster.TimeoutSeconds)
	evl, err := clientset.CoreV1().Events(selectNamespace).List(context.TODO(), metav1.ListOptions{
		TimeoutSeconds: &timeout,
		FieldSelector:  "involvedObject.name=" + selectPod,
	})
	if err != nil {
		log.Printf("Error getting events: %s", err)
		return "[Dashboard Error] Error getting events: can't get events from cluster"
	}

	for _, event := range evl.Items {
		events = events + event.GetCreationTimestamp().Format("2006-01-02 15:04:05") + " " + event.Message + "\n"
	}

	return events
}

func KubernetesCreateNamespace(clusterKey string, namespaceName string) error {

	// Get kubernetes clientset
	clientset, err := GetKubeClient(clusterKey)
	if err != nil {
		log.Printf("Error connecting to cluster: %s", err)
		return err
	}

	// Get all namespaces in cluster
	namespaces, err := clientset.CoreV1().Namespaces().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		log.Printf("Error getting namespaces: %s", err)
		return err
	}

	// Check if namespace exists
	namespaceExists := false
	for _, namespace := range namespaces.Items {
		if namespace.Name == namespaceName {
			namespaceExists = true
		}
	}

	// If namespace does not exist, create it
	if !namespaceExists {
		_, err = clientset.CoreV1().Namespaces().Create(context.TODO(), &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: namespaceName,
				Labels: map[string]string{
					// Add label to namespace, used for Silta NetworkPolicy filters
					"name": namespaceName,
				},
			},
		}, metav1.CreateOptions{})
		if err != nil {
			log.Printf("Error creating namespace: %s", err)
			return err
		}
	}

	return nil
}

func GetClusterColor(cluster string) string {
	if conf.KubeClusters[cluster].LabelColor != "" {
		return conf.KubeClusters[cluster].LabelColor
	}
	// Default color
	return "geekblue"
}

// calculate resource limit based on input value and multiplier
func CalculateLimit(value string, multiplier float64) (limit string, err error) {

	// Validate value
	_, err = resource.ParseQuantity(value)
	if err != nil {
		log.Printf("error parsing quantity: %s\n", err)
		return "", err
	}

	// The API server restricts quantities of extended resources to whole numbers.
	// Examples of valid quantities are 3, 3000m and 3Ki. Examples of invalid quantities are 0.5 and 1500m.
	reg, err := regexp.Compile("[^0-9-]+")
	if err != nil {
		panic(err)
	}

	numericStr := reg.ReplaceAllString(value, "")
	suffix := ""

	// extract suffix when present
	if numericStr != value {
		suffix = value[len(numericStr):]
	}

	// convert to float64
	numeric, err := strconv.ParseFloat(numericStr, 64)
	if err != nil {
		log.Println("error parsing float:", err)
		return
	}

	// multiply by multiplier and round up to remove decimal places
	// rounded := math.Round(numeric * multiplier)

	// rounded_quantified, err := resource.ParseQuantity(fmt.Sprintf("%v%v", rounded, suffix))
	// if err != nil {
	// 	log.println("error parsing quantity:", err)
	// 	return
	// }

	// return rounded_quantified.String(), nil

	return fmt.Sprintf("%v%v", numeric*multiplier, suffix), nil
}
