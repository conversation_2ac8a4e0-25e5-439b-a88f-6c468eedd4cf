package services

import (
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/wunderio/silta-dashboard/backend/cli"
	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/schemas"

	"k8s.io/client-go/rest"

	helmAction "helm.sh/helm/v3/pkg/action"
)

// UninstallRelease removes a Helm release and related resources
// Note: namespace is inferred from the helmclient.Options struct but set here for kubernetes clientset actions
func UninstallHelmRelease(selectCluster string, selectNamespace string, selectRelease string) error {

	clientset, err := GetKubeClient(selectCluster)
	if err != nil {
		return err
	}

	helmClient, err := GetHelmClient(selectCluster, selectNamespace)
	if err != nil {
		return err
	}

	err = cli.UninstallHelmRelease(clientset, helmClient, selectNamespace, selectRelease, true)
	if err != nil {
		return err
	}

	return nil
}

func GetReleaseChartName(selectCluster string, selectNamespace string, selectRelease string) string {
	release, err := GetReleaseInfo(selectCluster, selectNamespace, selectRelease)

	if err != nil {
		log.Printf("Error getting release info: %s\n", err)
		return ""
	}

	// Get chart name
	return release.Chart.Name()
}

func GetHelmClient(clusterKey string, selectNamespace string) (*helmAction.Configuration, error) {

	cluster := conf.KubeClusters[clusterKey]

	CaData, err := base64.StdEncoding.DecodeString(cluster.Cluster.CaData)
	if err != nil {
		// log.Println("Error decoding config.Cluster.CAData:", err)
		// continue
		return nil, err
	}

	if cluster.TimeoutSeconds == 0 {
		cluster.TimeoutSeconds = conf.KubeConnectionTimeoutSeconds
	}

	restConfig := &rest.Config{
		Host:        cluster.Cluster.Server,
		BearerToken: cluster.User.Token,
		TLSClientConfig: rest.TLSClientConfig{
			CAData: CaData,
		},
		Timeout: time.Duration(cluster.TimeoutSeconds) * time.Second,
	}

	// Use staticRESTClientGetter to ensure CAData is used
	restGetter := NewStaticRESTClientGetter(restConfig, selectNamespace)

	actionConfig := new(helmAction.Configuration)
	if err := actionConfig.Init(restGetter, selectNamespace, os.Getenv("HELM_DRIVER"), log.Printf); err != nil {
		// log.Printf("Failed to initialize Helm configuration: %s", err)
		return nil, err
	}

	return actionConfig, nil
}

func GetHelmValues(selectCluster string, selectNamespace string, selectRelease string) (map[string]interface{}, error) {

	helmClient, err := GetHelmClient(selectCluster, selectNamespace)
	if err != nil {
		return nil, err
	}
	valuesClient := helmAction.NewGetValues(helmClient)
	valuesClient.AllValues = true
	values, err := valuesClient.Run(selectRelease)
	if err != nil {
		return nil, err
	}

	return values, nil
}

func GetReleaseInfo(selectCluster string, selectNamespace string, selectRelease string) (schemas.ReleaseResponseStruct, error) {

	helmClient, err := GetHelmClient(selectCluster, selectNamespace)
	if err != nil {
		return schemas.ReleaseResponseStruct{}, err
	}

	infoClient := helmAction.NewGet(helmClient)
	helmReleaseInfo, err := infoClient.Run(selectRelease)
	if err != nil {
		return schemas.ReleaseResponseStruct{}, err
	}

	valuesClient := helmAction.NewGetValues(helmClient)
	valuesClient.AllValues = true
	releaseValues, err := valuesClient.Run(selectRelease)
	if err != nil {
		return schemas.ReleaseResponseStruct{}, err
	}

	releaseKey := fmt.Sprintf("%s_%s", selectRelease, selectCluster)
	release := schemas.ReleaseResponseStruct{
		Key:          releaseKey,
		Release:      selectRelease,
		Namespace:    selectNamespace,
		Status:       helmReleaseInfo.Info.Status.String(),
		Version:      helmReleaseInfo.Version,
		Cluster:      selectCluster,
		ClusterColor: GetClusterColor(selectCluster),
		Info:         helmReleaseInfo.Info,
		Chart:        helmReleaseInfo.Chart,
		Values:       releaseValues,
	}

	return release, nil
}
