package services

import (
	"context"
	"net/http"
	"strconv"

	"github.com/bradleyfalzon/ghinstallation/v2"
	"github.com/google/go-github/v49/github"
	"github.com/wunderio/silta-dashboard/backend/conf"
)

type GithubUser struct {
	ID              int64  `json:"id"`
	Login           string `json:"login"`
	AvatarURL       string `json:"avatar_url"`
	IsGithubOrgUser bool   `json:"is_github_org_user"`
}

// GetGithubOrgCollaborators returns a list of Github organization collaborators.
// Scope can be "internal" or "all".
// Note: This does not return collaborator access (pull, push, admin, etc), for that we'd need to iterate all repositories.
func GetGithubOrgCollaborators(githubOrg string, scope string) ([]GithubUser, error) {

	var collaborators []GithubUser

	githubAppID, err := strconv.ParseInt(conf.GithubAppID, 10, 64)
	if err != nil {
		return nil, err
	}
	githubAppInstallationID, err := strconv.ParseInt(conf.GithubAppInstallationID, 10, 64)
	if err != nil {
		return nil, err
	}

	itr, err := ghinstallation.New(http.DefaultTransport, githubAppID, githubAppInstallationID, []byte(conf.GithubAppPrivateKey))

	if err != nil {
		return nil, err
	}

	// Use installation transport with client.
	client := github.NewClient(&http.Client{Transport: itr})

	collaborators, err = GetGithubOrgMembers(githubOrg, "all")
	if err != nil {
		return nil, err
	}

	// If scope is all, add outside collaborators. The difference is that github "all" members does not include outside collaborators.
	if scope == "all" {
		// List outside collaborators
		listOptions := &github.ListOutsideCollaboratorsOptions{
			ListOptions: github.ListOptions{
				PerPage: 100,
			},
		}
		for {
			u, listResponse, err := client.Organizations.ListOutsideCollaborators(context.TODO(), githubOrg, listOptions)
			if err != nil {
				return nil, err
			}
			for _, user := range u {
				collaborators = append(collaborators, GithubUser{
					ID:              *user.ID,
					Login:           *user.Login,
					AvatarURL:       *user.AvatarURL,
					IsGithubOrgUser: false,
				})
			}
			// Break if no more pages
			if listResponse.NextPage == 0 {
				break
			}
			listOptions.Page = listResponse.NextPage
		}
	}

	return collaborators, nil
}

// GetGithubOrgCollaboratorScope returns the scope of a Github user in a Github organization.
// Returned scope can be "internal", "external" or "none".
// Note: This does not return collaborator access (pull, push, admin, etc), for that we'd need to iterate all repositories.
func GetGithubOrgCollaboratorScope(githubOrg string, githubUserID *int64) string {
	collaborators, err := GetGithubOrgCollaborators(githubOrg, "all")
	if err != nil {
		return "none"
	}

	for _, collaborator := range collaborators {
		if collaborator.ID == *githubUserID {
			if collaborator.IsGithubOrgUser {
				return "internal"
			} else {
				return "external"
			}
		}
	}

	return "none"
}

func GetGithubOrgMembers(githubOrg string, role string) ([]GithubUser, error) {

	var members []GithubUser

	githubAppID, err := strconv.ParseInt(conf.GithubAppID, 10, 64)
	if err != nil {
		return nil, err
	}
	githubAppInstallationID, err := strconv.ParseInt(conf.GithubAppInstallationID, 10, 64)
	if err != nil {
		return nil, err
	}

	itr, err := ghinstallation.New(http.DefaultTransport, githubAppID, githubAppInstallationID, []byte(conf.GithubAppPrivateKey))

	if err != nil {
		return nil, err
	}

	// Use installation transport with client.
	client := github.NewClient(&http.Client{Transport: itr})

	listOptions := &github.ListMembersOptions{
		Role: role,
		ListOptions: github.ListOptions{
			PerPage: 100,
		},
	}
	for {
		u, listResponse, err := client.Organizations.ListMembers(context.TODO(), githubOrg, listOptions)
		if err != nil {
			return nil, err
		}
		for _, user := range u {
			members = append(members, GithubUser{
				ID:              *user.ID,
				Login:           *user.Login,
				AvatarURL:       *user.AvatarURL,
				IsGithubOrgUser: true,
			})
		}
		// Break if no more pages
		if listResponse.NextPage == 0 {
			break
		}
		listOptions.Page = listResponse.NextPage
	}

	return members, nil
}

// Return all Github repositories for a given organization
func GetAllGitRepositories(githubOrg string) ([]*github.Repository, error) {

	var repositories []*github.Repository

	githubAppID, err := strconv.ParseInt(conf.GithubAppID, 10, 64)
	if err != nil {
		return nil, err
	}
	githubAppInstallationID, err := strconv.ParseInt(conf.GithubAppInstallationID, 10, 64)
	if err != nil {
		return nil, err
	}

	itr, err := ghinstallation.New(http.DefaultTransport, githubAppID, githubAppInstallationID, []byte(conf.GithubAppPrivateKey))

	if err != nil {
		return nil, err
	}

	// Use installation transport with client.
	client := github.NewClient(&http.Client{Transport: itr})

	opt := &github.RepositoryListByOrgOptions{
		Type: "all",
		ListOptions: github.ListOptions{
			PerPage: 100,
		},
	}
	// get all pages of results
	for {
		repos, resp, err := client.Repositories.ListByOrg(context.TODO(), githubOrg, opt)
		if err != nil {
			return nil, err
		}
		repositories = append(repositories, repos...)

		if resp.NextPage == 0 {
			break
		}
		opt.Page = resp.NextPage
	}

	return repositories, nil
}

// Get all collaborators for a given repository
// scope: "internal", "all"
func GetAllGitRepositoryCollaborators(githubOrg string, repositoryName string, scope string) ([]*github.User, error) {

	var users []*github.User

	githubAppID, err := strconv.ParseInt(conf.GithubAppID, 10, 64)
	if err != nil {
		return nil, err
	}
	githubAppInstallationID, err := strconv.ParseInt(conf.GithubAppInstallationID, 10, 64)
	if err != nil {
		return nil, err
	}

	itr, err := ghinstallation.New(http.DefaultTransport, githubAppID, githubAppInstallationID, []byte(conf.GithubAppPrivateKey))

	if err != nil {
		return nil, err
	}

	// Use installation transport with client.
	client := github.NewClient(&http.Client{Transport: itr})

	var internalUsers []*github.User

	if scope == "internal" {
		// Get organisation members. This is required to filter out external users as there's no special affiliation for internal users
		listMembersOptions := &github.ListMembersOptions{
			Role: "all",
			ListOptions: github.ListOptions{
				PerPage: 100,
			},
		}

		// Get all pages of results
		for {
			u, listResponse, err := client.Organizations.ListMembers(context.TODO(), githubOrg, listMembersOptions)
			if err != nil {
				return nil, err
			}
			internalUsers = append(internalUsers, u...)
			if listResponse.NextPage == 0 {
				break
			}
			listMembersOptions.Page = listResponse.NextPage
		}

	}

	opt := &github.ListCollaboratorsOptions{
		ListOptions: github.ListOptions{
			PerPage: 100,
		},
	}

	// Get all pages of results
	for {
		repoUsers, resp, err := client.Repositories.ListCollaborators(context.TODO(), githubOrg, repositoryName, opt)
		if err != nil {
			return nil, err
		}
		for _, user := range repoUsers {
			if scope == "internal" {
				// Check if user is internal, only add if they are
				for _, internalUser := range internalUsers {
					if *internalUser.ID == *user.ID {
						users = append(users, user)
					}
				}
			} else {
				users = append(users, user)
			}
		}

		if resp.NextPage == 0 {
			break
		}
		opt.Page = resp.NextPage
	}

	return users, nil
}
