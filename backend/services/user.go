package services

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/google/go-github/v49/github"
	"github.com/google/uuid"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/db"
	"github.com/wunderio/silta-dashboard/backend/schemas"

	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/oauth2"
)

// Create session access token
func GenerateAccessToken() (token string) {
	sessionToken, _ := uuid.NewRandom()
	return sessionToken.String()
}

// CreatePasswordUserSession creates a session for the password user
func CreatePasswordUserSession() (string, error) {
	// Create a new user object
	user := &schemas.User{
		Name: "local-user",
	}

	// Create session token
	sessionToken := GenerateAccessToken()
	user.SessionToken = sessionToken
	user.SessionExpires = time.Now().Add(conf.SessionLifetime).Format(time.RFC3339)

	// Create user in database
	_, err := db.Users.InsertOne(context.TODO(), user)
	if err != nil {
		return "", err
	}

	return sessionToken, nil
}

// Validate user session
func ValidateSession(token string) bool {

	// Select number of users with matching session token
	filter := bson.M{"sessionToken": token}
	count, err := db.Users.CountDocuments(context.TODO(), filter)
	if err != nil {
		log.Printf("[ValidateSession] Error: %v", err)
		return false
	}

	// If there is not exactly one user with matching session token, return false
	if count != 1 {
		return false
	}

	// Load user from database
	user, err := GetUserBySessionToken(token)
	if err != nil {
		log.Printf("[ValidateSession] Error: %v", err)
		return false
	}

	// Check if session has expired
	if user.SessionExpires < time.Now().Format(time.RFC3339) {
		return false
	}

	// Make sure user has github ID
	ghId, err := GetGithubID(token)
	if err != nil {
		log.Printf("[ValidateSession] Error: %v", err)
		return false
	}

	// Final check for github ID
	if ghId != nil {
		return true
	}

	// If password authentication is enabled and user is the password user, return true
	if conf.PasswordLoginEnabled && user.Name == "local-user" {
		return true
	}

	return false
}

// Check if user has access to namespace
func ValidateNamespaceAccess(sessionToken, namespace string) bool {

	// Load user from database
	user, err := GetUserBySessionToken(sessionToken)
	if err != nil {
		log.Printf("[ValidateSession] Error: %v", err)
		return false
	}
	if conf.PasswordLoginEnabled && user.Name == "local-user" {
		// Password user has access to all namespaces
		return true
	}

	if conf.GithubLoginEnabled {
		// Load user repository list
		repositories, _ := GetUserGitRepositories(conf.GithubOrg, sessionToken)
		for _, repository := range repositories {
			// if repository org + repository name matches namespace, return true.
			// We match on both org and name to avoid false positives with forks
			if *repository.FullName == fmt.Sprintf("%s/%s", conf.GithubOrg, namespace) {
				return true
			}
		}
	}
	return false
}

func GetUserBySessionToken(sessionToken string) (*schemas.User, error) {

	if sessionToken == "" {
		return nil, errors.New("session token is empty")
	}

	filter := bson.M{"sessionToken": sessionToken}
	var user schemas.User
	err := db.Users.FindOne(context.TODO(), filter).Decode(&user)

	if err != nil {
		return nil, err
	}

	return &user, nil
}

// Validate user session
func GetGithubID(sessionToken string) (*int64, error) {

	// Get user using session token
	filter := bson.M{"sessionToken": sessionToken}
	var user schemas.User
	err := db.Users.FindOne(context.TODO(), filter).Decode(&user)
	if err != nil {
		return nil, err
	}
	return user.Github.GithubId, nil
}

func GetUserGitRepositories(githubOrg, sessionToken string) ([]*github.Repository, error) {

	var repositories []*github.Repository

	// Get user using session token
	user, err := GetUserBySessionToken(sessionToken)
	if err != nil {
		return nil, err
	}

	userCache, err := GetUserCache(user.Uuid, "Repositories")
	if err == nil {
		return userCache.Repositories, nil
	} else {

		// Cache is empty, fetch from Github
		ctx := context.Background()
		ts := oauth2.StaticTokenSource(
			&oauth2.Token{AccessToken: user.Github.ApiAccessToken},
		)
		tc := oauth2.NewClient(ctx, ts)

		client := github.NewClient(tc)

		// TODO:MAYBE Change repository listing to use GH App credentials to pull all repositories
		//            and cache repo list for all users, iterate on the cache to find the user's repos.
		//            It will be faster and use less GH API calls when used for multiple users.
		//            This might need a cache invalidation button to reload user permissions
		//            as relogging would not be enough anymore.

		// list all repositories for the authenticated user
		opt := &github.RepositoryListByOrgOptions{
			Type: "all",
			ListOptions: github.ListOptions{
				PerPage: 100,
			},
		}
		for {
			repos, resp, err := client.Repositories.ListByOrg(ctx, githubOrg, opt)
			if err != nil {
				return nil, err
			}
			for _, repo := range repos {
				// Only allow repositories with push permission
				// map[admin:false maintain:false pull:true push:false triage:false]
				if repo.Permissions != nil {
					if repo.Permissions["push"] {
						repositories = append(repositories, repo)
					}
				}
			}
			if resp.NextPage == 0 {
				break
			}
			opt.Page = resp.NextPage
		}

		// set cache expiration to 1 hour
		cacheExpires := time.Now().Add(time.Hour).Format(time.RFC3339)
		newUserCache := schemas.UserCache{
			Uuid:               user.Uuid,
			GithubId:           user.Github.GithubId,
			RepositoriesExpire: cacheExpires,
			Repositories:       repositories,
		}
		err := SetUserCache(user.Uuid, "Repositories", newUserCache)
		if err != nil {
			log.Printf("[GetUserGitRepositories] Cache entry creation error: %s\n", err)
		}
	}

	return repositories, nil
}

func GetUserNamespaces(sessionToken string) []apiNamespaceResponseStruct {
	namespaces := []apiNamespaceResponseStruct{}

	if conf.PasswordLoginEnabled {
		// Display all namespaces for password user
		// Get all namespaces in all clusters
		namespacesAndClusters := GetNamespaces()
		for _, namespace := range namespacesAndClusters {
			namespaces = append(namespaces, apiNamespaceResponseStruct{
				Name:     namespace.Name,
				Clusters: namespace.Clusters,
			})
		}

	} else if conf.GithubLoginEnabled {

		// Get user repositories
		repositories, err := GetUserGitRepositories(conf.GithubOrg, sessionToken)
		if err != nil {
			log.Printf("[GetUserNamespaces] Error loading user repositories: %v", err)
		}

		// Get namespaces in all clusters and match with user repositories
		namespacesAndClusters := GetNamespaces()
		for _, namespace := range namespacesAndClusters {
			// If namespace is in user repositories, add it to the list
			for _, repository := range repositories {
				if *repository.FullName == fmt.Sprintf("%s/%s", conf.GithubOrg, namespace.Name) {
					namespaces = append(namespaces, apiNamespaceResponseStruct{
						Name:     namespace.Name,
						Clusters: namespace.Clusters,
					})
				}
			}
		}
	}

	// Option #2: Get namespaces from pods, only displays namespaces with pods
	// Get user pods
	// cluster := ""
	// namespace := ""
	// pods := GetKubePods(cluster, namespace)
	// namespacesFromPods := GetAggregatedFieldFromPods(pods, "Namespace", "Cluster")
	// for namespace, clusters := range namespacesFromPods {
	// 	// If namespace is in user repositories, add it to the list
	// 	for _, repository := range repositories {
	// 		if *repository.FullName == fmt.Sprintf("%s/%s", conf.GithubOrg, namespace) {
	// 			namespaces = append(namespaces, apiNamespaceResponseStruct{
	// 				Name:     namespace,
	// 				Clusters: clusters,
	// 			})
	// 		}
	// 	}
	// }

	return namespaces
}

// Create unique UUID, make sure there's no such uuid in database
func CreateUserUUID() (string, error) {
	// Loop until unique UUID is found
	for {
		// Create UUID
		uuid := uuid.NewString()
		// Select number of users with matching session token
		filter := bson.M{"uuid": uuid}
		count, err := db.Users.CountDocuments(context.TODO(), filter)
		if err != nil {
			log.Printf("[CreateUserUUID] uuid generation error: %v", err)
			return "", err
		}
		if count == 0 {
			return uuid, nil
		}
	}
}

// Load user cache from database
func GetUserCache(uuid string, field string) (*schemas.UserCache, error) {

	if uuid == "" {
		return nil, errors.New("invalid uuid")
	}

	// Check cache table for user cache entry
	filter := bson.M{"uuid": uuid}
	var userCache schemas.UserCache
	err := db.UserCache.FindOne(context.TODO(), filter).Decode(&userCache)
	if err != nil {
		return nil, err
	}

	if field == "Repositories" {
		if userCache.Repositories == nil {
			return nil, errors.New("cache entry not found")
		}
		// Check cacheExpires time, if it's in the past, delete cache entry
		if userCache.RepositoriesExpire < time.Now().Format(time.RFC3339) {

			// Delete Repositories and repositoriesExpire values from cache
			filter := bson.M{"uuid": uuid}
			update := bson.M{"$unset": bson.M{"repositories": "", "repositoriesExpire": ""}}
			_, err := db.UserCache.UpdateOne(context.TODO(), filter, update)
			if err != nil {
				log.Printf("[GetUserCache] Error deleting cache entry: %s\n", err)
			}
			// And return that there's no cache	entry
			return nil, errors.New("cache entry expired")
		}
	}

	return &userCache, nil
}

// Set or update user cache in database
func SetUserCache(uuid string, field string, userCache schemas.UserCache) error {

	if uuid == "" {
		return errors.New("invalid uuid")
	}

	CreateUserCache(uuid)

	// Try to find existing cache by user uuid
	filter := bson.M{"uuid": uuid}
	var getUserCache schemas.UserCache
	err := db.UserCache.FindOne(context.TODO(), filter).Decode(&getUserCache)
	if err != nil {
		// Create user cache entry
		_, err = db.UserCache.InsertOne(context.TODO(), userCache)
		if err != nil {
			log.Printf("[SetUserCache] User cache creation error: %s\n", err)
			return err
		}
	} else {
		// Update only the field that was passed
		if field == "Repositories" {
			getUserCache.GithubId = userCache.GithubId
			getUserCache.Repositories = userCache.Repositories
			getUserCache.RepositoriesExpire = userCache.RepositoriesExpire
		}

		// Update user cache entry
		filter := bson.M{"uuid": uuid}
		_, err = db.UserCache.UpdateOne(context.TODO(), filter, bson.M{"$set": getUserCache})
		if err != nil {
			log.Printf("[SetUserCache] User cache update error: %s\n", err)
			return err
		}
	}

	return nil
}

func SetUserNamespaceCache(uuid string, cacheData schemas.NamespaceCache) error {

	if uuid == "" {
		return errors.New("invalid uuid")
	}

	// Make sure there's a user cache entry
	CreateUserCache(uuid)

	// Remove existing cache entry for this namespace
	DeleteUserNamespaceCache(uuid, cacheData.Name)

	// Add new cache entry
	filter := bson.M{"uuid": uuid}
	update := bson.M{
		"$push": bson.M{"namespaces": cacheData},
	}
	_, err := db.UserCache.UpdateOne(context.TODO(), filter, update)
	if err != nil {
		log.Printf("[SetUserReleasesCache] Error updating release list cache user cache: %s\n", err)
		return err
	}

	return nil
}

func GetUserNamespaceCache(uuid string, namespace string) ([]schemas.ReleaseResponseStruct, error) {

	if uuid == "" {
		return nil, errors.New("invalid uuid")
	}

	// Get user cache entry
	filter := bson.M{"uuid": uuid}
	var userCache schemas.UserCache
	err := db.UserCache.FindOne(context.TODO(), filter).Decode(&userCache)
	if err != nil {
		log.Printf("[GetUserNamespaceCache] Error getting user cache: %s\n", err)
		return nil, err
	}

	// Extract namespace cache entry
	// Probaby a better way to do this would be using projection
	for _, namespaceCache := range userCache.Namespaces {

		if namespaceCache.Name == namespace {

			// Check cacheExpires time, if it's in the past, delete cache entry
			if namespaceCache.CacheExpire < time.Now().Format(time.RFC3339) {

				DeleteUserNamespaceCache(uuid, namespace)

				// And return that there's no cache	entry
				return nil, errors.New("cache entry expired")
			}

			return namespaceCache.Releases, nil
		}
	}

	return nil, errors.New("cache entry not found")

}

// Delete user cache entry for a namespace
func DeleteUserNamespaceCache(uuid string, namespace string) error {

	if uuid == "" {
		return errors.New("invalid uuid")
	}

	// Delete Releases and releasesExpire values from cache
	filter := bson.M{"uuid": uuid}
	update := bson.M{"$pull": bson.M{"namespaces": bson.M{"name": namespace}}}
	_, err := db.UserCache.UpdateOne(context.TODO(), filter, update)
	if err != nil {
		log.Printf("[GetUserNamespaceCache] Error deleting cache entry: %s\n", err)
		return err
	}
	return nil
}

func CreateUserCache(uuid string) error {

	if uuid == "" {
		return errors.New("invalid uuid")
	}

	// Select number of cache entries for user
	filter := bson.M{"uuid": uuid}
	count, err := db.UserCache.CountDocuments(context.TODO(), filter)
	if err != nil {
		log.Printf("[CreateUserCache] Error creating user cache entry: %v\n", err)
		return err
	}

	if count == 0 {
		// Create user cache entry
		userCache := schemas.UserCache{
			Uuid:         uuid,
			Repositories: []*github.Repository{},
			Namespaces:   []schemas.NamespaceCache{},
		}

		_, err = db.UserCache.InsertOne(context.TODO(), userCache)
		if err != nil {
			log.Printf("[CreateUserCache] User cache creation error: %s\n", err)
			return err
		}
	}

	return nil
}

// Delete user cache entries from database
func RemoveUserCache(uuid string) error {

	if uuid == "" {
		return errors.New("invalid uuid")
	}

	// Delete all user cache entries (should be one, but just in case)
	filter := bson.M{"uuid": uuid}
	_, err := db.UserCache.DeleteMany(context.TODO(), filter)
	if err != nil {
		log.Printf("[RemoveUserCache] User cache deletion error: %s\n", err)
		return err
	}

	return nil
}

// Return a map of all github users and their google email addresses
func GetGhToGmailMappings() (map[string]string, error) {

	filter := bson.M{"google.email": bson.M{"$exists": true, "$ne": ""}}

	ghToGmail := make(map[string]string)

	cur, err := db.Users.Find(context.TODO(), filter)
	if err != nil {
		log.Printf("[GetGhToGmailMappings] Error getting users: %s\n", err)
		return nil, err
	}
	defer cur.Close(context.Background())

	for cur.Next(context.Background()) {
		var user schemas.User
		err := cur.Decode(&user)
		if err != nil {
			log.Printf("[GetGhToGmailMappings] Error decoding user: %s\n", err)
			return nil, err
		}

		// Create map of github username to google email address
		githubIdStr := strconv.FormatInt(*user.Github.GithubId, 10)
		ghToGmail[githubIdStr] = user.Google.Email
	}

	return ghToGmail, nil
}
