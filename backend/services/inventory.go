package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/db"
	"github.com/wunderio/silta-dashboard/backend/schemas"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func CreateClusterInventory() {
	for clusterKey, cluster := range conf.KubeClusters {
		if !cluster.Enabled {
			continue
		}
		getClusterInventory(clusterKey)
	}
}

func getClusterInventory(clusterKey string) error {

	// TODO:MAYBE silta cli should extract php and nginx versions from dockerfiles, store them in release values and we can read them from there

	releases := []schemas.ReleaseInventory{}

	// Connect to cluster
	clientset, err := GetKubeClient(clusterKey)
	if err != nil {
		log.Printf("Error connecting to cluster: %s", err)
		return err
	}

	// Remove existing inventory data for this cluster before adding new entries
	log.Printf("Removing existing inventory data for cluster %s", clusterKey)
	err = removeClusterInventoryData(clusterKey)
	if err != nil {
		log.Printf("Error removing existing inventory data: %s", err)
		// Continue anyway - we still want to add new data
	}

	// Get Kubernetes version
	versionInfo, err := clientset.Discovery().ServerVersion()
	if err != nil {
		log.Printf("Error getting Kubernetes version: %s", err)
	} else {
		// Store cluster inventory with Kubernetes version
		clusterInventory := schemas.ClusterInventory{
			KubernetesVersion: versionInfo.GitVersion,
			Updated:           time.Now().Format(time.RFC3339),
		}
		err = storeClusterInventory(clusterKey, clusterInventory)
		if err != nil {
			log.Printf("Error storing cluster inventory: %s", err)
		}
	}

	var namespaces *v1.NamespaceList

	// silta-cluster has no release-info configmap, load from helm
	releaseInventory, err := GetReleaseInventory(clusterKey, "silta-cluster", "silta-cluster")
	if err != nil {
		log.Printf("Error getting release inventory: %s", err)
	} else {
		releases = append(releases, releaseInventory)
	}

	// Get components (helm release information) only if it's enabled in cluster config
	if conf.KubeClusters[clusterKey].Inventory.Components.Enabled {

		// Get all namespaces in cluster
		namespaces, err = clientset.CoreV1().Namespaces().List(context.TODO(), metav1.ListOptions{})

		// Get helm releases in all namespaces, get values, chart name, version, deployment time and status
		for _, namespace := range namespaces.Items {
			//  Load release info for each release individually to avoid helm timeout
			rels := GetReleasesFromConfigmaps(clusterKey, namespace.Name)
			if err != nil {
				log.Printf("Error getting releases from helm: %s", err)
				continue
			}

			for _, release := range rels {
				releaseInventory, err := GetReleaseInventory(clusterKey, release.Namespace, release.Release)
				if err != nil {
					log.Printf("Error getting release inventory: %s", err)
					continue
				}
				releases = append(releases, releaseInventory)
			}
		}
	}

	// Store releases in MongoDB
	log.Printf("Found %d releases in %s", len(releases), clusterKey)
	for _, release := range releases {

		// Debug
		// fmt.Printf("\n-----\nRelease: %s\t%s\t%s\t%s\t%s\tChart: %s\tVersion: %d\tStatus: %s\n",
		// 	release.Cluster,
		// 	release.Namespace,
		// 	release.Name,
		// 	release.Chart,
		// 	release.Deployed,
		// 	release.Chart,
		// 	release.Version,
		// 	release.Status)

		// for _, component := range release.Components {
		// 	fmt.Printf("\tComponent: %s\tEnabled: %t\tImage: %s\tParsedVersion: %s\tSource: %s\n",
		// 		component.Name,
		// 		component.Enabled,
		// 		component.Image,
		// 		component.ParsedVersion,
		// 		component.Source)
		// }

		// Store release in MongoDB
		err = storeReleaseInventory(release)
		if err != nil {
			log.Printf("Error storing release inventory: %s", err)
		}
	}

	// Get images only if it's enabled in cluster config
	if conf.KubeClusters[clusterKey].Inventory.Images.Enabled {

		// Make a list of images, include namespace, resource type (statefulset, deployment, pod, etc.), container name and image
		images := []schemas.ImageInventory{}
		for _, namespace := range namespaces.Items {

			images = append(images, getImageInventoryFromStatefulsets(clusterKey, namespace.Name)...)
			images = append(images, getImageInventoryFromDeployments(clusterKey, namespace.Name)...)
			images = append(images, getImageInventoryFromDaemonsets(clusterKey, namespace.Name)...)
			images = append(images, getImageInventoryFromJobs(clusterKey, namespace.Name)...)
			images = append(images, getImageInventoryFromCronjobs(clusterKey, namespace.Name)...)
		}
		log.Printf("Found %d images in %s", len(images), clusterKey)

		// Store images in MongoDB
		for _, image := range images {
			err = storeImageInventory(image)
			if err != nil {
				log.Printf("Error storing image inventory: %s", err)
			}
		}
	}

	return nil
}

func getImageInventoryFromStatefulsets(clusterKey string, namespace string) []schemas.ImageInventory {
	images := []schemas.ImageInventory{}
	statefulsets := GetKubeStatefulsets(clusterKey, namespace)
	for _, statefulset := range statefulsets {
		for _, container := range statefulset.Statefulset.Spec.Template.Spec.Containers {
			images = append(images, schemas.ImageInventory{
				Updated:           time.Now().Format(time.RFC3339),
				Cluster:           clusterKey,
				Namespace:         statefulset.Namespace,
				ResourceName:      statefulset.Name,
				ParsedReleaseName: parseReleaseNameFromMetadata(statefulset.Statefulset.ObjectMeta),
				Url:               container.Image,
				Image:             parseImagePathFromImage(container.Image),
				Tag:               parseTagFromImage(container.Image),
				Source:            "statefulset",
			})
		}
	}
	return images
}

func getImageInventoryFromDeployments(clusterKey string, namespace string) []schemas.ImageInventory {
	images := []schemas.ImageInventory{}
	deployments := GetKubeDeployments(clusterKey, namespace)
	for _, deployment := range deployments {
		for _, container := range deployment.Deployment.Spec.Template.Spec.Containers {
			images = append(images, schemas.ImageInventory{
				Updated:           time.Now().Format(time.RFC3339),
				Cluster:           clusterKey,
				Namespace:         deployment.Namespace,
				ResourceName:      deployment.Name,
				ParsedReleaseName: parseReleaseNameFromMetadata(deployment.Deployment.ObjectMeta),
				Url:               container.Image,
				Image:             parseImagePathFromImage(container.Image),
				Tag:               parseTagFromImage(container.Image),
				Source:            "deployment",
			})
		}
	}
	return images
}

func getImageInventoryFromDaemonsets(clusterKey string, namespace string) []schemas.ImageInventory {
	images := []schemas.ImageInventory{}
	daemonsets := GetKubeDaemonsets(clusterKey, namespace)
	for _, daemonset := range daemonsets {
		for _, container := range daemonset.Daemonset.Spec.Template.Spec.Containers {
			images = append(images, schemas.ImageInventory{
				Updated:           time.Now().Format(time.RFC3339),
				Cluster:           clusterKey,
				Namespace:         daemonset.Namespace,
				ResourceName:      daemonset.Name,
				ParsedReleaseName: parseReleaseNameFromMetadata(daemonset.Daemonset.ObjectMeta),
				Url:               container.Image,
				Image:             parseImagePathFromImage(container.Image),
				Tag:               parseTagFromImage(container.Image),
				Source:            "daemonset",
			})
		}
	}
	return images
}

func getImageInventoryFromJobs(clusterKey string, namespace string) []schemas.ImageInventory {
	images := []schemas.ImageInventory{}
	jobs := GetKubeJobs(clusterKey, namespace)
	for _, job := range jobs {
		for _, container := range job.Job.Spec.Template.Spec.Containers {
			images = append(images, schemas.ImageInventory{
				Updated:           time.Now().Format(time.RFC3339),
				Cluster:           clusterKey,
				Namespace:         job.Namespace,
				ResourceName:      job.Name,
				ParsedReleaseName: parseReleaseNameFromMetadata(job.Job.ObjectMeta),
				Url:               container.Image,
				Image:             parseImagePathFromImage(container.Image),
				Tag:               parseTagFromImage(container.Image),
				Source:            "job",
			})
		}
	}
	return images
}

func getImageInventoryFromCronjobs(clusterKey string, namespace string) []schemas.ImageInventory {
	images := []schemas.ImageInventory{}
	cronjobs := GetKubeCronjobs(clusterKey, namespace)
	for _, cronjob := range cronjobs {
		for _, container := range cronjob.Cronjob.Spec.JobTemplate.Spec.Template.Spec.Containers {
			images = append(images, schemas.ImageInventory{
				Updated:           time.Now().Format(time.RFC3339),
				Cluster:           clusterKey,
				Namespace:         cronjob.Namespace,
				ResourceName:      cronjob.Name,
				ParsedReleaseName: parseReleaseNameFromMetadata(cronjob.Cronjob.ObjectMeta),
				Url:               container.Image,
				Image:             parseImagePathFromImage(container.Image),
				Tag:               parseTagFromImage(container.Image),
				Source:            "cronjob",
			})
		}
	}
	return images
}

func GetReleaseInventory(clusterKey string, releaseNamespace string, releaseName string) (schemas.ReleaseInventory, error) {
	// TODO: add chart name to silta-release configmap / silta-release crd
	releaseInfo, err := GetReleaseInfo(clusterKey, releaseNamespace, releaseName)
	if err != nil {
		return schemas.ReleaseInventory{}, err
	}

	releaseComponents, err := releaseValuesMapper(releaseInfo)
	if err != nil {
		return schemas.ReleaseInventory{}, err
	}
	release := schemas.ReleaseInventory{
		Updated:      time.Now().Format(time.RFC3339),
		Cluster:      clusterKey,
		Namespace:    releaseNamespace,
		Name:         releaseName,
		Chart:        releaseInfo.Chart.Name(),
		ChartVersion: releaseInfo.Chart.Metadata.Version,
		Version:      releaseInfo.Version,
		Deployed:     releaseInfo.Info.LastDeployed.Local().Format(time.RFC3339),
		Status:       releaseInfo.Info.Status.String(),
		Components:   releaseComponents,
	}
	return release, nil
}

func releaseValuesMapper(releaseInfo schemas.ReleaseResponseStruct) ([]schemas.ReleaseInventoryComponents, error) {
	releaseComponents := []schemas.ReleaseInventoryComponents{}

	if releaseInfo.Chart.Name() == "drupal" {

		releaseValues := schemas.DrupalReleaseValuesSchema{}
		// Convert map to JSON bytes first, then unmarshal to struct
		valuesBytes, err := json.Marshal(releaseInfo.Values)
		if err != nil {
			log.Printf("Error marshalling release values: %s", err)
			return nil, err
		}
		err = json.Unmarshal(valuesBytes, &releaseValues)
		if err != nil {
			log.Printf("Error unmarshalling release values: %s", err)
			return nil, err
		}

		// Add downscaler service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:    "downscaler",
			Enabled: releaseValues.Downscaler.Enabled,
			Source:  "chart",
		})

		// Add mariadb service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "mariadb",
			Enabled:       releaseValues.Mariadb.Enabled,
			Image:         parseImage(fmt.Sprintf("%s/%s:%s", releaseValues.Mariadb.Image.Registry, releaseValues.Mariadb.Image.Repository, releaseValues.Mariadb.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s/%s:%s", releaseValues.Mariadb.Image.Registry, releaseValues.Mariadb.Image.Repository, releaseValues.Mariadb.Image.Tag)),
			Source:        "subchart",
		})

		// Add elasticsearch service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "elasticsearch",
			Enabled:       releaseValues.Elasticsearch.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.Elasticsearch.Image, releaseValues.Elasticsearch.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.Elasticsearch.Image, releaseValues.Elasticsearch.ImageTag)),
			Source:        "subchart",
		})

		// Add memcached service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "memcached",
			Enabled:       releaseValues.Memcached.Enabled,
			Image:         parseImage(fmt.Sprintf("%s/%s:%s", releaseValues.Memcached.Image.Registry, releaseValues.Memcached.Image.Repository, releaseValues.Memcached.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s/%s:%s", releaseValues.Memcached.Image.Registry, releaseValues.Memcached.Image.Repository, releaseValues.Memcached.Image.Tag)),
			Source:        "subchart",
		})

		// Add redis service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "redis",
			Enabled:       releaseValues.Redis.Enabled,
			Image:         parseImage(fmt.Sprintf("%s/%s:%s", releaseValues.Redis.Image.Registry, releaseValues.Redis.Image.Repository, releaseValues.Redis.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s/%s:%s", releaseValues.Redis.Image.Registry, releaseValues.Redis.Image.Repository, releaseValues.Redis.Image.Tag)),
			Source:        "subchart",
		})

		// Add solr service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "solr",
			Enabled:       releaseValues.Solr.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.Solr.Image, releaseValues.Solr.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.Solr.Image, releaseValues.Solr.ImageTag)),
			Source:        "chart",
		})

		// Add varnish service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "varnish",
			Enabled:       releaseValues.Varnish.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.Varnish.Image, releaseValues.Varnish.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.Varnish.Image, releaseValues.Varnish.ImageTag)),
			Source:        "chart",
		})

		// Add mailpit service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "mailpit",
			Enabled:       releaseValues.Mailpit.Enabled,
			Image:         parseImage(fmt.Sprintf("%s/%s:%s", releaseValues.Mailpit.Image.Registry, releaseValues.Mailpit.Image.Repository, releaseValues.Mailpit.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s/%s:%s", releaseValues.Mailpit.Image.Registry, releaseValues.Mailpit.Image.Repository, releaseValues.Mailpit.Image.Tag)),
			Source:        "subchart",
		})

		// Add mailhog service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "mailhog",
			Enabled:       releaseValues.Mailhog.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.Mailhog.Image.Repository, releaseValues.Mailhog.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.Mailhog.Image.Repository, releaseValues.Mailhog.Image.Tag)),
			Source:        "subchart",
		})

		// Add clamav service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "clamav",
			Enabled:       releaseValues.Clamav.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.Clamav.Image, releaseValues.Clamav.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.Clamav.Image, releaseValues.Clamav.ImageTag)),
			Source:        "chart",
		})

		// Add pxc-db service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:    "pxc-db",
			Enabled: releaseValues.PxcDb.Enabled,
			Source:  "chart",
		})

		// Add signalsciences service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "signalsciences",
			Enabled:       releaseValues.SignalSciences.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.SignalSciences.Image, releaseValues.SignalSciences.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.SignalSciences.Image, releaseValues.SignalSciences.ImageTag)),
			Source:        "chart",
		})
	}

	if releaseInfo.Chart.Name() == "frontend" {

		releaseValues := schemas.FrontendReleaseValuesSchema{}
		// Convert map to JSON bytes first, then unmarshal to struct
		valuesBytes, err := json.Marshal(releaseInfo.Values)
		if err != nil {
			log.Printf("Error marshalling release values: %s", err)
			return nil, err
		}
		err = json.Unmarshal(valuesBytes, &releaseValues)
		if err != nil {
			log.Printf("Error unmarshalling release values: %s", err)
			return nil, err
		}

		// Add downscaler service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:    "downscaler",
			Enabled: releaseValues.Downscaler.Enabled,
			Source:  "chart",
		})

		// Add mariadb service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "mariadb",
			Enabled:       releaseValues.Mariadb.Enabled,
			Image:         parseImage(fmt.Sprintf("%s/%s:%s", releaseValues.Mariadb.Image.Registry, releaseValues.Mariadb.Image.Repository, releaseValues.Mariadb.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s/%s:%s", releaseValues.Mariadb.Image.Registry, releaseValues.Mariadb.Image.Repository, releaseValues.Mariadb.Image.Tag)),
			Source:        "subchart",
		})

		// Add varnish service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "varnish",
			Enabled:       releaseValues.Varnish.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.Varnish.Image, releaseValues.Varnish.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.Varnish.Image, releaseValues.Varnish.ImageTag)),
			Source:        "chart",
		})

		// Add elasticsearch service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "elasticsearch",
			Enabled:       releaseValues.Elasticsearch.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.Elasticsearch.Image, releaseValues.Elasticsearch.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.Elasticsearch.Image, releaseValues.Elasticsearch.ImageTag)),
			Source:        "subchart",
		})

		// Add mongodb service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "mongodb",
			Enabled:       releaseValues.Mongodb.Enabled,
			Image:         parseImage(fmt.Sprintf("%s/%s:%s", releaseValues.Mongodb.Image.Registry, releaseValues.Mongodb.Image.Repository, releaseValues.Mongodb.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s/%s:%s", releaseValues.Mongodb.Image.Registry, releaseValues.Mongodb.Image.Repository, releaseValues.Mongodb.Image.Tag)),
			Source:        "subchart",
		})

		// Add postgresql service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "postgresql",
			Enabled:       releaseValues.Postgresql.Enabled,
			Image:         parseImage(fmt.Sprintf("%s/%s:%s", releaseValues.Postgresql.Image.Registry, releaseValues.Postgresql.Image.Repository, releaseValues.Postgresql.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s/%s:%s", releaseValues.Postgresql.Image.Registry, releaseValues.Postgresql.Image.Repository, releaseValues.Postgresql.Image.Tag)),
			Source:        "subchart",
		})

		// Add rabbitmq service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "rabbitmq",
			Enabled:       releaseValues.Rabbitmq.Enabled,
			Image:         parseImage(fmt.Sprintf("%s/%s:%s", releaseValues.Rabbitmq.Image.Registry, releaseValues.Rabbitmq.Image.Repository, releaseValues.Rabbitmq.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s/%s:%s", releaseValues.Rabbitmq.Image.Registry, releaseValues.Rabbitmq.Image.Repository, releaseValues.Rabbitmq.Image.Tag)),
			Source:        "subchart",
		})

		// Add signalsciences service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "signalsciences",
			Enabled:       releaseValues.SignalSciences.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.SignalSciences.Image, releaseValues.SignalSciences.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.SignalSciences.Image, releaseValues.SignalSciences.ImageTag)),
			Source:        "chart",
		})

		// Add mailpit service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "mailpit",
			Enabled:       releaseValues.Mailpit.Enabled,
			Image:         parseImage(fmt.Sprintf("%s/%s:%s", releaseValues.Mailpit.Image.Registry, releaseValues.Mailpit.Image.Repository, releaseValues.Mailpit.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s/%s:%s", releaseValues.Mailpit.Image.Registry, releaseValues.Mailpit.Image.Repository, releaseValues.Mailpit.Image.Tag)),
			Source:        "subchart",
		})

		// Add mailhog service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "mailhog",
			Enabled:       releaseValues.Mailhog.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.Mailhog.Image.Repository, releaseValues.Mailhog.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.Mailhog.Image.Repository, releaseValues.Mailhog.Image.Tag)),
			Source:        "subchart",
		})

		// Add redis service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "redis",
			Enabled:       releaseValues.Redis.Enabled,
			Image:         parseImage(fmt.Sprintf("%s/%s:%s", releaseValues.Redis.Image.Registry, releaseValues.Redis.Image.Repository, releaseValues.Redis.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s/%s:%s", releaseValues.Redis.Image.Registry, releaseValues.Redis.Image.Repository, releaseValues.Redis.Image.Tag)),
			Source:        "subchart",
		})

		// Add custom services from services map
		for serviceName, service := range releaseValues.Services {
			if service.Image != "" {
				releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
					Name:          serviceName,
					Enabled:       true, // If service is defined in values, consider it enabled
					Image:         parseImage(service.Image),
					ParsedVersion: parseVersionFromImage(service.Image),
					Source:        "custom",
				})
			}
		}
	}

	if releaseInfo.Chart.Name() == "simple" {

		releaseValues := schemas.SimpleReleaseValuesSchema{}
		// Convert map to JSON bytes first, then unmarshal to struct
		valuesBytes, err := json.Marshal(releaseInfo.Values)
		if err != nil {
			log.Printf("Error marshalling release values: %s", err)
			return nil, err
		}
		err = json.Unmarshal(valuesBytes, &releaseValues)
		if err != nil {
			log.Printf("Error unmarshalling release values: %s", err)
			return nil, err
		}

		// Add downscaler service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:    "downscaler",
			Enabled: releaseValues.Downscaler.Enabled,
			Source:  "chart",
		})

		// Add signalsciences service
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "signalsciences",
			Enabled:       releaseValues.SignalSciences.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.SignalSciences.Image, releaseValues.SignalSciences.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.SignalSciences.Image, releaseValues.SignalSciences.ImageTag)),
			Source:        "chart",
		})
	}

	if releaseInfo.Chart.Name() == "silta-cluster" {
		releaseValues := schemas.SiltaClusterReleaseValuesSchema{}
		// Convert map to JSON bytes first, then unmarshal to struct
		valuesBytes, err := json.Marshal(releaseInfo.Values)
		if err != nil {
			log.Printf("Error marshalling release values: %s", err)
			return nil, err
		}
		err = json.Unmarshal(valuesBytes, &releaseValues)
		if err != nil {
			log.Printf("Error unmarshalling release values: %s", err)
			return nil, err
		}

		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "ingress-nginx",
			Enabled:       releaseValues.IngressNginx.Enabled,
			Image:         releaseValues.IngressNginx.Controller.Image.Image,
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.IngressNginx.Controller.Image.Image, releaseValues.IngressNginx.Controller.Image.Tag)),
			Source:        "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "nginx-traefik",
			Enabled:       releaseValues.NginxTraefik.Enabled,
			Image:         releaseValues.NginxTraefik.Controller.Image.Image,
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.NginxTraefik.Controller.Image.Image, releaseValues.NginxTraefik.Controller.Image.Tag)),
			Source:        "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "traefik",
			Enabled:       releaseValues.Traefik.Enabled,
			Image:         releaseValues.Traefik.Image,
			ParsedVersion: parseVersionFromImage(releaseValues.Traefik.Image),
			Source:        "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "csi-rclone",
			Enabled:       releaseValues.CsiRclone.Enabled,
			Image:         "csi-rclone:" + releaseValues.CsiRclone.Version,
			ParsedVersion: parseVersionFromImage("csi-rclone:" + releaseValues.CsiRclone.Version),
			Source:        "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "minio",
			Enabled:       releaseValues.Minio.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.Minio.Image.Repository, releaseValues.Minio.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.Minio.Image.Repository, releaseValues.Minio.Image.Tag)),
			Source:        "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "ssh-jumpserver",
			Enabled:       releaseValues.GitAuth.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.GitAuth.Image, releaseValues.GitAuth.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.GitAuth.Image, releaseValues.GitAuth.ImageTag)),
			Source:        "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "ssh-key-server",
			Enabled:       releaseValues.SshKeyServer.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.SshKeyServer.Image, releaseValues.SshKeyServer.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.SshKeyServer.Image, releaseValues.SshKeyServer.ImageTag)),
			Source:        "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "deployment-remover",
			Enabled:       releaseValues.DeploymentRemover.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.DeploymentRemover.Image, releaseValues.DeploymentRemover.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.DeploymentRemover.Image, releaseValues.DeploymentRemover.ImageTag)),
			Source:        "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "silta-downscaler",
			Enabled:       releaseValues.SiltaDownscaler.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.SiltaDownscaler.Image, releaseValues.SiltaDownscaler.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.SiltaDownscaler.Image, releaseValues.SiltaDownscaler.ImageTag)),
			Source:        "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "silta-proxy",
			Enabled:       releaseValues.SiltaProxy.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.SiltaProxy.Image, releaseValues.SiltaProxy.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.SiltaProxy.Image, releaseValues.SiltaProxy.Tag)),
			Source:        "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:    "pxc-operator",
			Enabled: releaseValues.PxcOperator.Enabled,
			Source:  "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:    "cert-manager",
			Enabled: releaseValues.CertManager.Enabled,
			Source:  "external",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "instana-agent",
			Enabled:       releaseValues.InstanaAgent.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.InstanaAgent.Agent.Image.Name, releaseValues.InstanaAgent.Agent.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.InstanaAgent.Agent.Image.Name, releaseValues.InstanaAgent.Agent.Image.Tag)),
			Source:        "external",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "nfs-subdir-external-provisioner",
			Enabled:       releaseValues.NfsSubdirExternalProvisioner.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.NfsSubdirExternalProvisioner.Image.Repository, releaseValues.NfsSubdirExternalProvisioner.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.NfsSubdirExternalProvisioner.Image.Repository, releaseValues.NfsSubdirExternalProvisioner.Image.Tag)),
			Source:        "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "k8s-controller-sidecars",
			Enabled:       releaseValues.K8sControllerSidecars.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.K8sControllerSidecars.Image.Repository, releaseValues.K8sControllerSidecars.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.K8sControllerSidecars.Image.Repository, releaseValues.K8sControllerSidecars.Image.Tag)),
			Source:        "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "docker-registry",
			Enabled:       releaseValues.DockerRegistry.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.DockerRegistry.Image.Repository, releaseValues.DockerRegistry.Image.Tag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.DockerRegistry.Image.Repository, releaseValues.DockerRegistry.Image.Tag)),
			Source:        "chart",
		})
		releaseComponents = append(releaseComponents, schemas.ReleaseInventoryComponents{
			Name:          "daemonset-max-map-count-setter",
			Enabled:       releaseValues.Daemonset.MaxMapCountSetter.Enabled,
			Image:         parseImage(fmt.Sprintf("%s:%s", releaseValues.Daemonset.MaxMapCountSetter.Image, releaseValues.Daemonset.MaxMapCountSetter.ImageTag)),
			ParsedVersion: parseVersionFromImage(fmt.Sprintf("%s:%s", releaseValues.Daemonset.MaxMapCountSetter.Image, releaseValues.Daemonset.MaxMapCountSetter.ImageTag)),
			Source:        "chart",
		})
	}

	return releaseComponents, nil
}

// Parse image url parts, return clean image url
func parseImage(imageUrl string) string {
	// Image url can be in format "repository/image:tag" or "repository/image@digest",
	// we need to remove empty parts or return empty string if parts are missing

	var image string
	var tag string
	var repository string
	var imageName string

	// Extract image and tag
	imageParts := strings.Split(imageUrl, ":")
	if len(imageParts) > 1 {
		image = imageParts[0]
		tag = imageParts[1]
	}

	imageParts = strings.Split(image, "/")
	if len(imageParts) > 1 {
		repository = strings.Join(imageParts[0:len(imageParts)-1], "/")
		imageName = imageParts[len(imageParts)-1]
	}

	if repository != "" && imageName != "" && tag != "" {
		return fmt.Sprintf("%s/%s:%s", repository, imageName, tag)
	}
	if repository != "" && imageName != "" {
		return fmt.Sprintf("%s/%s", repository, imageName)
	}
	if imageName != "" && tag != "" {
		return fmt.Sprintf("%s:%s", imageName, tag)
	}
	if imageName != "" {
		return imageName
	}

	return ""
}

func parseImagePathFromImage(image string) string {
	imageParts := strings.Split(image, "/")
	if len(imageParts) > 1 {
		return imageParts[len(imageParts)-1]
	}
	return image
}

func parseTagFromImage(image string) string {
	imageParts := strings.Split(image, ":")
	if len(imageParts) > 1 {
		return imageParts[1]
	}
	return ""
}

// Extract version from image url if present
func parseVersionFromImage(image string) string {
	// Image url can be in format "repository/image:tag" or "repository/image@digest",
	// we need to extract tag part if image is in "repository/image:tag" format
	// Possible formats:
	// docker.io/library/nginx:1.20.0, version: 1.20.0
	// docker.io/library/nginx@sha256:1234567890, version: ""
	// docker.io/library/nginx, version: ""
	// docker.io/library/nginx: version: ""
	// library/nginx:1.20.0 version: 1.20.0
	// library/nginx:a-custom-image-1.20.0 version: 1.20.0
	// test555:test-1.2.3 version: 1.2.3
	// test555:test-v1.2.3 version: 1.2.3
	// docker.io/bitnami/mariadb:10.6.15-debian-11-r24 version: 10.6.15
	// wunderio/silta-varnish:6-v1 version: 6
	// docker.io/wunderio/bitnami-redis:6.2.7-debian-11-r11 version: 6.2.7
	// europe-north1-docker.pkg.dev/projectname-elasticsearch:with-icu-analysis version: ""
	// europe-north1-docker.pkg.dev/projectname-elasticsearch:with-plugins version: ""
	// europe-north1-docker.pkg.dev/projectname-elasticsearch:with-icu-analysis-8.13.4 version: ""
	// europe-north1-docker.pkg.dev/projectname-elasticsearch:8.13.4-with-icu-analysis version: "8.13.4"
	// europe-north1-docker.pkg.dev/projectname-elasticsearch:8.13.4_1 version: 8.13.4

	// Extract image and tag
	imageParts := strings.Split(image, ":")

	var tag string
	if len(imageParts) > 1 {
		tag = imageParts[1]
	}

	// Extract version from tag
	version := ""
	if tag != "" {
		// Special case for elasticsearch "with-icu-analysis" and "with-plugins"
		if strings.Contains(image, "elasticsearch") && strings.HasPrefix(tag, "with-icu-analysis") || strings.HasPrefix(tag, "with-plugins") {
			version = ""
		} else if strings.Contains(tag, "-") {
			// If tag contains dash, version is the part before first dash
			tagParts := strings.Split(tag, "-")
			version = tagParts[0]
		} else {
			version = tag
		}
	}
	// if tag contains underscore, version is the part before first underscore
	if tag != "" && strings.Contains(tag, "_") {
		tagParts := strings.Split(tag, "_")
		version = tagParts[0]
	}

	return version
}

func parseReleaseNameFromMetadata(metadata metav1.ObjectMeta) string {
	if metadata.Labels["release"] != "" {
		return metadata.Labels["release"]
	}
	if metadata.Labels["app.kubernetes.io/instance"] != "" {
		return metadata.Labels["app.kubernetes.io/instance"]
	}
	if metadata.Annotations["meta.helm.sh/release-name"] != "" {
		return metadata.Annotations["meta.helm.sh/release-name"]
	}

	return ""
}

// storeReleaseInventory stores or updates a release inventory in MongoDB
func storeReleaseInventory(release schemas.ReleaseInventory) error {
	// Use cluster, namespace, and name as unique identifier
	filter := bson.M{
		"cluster":   release.Cluster,
		"namespace": release.Namespace,
		"name":      release.Name,
	}

	// Upsert the release (update if exists, insert if not)
	opts := options.Update().SetUpsert(true)
	_, err := db.InventoryReleases.UpdateOne(context.TODO(), filter, bson.M{"$set": release}, opts)
	if err != nil {
		return fmt.Errorf("error storing release inventory: %w", err)
	}

	return nil
}

// storeImageInventory stores or updates an image inventory in MongoDB
func storeImageInventory(image schemas.ImageInventory) error {
	// Use cluster, namespace, image, and source as unique identifier
	filter := bson.M{
		"cluster":   image.Cluster,
		"namespace": image.Namespace,
		"image":     image.Image,
		"source":    image.Source,
	}

	// Upsert the image (update if exists, insert if not)
	opts := options.Update().SetUpsert(true)
	_, err := db.InventoryImages.UpdateOne(context.TODO(), filter, bson.M{"$set": image}, opts)
	if err != nil {
		return fmt.Errorf("error storing image inventory: %w", err)
	}

	return nil
}

// storeClusterInventory stores or updates cluster inventory information in MongoDB
func storeClusterInventory(clusterKey string, inventory schemas.ClusterInventory) error {
	// Get existing cluster config from conf.KubeClusters
	cluster, exists := conf.KubeClusters[clusterKey]
	if !exists {
		return fmt.Errorf("cluster %s not found in configuration", clusterKey)
	}

	// Create a document that includes the cluster config and inventory data
	clusterDoc := bson.M{
		"name":           clusterKey,
		"enabled":        cluster.Enabled,
		"cluster":        cluster.Cluster,
		"user":           cluster.User,
		"kubecost":       cluster.KubeCost,
		"timeoutSeconds": cluster.TimeoutSeconds,
		"labelColor":     cluster.LabelColor,
		"createRBAC":     cluster.CreateRBAC,
		"inventory": bson.M{
			"kubernetesVersion": inventory.KubernetesVersion,
			"updated":           inventory.Updated,
			"imagesEnabled":     cluster.Inventory.Images.Enabled,
			"componentsEnabled": cluster.Inventory.Components.Enabled,
		},
	}

	// Use cluster name as unique identifier
	filter := bson.M{"name": clusterKey}

	// Upsert the cluster (update if exists, insert if not)
	opts := options.Update().SetUpsert(true)
	_, err := db.Clusters.UpdateOne(context.TODO(), filter, bson.M{"$set": clusterDoc}, opts)
	if err != nil {
		return fmt.Errorf("error storing cluster inventory: %w", err)
	}

	return nil
}

// removeClusterInventoryData removes all inventory data for a specific cluster
// This includes releases, images, and cluster inventory information
func removeClusterInventoryData(clusterKey string) error {
	// Remove all releases for this cluster
	releaseFilter := bson.M{"cluster": clusterKey}
	releaseResult, err := db.InventoryReleases.DeleteMany(context.TODO(), releaseFilter)
	if err != nil {
		log.Printf("Error removing releases from inventory for cluster %s: %s", clusterKey, err)
	} else {
		log.Printf("Removed %d releases from inventory for cluster %s", releaseResult.DeletedCount, clusterKey)
	}

	// Remove all images for this cluster
	imageFilter := bson.M{"cluster": clusterKey}
	imageResult, err := db.InventoryImages.DeleteMany(context.TODO(), imageFilter)
	if err != nil {
		log.Printf("Error removing images from inventory for cluster %s: %s", clusterKey, err)
	} else {
		log.Printf("Removed %d images from inventory for cluster %s", imageResult.DeletedCount, clusterKey)
	}

	// Note: We don't remove the cluster document itself as it will be updated with new inventory data
	// The cluster document contains configuration data that should persist

	return nil
}
