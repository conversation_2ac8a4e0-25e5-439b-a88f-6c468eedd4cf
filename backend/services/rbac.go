package services

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"golang.org/x/exp/slices"
	"gopkg.in/yaml.v3"
	v1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

type ProjectCollaborator struct {
	Login *string
	ID    int64
}

type Project struct {
	Name          string
	Collaborators []ProjectCollaborator
}

func CreateAllNamespaceRBAC(recreateGh bool, recreateCci bool) error {

	// Load all users from MongoDB, create github username to google email map.
	log.Println("Loading github to google mappings from database")
	userMappings, err := GetGhToGmailMappings()
	if err != nil {
		log.Fatal("Error while reading Github to Google mappings:", err)
		return err
	}

	// Load all projects from github using Github App access
	log.Println("Loading repository list from Github")
	repositories, err := GetAllGitRepositories(conf.GithubOrg)
	if err != nil {
		log.Fatal("Error while getting Github repository list:", err)
		return err
	}

	// Load existing namespaces from all clusters
	existingNamespaceList := GetNamespaces()

	projectList := make(map[string]Project)

	log.Printf("Loading collaborator list from Github (%d projects, this will take a while). This only loads projects that have namespace in any cluster", len(repositories))

	counter := 0

	// Iterate projects and get users with push permissions from github project (excluding external contributors)
	for _, repository := range repositories {

		// Load collaborator list only for projects that have namespace in any cluster.
		// This will speed up the process considerably.
		if NamespacesContains(existingNamespaceList, *repository.Name) {

			collaborators, err := GetAllGitRepositoryCollaborators(conf.GithubOrg, *repository.Name, "internal")
			if err != nil {
				log.Fatal("Error requesting github collaborators:", err)
				return err
			}

			// create empty collaborator list of type projectCollaborator and assign to collaborators variable
			pushers := make([]ProjectCollaborator, 0)

			// Select collaborators with push access
			for _, collaborator := range collaborators {
				if collaborator.Permissions["push"] {

					// Add collaborator to projectList
					pushers = append(pushers, ProjectCollaborator{
						Login: collaborator.Login,
						ID:    *collaborator.ID,
					})
				}
			}

			// Add project to projectList
			projectList[*repository.Name] = Project{
				Name:          *repository.Name,
				Collaborators: pushers,
			}
		}

		counter++
		if counter%50 == 0 {
			log.Printf("%d/%d repositories iterated", counter, len(repositories))
		}
	}

	// Iterate clusters and match cluster namespaces with github projects
	for clusterKey, cluster := range conf.KubeClusters {

		if !cluster.Enabled {
			continue
		}

		// Check if cluster has RBAC creation enabled
		if cluster.CreateRBAC.GithubContributors || cluster.CreateRBAC.CircleCI {

			log.Println("Creating RBAC for cluster", clusterKey)

			// Connect to cluster
			clientset, err := GetKubeClient(clusterKey)
			if err != nil {
				log.Printf("Error connecting to cluster: %s", err)
				continue
			}

			// Get all namespaces in cluster
			namespaces, err := clientset.CoreV1().Namespaces().List(context.TODO(), metav1.ListOptions{})
			if err != nil {
				log.Printf("Error getting namespaces: %s", err.Error())
				continue
			}

			// Iterate namespaces and check if they match any of the projects
			for _, namespace := range namespaces.Items {
				if _, ok := projectList[namespace.Name]; ok {

					if recreateGh {
						err = CreateNamespaceGhRBAC(clusterKey, namespace.Name, projectList[namespace.Name], userMappings)
						if err != nil {
							log.Printf("Error creating Github RBAC: %s", err)
						}
					}
					if recreateCci {
						err = CreateNamespaceCciRBAC(clusterKey, namespace.Name, projectList[namespace.Name], userMappings)
						if err != nil {
							log.Printf("Error creating CircleCI RBAC: %s", err)
						}
					}
				}
			}
		}
	}

	return nil
}

func CreateNamespaceRBAC(clusterKey string, namespaceName string, project Project, userMappings map[string]string) error {

	errGh := CreateNamespaceGhRBAC(clusterKey, namespaceName, project, userMappings)
	if errGh != nil {
		log.Printf("Error creating Github RBAC: %s", errGh)
	}

	errCci := CreateNamespaceCciRBAC(clusterKey, namespaceName, project, userMappings)
	if errCci != nil {
		log.Printf("Error creating CircleCI RBAC: %s", errCci)
	}

	if errGh != nil || errCci != nil {
		return errors.New("error creating RBAC")
	}

	return nil
}

func CreateNamespaceGhRBAC(clusterKey string, namespaceName string, project Project, userMappings map[string]string) error {

	// if namespaceName is in restricted namespaces, skip RBAC creation
	if slices.Contains(conf.RestrictedNamespaces, namespaceName) {
		log.Printf("Skipping Github RBAC creation for restricted namespace %s", namespaceName)
		return nil
	}

	cluster := conf.KubeClusters[clusterKey]

	// Connect to cluster
	clientset, err := GetKubeClient(clusterKey)
	if err != nil {
		log.Printf("Error connecting to cluster: %s", err)
		return err
	}

	// Create or update "silta-namespace-admin" role in each namespace
	role := &rbacv1.Role{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "silta-namespace-admin",
			Namespace: namespaceName,
			Labels: map[string]string{
				"creator": "silta-dashboard",
			},
		},
		Rules: []rbacv1.PolicyRule{
			{
				APIGroups: []string{"*"},
				Resources: []string{"*"},
				Verbs:     []string{"*"},
			},
		},
	}

	// Create role
	_, err = clientset.RbacV1().Roles(namespaceName).Create(context.TODO(), role, metav1.CreateOptions{})
	if err != nil {
		_, err = clientset.RbacV1().Roles(namespaceName).Update(context.TODO(), role, metav1.UpdateOptions{})
		if err != nil {
			log.Printf("Error creating or updating role: %s", err.Error())
			return err
		}
	}

	// TODO:MAYBE Trigger mapping refresh after Google mapping changes?
	if cluster.CreateRBAC.GithubContributors {

		// Create or update admin RoleBinding
		roleBinding := &rbacv1.RoleBinding{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "silta-namespace-admin-gh",
				Namespace: namespaceName,
				Labels: map[string]string{
					"creator": "silta-dashboard",
				},
			},
			RoleRef: rbacv1.RoleRef{
				Kind:     "Role",
				Name:     "silta-namespace-admin",
				APIGroup: "rbac.authorization.k8s.io",
			},
			Subjects: []rbacv1.Subject{},
		}

		// Iterate project collaborators (with push access) and add them to roleBinding
		for _, collaborator := range project.Collaborators {

			// Try to get user email from github
			collaboratorIdStr := strconv.FormatInt(collaborator.ID, 10)

			// If collaborator is not in userMappings, skip
			if _, ok := userMappings[collaboratorIdStr]; !ok {
				continue
			}

			// Get google email from github username
			googleEmail := userMappings[collaboratorIdStr]

			// Add collaborator to roleBinding
			roleBinding.Subjects = append(roleBinding.Subjects, rbacv1.Subject{
				Kind: "User",
				Name: googleEmail,
			})
		}

		_, err = clientset.RbacV1().RoleBindings(namespaceName).Create(context.TODO(), roleBinding, metav1.CreateOptions{})
		if err != nil {
			_, err = clientset.RbacV1().RoleBindings(namespaceName).Update(context.TODO(), roleBinding, metav1.UpdateOptions{})
			if err != nil {
				log.Printf("Error creating or updating roleBinding: %s", err.Error())
				return err
			}
		}

		log.Printf("Created or updated Github roleBinding for cluster %s, namespace: %s", clusterKey, namespaceName)
	}

	return nil
}

func waitForSaSecretsField(clientset *kubernetes.Clientset, namespaceName string, secretName string, timeoutSeconds int64, oldSecretName string) (*v1.ServiceAccount, error) {

	// Loop with timeout and wait for secret to be created
	timeout := time.Now().Add(time.Second * time.Duration(timeoutSeconds))
	for {
		// Re-read service account
		cciSA, err := clientset.CoreV1().ServiceAccounts(namespaceName).Get(context.TODO(), secretName, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}

		if len(cciSA.Secrets) > 0 {
			// Make sure it does not reference the old secret
			if cciSA.Secrets[0].Name != oldSecretName {
				return cciSA, nil
			}
		}

		if time.Now().After(timeout) {
			err = errors.New("timeout waiting for CircleCI SA secret to be created")
			return nil, err
		}

		// New kubernetes.io/service-account-token secret will be created by kubernetes automatically, we might need to wait for it to be created
		time.Sleep(time.Second * 1)
	}
}

func waitForSecretDataField(clientset *kubernetes.Clientset, namespaceName string, secretName string, timeoutSeconds int64) (*v1.Secret, error) {

	// Loop with timeout and wait for secret to be created
	timeout := time.Now().Add(time.Second * time.Duration(timeoutSeconds))
	for {
		// Re-read secret
		secret, err := clientset.CoreV1().Secrets(namespaceName).Get(context.TODO(), secretName, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}

		if len(secret.Data) > 0 {
			return secret, nil
		}

		if time.Now().After(timeout) {
			err = errors.New("timeout waiting for secret to be created")
			return nil, err
		}

		// New kubernetes.io/service-account-token secret will be created by kubernetes automatically, we might need to wait for it to be created
		time.Sleep(time.Second * 1)
	}
}

func CreateNamespaceCciRBAC(clusterKey string, namespaceName string, project Project, userMappings map[string]string) error {

	// if namespaceName is in restricted namespaces, skip RBAC creation
	if slices.Contains(conf.RestrictedNamespaces, namespaceName) {
		log.Printf("Skipping CircleCI RBAC creation for restricted namespace %s", namespaceName)
		return nil
	}

	cluster := conf.KubeClusters[clusterKey]

	// Connect to cluster
	clientset, err := GetKubeClient(clusterKey)
	if err != nil {
		log.Printf("Error connecting to cluster: %s", err)
		return err
	}

	// Create or update "silta-namespace-admin" role in each namespace
	role := &rbacv1.Role{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "silta-namespace-admin",
			Namespace: namespaceName,
			Labels: map[string]string{
				"creator": "silta-dashboard",
			},
		},
		Rules: []rbacv1.PolicyRule{
			{
				APIGroups: []string{"*"},
				Resources: []string{"*"},
				Verbs:     []string{"*"},
			},
		},
	}

	// Create role
	_, err = clientset.RbacV1().Roles(namespaceName).Create(context.TODO(), role, metav1.CreateOptions{})
	if err != nil {
		_, err = clientset.RbacV1().Roles(namespaceName).Update(context.TODO(), role, metav1.UpdateOptions{})
		if err != nil {
			log.Printf("Error creating or updating role: %s", err.Error())
			return err
		}
	}

	// CircleCI RBAC
	if cluster.CreateRBAC.CircleCI {

		if conf.CircleCiToken == "" {
			log.Println("CircleCI token is not set, skipping CircleCI service account creation")
			return nil
		}

		// Get kubernetes version
		kubeVersion, err := clientset.Discovery().ServerVersion()
		if err != nil {
			log.Printf("Error getting kubernetes version: %s", err.Error())
			return err
		}

		// Load CircleCI service account
		_, err = clientset.CoreV1().ServiceAccounts(namespaceName).Get(context.TODO(), "silta-circleci", metav1.GetOptions{})

		// If SA does not exist, create it
		if err != nil {
			// Define SA
			cciSAMeta := &v1.ServiceAccount{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "silta-circleci",
					Namespace: namespaceName,
					Labels: map[string]string{
						"creator": "silta-dashboard",
					},
				},
			}

			// Create SA
			_, err = clientset.CoreV1().ServiceAccounts(namespaceName).Create(context.TODO(), cciSAMeta, metav1.CreateOptions{})
			if err != nil {
				log.Printf("Error creating serviceaccount: %s", err.Error())
				return err
			}
		}

		accessToken := []byte{}
		secret := &v1.Secret{}

		// If kubernetes cluster version is below 1.24 (1.23 and below), wait for secret field to be populated
		if kubeVersion.Major < "1" || (kubeVersion.Major == "1" && kubeVersion.Minor < "24") {

			// Wait for secret field to be populated and update cciSA variable
			cciSA, err := waitForSaSecretsField(clientset, namespaceName, "silta-circleci", 10, "")
			if err != nil {
				log.Printf("Error waiting for secret field to be populated: %s", err.Error())
				return err
			}

			saSecretName := cciSA.Secrets[0].Name

			// Get secret using autogenerated name from cciSA
			secret, err = clientset.CoreV1().Secrets(namespaceName).Get(context.TODO(), saSecretName, metav1.GetOptions{})
			if err != nil {
				log.Printf("Error getting CircleCI SA secret: %s", err.Error())
				// There should be a secret already.
				return err
			}
		}

		// If kubernetes cluster version is 1.24 or above, we need to create secret manually.
		// TokenRequest API won't work for us at this time since we need a long lived secret for CI/CD
		if kubeVersion.Major > "1" || (kubeVersion.Major == "1" && kubeVersion.Minor >= "24") {

			saSecretName := "silta-circleci-token"

			// Load CircleCI SA secret using hardcoded name
			secret, err = clientset.CoreV1().Secrets(namespaceName).Get(context.TODO(), saSecretName, metav1.GetOptions{})
			if err != nil {

				// Create secret for CircleCI SA
				secretMeta := &v1.Secret{
					ObjectMeta: metav1.ObjectMeta{
						Name:      saSecretName,
						Namespace: namespaceName,
						Labels: map[string]string{
							"creator": "silta-dashboard",
						},
						Annotations: map[string]string{
							"kubernetes.io/service-account.name": "silta-circleci",
						},
					},
					Type: "kubernetes.io/service-account-token",
				}

				// Create secret
				_, err = clientset.CoreV1().Secrets(namespaceName).Create(context.TODO(), secretMeta, metav1.CreateOptions{})
				if err != nil {
					log.Printf("Error creating CircleCI SA secret: %s", err.Error())
					return err
				}
				// Wait for secret data field to be populated
				secret, err = waitForSecretDataField(clientset, namespaceName, saSecretName, 10)
				if err != nil {
					log.Printf("Error waiting for secret data field to be populated: %s", err.Error())
					return err
				}
			}
		}

		accessToken = secret.Data["token"]

		// Calculate secret creation age
		secretAge := time.Since(secret.GetCreationTimestamp().Time)

		// Rotation logic: if a secret is older than 1 month, delete and create new secret
		if secretAge > time.Hour*24*30 {

			log.Printf("CircleCI secret is older than 1 month, deleting and creating new one\n")

			// Delete secret
			err = clientset.CoreV1().Secrets(namespaceName).Delete(context.TODO(), secret.Name, metav1.DeleteOptions{})
			if err != nil {
				log.Printf("Error deleting CircleCI SA secret: %s", err.Error())
				return err
			}

			// < 1.24 Reload SA, wait for secret name to be populated, get the new secret
			if kubeVersion.Major < "1" || (kubeVersion.Major == "1" && kubeVersion.Minor < "24") {

				// Wait for secret field to be populated and update cciSA variable
				cciSA, err := waitForSaSecretsField(clientset, namespaceName, "silta-circleci", 10, secret.Name)
				if err != nil {
					log.Printf("Error waiting for secret field to be populated: %s", err.Error())
					return err
				}

				saSecretName := cciSA.Secrets[0].Name

				// Get secret using autogenerated name from cciSA
				secret, err = clientset.CoreV1().Secrets(namespaceName).Get(context.TODO(), saSecretName, metav1.GetOptions{})
				if err != nil {
					log.Printf("Error getting CircleCI SA secret: %s", err.Error())
					// There should be a secret already.
					return err
				}
			}

			// 1.24+ Secret recreation
			if kubeVersion.Major > "1" || (kubeVersion.Major == "1" && kubeVersion.Minor >= "24") {

				saSecretName := "silta-circleci-token"

				// Create secret for CircleCI SA
				secret = &v1.Secret{
					ObjectMeta: metav1.ObjectMeta{
						Name:      saSecretName,
						Namespace: namespaceName,
						Labels: map[string]string{
							"creator": "silta-dashboard",
						},
						Annotations: map[string]string{
							"kubernetes.io/service-account.name": "silta-circleci",
						},
					},
					Type: "kubernetes.io/service-account-token",
				}

				// Create secret
				_, err = clientset.CoreV1().Secrets(namespaceName).Create(context.TODO(), secret, metav1.CreateOptions{})
				if err != nil {
					log.Printf("Error creating CircleCI SA secret: %s", err.Error())
					return err
				}
				// Wait for secret data field to be populated
				secret, err = waitForSecretDataField(clientset, namespaceName, saSecretName, 10)
				if err != nil {
					log.Printf("Error waiting for secret data field to be populated: %s", err.Error())
					return err
				}
			}

			accessToken = secret.Data["token"]
		}

		// Create or update admin RoleBinding
		roleBinding := &rbacv1.RoleBinding{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "silta-namespace-admin-cci",
				Namespace: namespaceName,
				Labels: map[string]string{
					"creator": "silta-dashboard",
				},
			},
			RoleRef: rbacv1.RoleRef{
				Kind:     "Role",
				Name:     "silta-namespace-admin",
				APIGroup: "rbac.authorization.k8s.io",
			},
			Subjects: []rbacv1.Subject{
				{
					Kind: "ServiceAccount",
					Name: "silta-circleci",
				},
			},
		}

		_, err = clientset.RbacV1().RoleBindings(namespaceName).Create(context.TODO(), roleBinding, metav1.CreateOptions{})
		if err != nil {
			_, err = clientset.RbacV1().RoleBindings(namespaceName).Update(context.TODO(), roleBinding, metav1.UpdateOptions{})
			if err != nil {
				log.Printf("Error creating or updating roleBinding: %s", err.Error())
				return err
			}
		}

		// Create kubectl config yaml
		cciKubeConfig := KubeConfig{
			ApiVersion:     "v1",
			Kind:           "Config",
			CurrentContext: "silta",
			Clusters: []KubeConfigClusters{
				{
					Name: "silta",
					Cluster: KubeConfigCluster{
						Server:                   cluster.Cluster.Server,
						CertificateAuthorityData: cluster.Cluster.CaData,
					},
				},
			},
			Contexts: []KubeConfigContexts{
				{
					Name: "silta",
					Context: KubeConfigContext{
						Cluster:   "silta",
						Namespace: namespaceName,
						User:      "silta-circleci",
					},
				},
			},
			Users: []KubeConfigUsers{
				{
					Name: "silta-circleci",
					User: KubeConfigUser{
						Token: string(accessToken),
					},
				},
			},
		}

		cciKubeConfigYaml, err := yaml.Marshal(cciKubeConfig)
		if err != nil {
			return err
		}

		// Post ENV var to CircleCI project
		cciApiUrl := fmt.Sprintf("https://circleci.com/api/v2/project/gh/%s/%s/envvar", conf.GithubOrg, namespaceName)

		type CircleCiEnvVar struct {
			Name  string `json:"name"`
			Value string `json:"value"`
		}

		cciEnvVar := CircleCiEnvVar{
			Name:  fmt.Sprintf("%s_KUBECTL_CONFIG", clusterKey),
			Value: string(base64.StdEncoding.EncodeToString(cciKubeConfigYaml)),
		}

		payload, _ := json.Marshal(cciEnvVar)
		req, _ := http.NewRequest("POST", cciApiUrl, bytes.NewBuffer(payload))
		req.Header.Add("content-type", "application/json")
		req.Header.Add("authorization", "Basic "+base64.StdEncoding.EncodeToString([]byte(string(conf.CircleCiToken))))
		res, _ := http.DefaultClient.Do(req)
		res.Body.Close()
	}

	log.Printf("Created or updated CircleCI roleBinding for cluster %s, namespace: %s", clusterKey, namespaceName)

	return nil
}
