package services

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"strings"
	"time"

	"github.com/google/uuid"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/yaml"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/db"
	"github.com/wunderio/silta-dashboard/backend/schemas"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/Masterminds/semver/v3"

	"github.com/rclone/rclone/fs/config/obscure" // Using official rclone module
)

func ParseReleaseName(release string) (string, string, string) {
	releaseSplit := strings.Split(release, "/")
	if len(releaseSplit) != 3 {
		return "", "", ""
	}
	return releaseSplit[0], releaseSplit[1], releaseSplit[2]
}

func UpsertEnvVar(container *corev1.Container, name, value string) {
	for i, env := range container.Env {
		if env.Name == name {
			container.Env[i].Value = value
			return
		}
	}
	container.Env = append(container.Env, corev1.EnvVar{
		Name:  name,
		Value: value,
	})
}

func IsPartialSync(releaseSync schemas.ReleaseSync) bool {
	return releaseSync.SyncDatabase == "false" || releaseSync.SyncFiles == "false"
}

// getHelmChartVersionFromConfigMap Extracts the semver.Version from the helm.sh/chart label on a config map.
// Returns error if label is missing, malformed, or not a valid semver.
func getHelmChartVersionFromConfigMap(configMap *corev1.ConfigMap) (*semver.Version, error) {
	const labelKey = "helm.sh/chart"

	chartLabel, ok := configMap.Labels[labelKey]
	if !ok {
		return nil, fmt.Errorf("label %s not found", labelKey)
	}

	parts := strings.Split(chartLabel, "-")
	// Loop from the end to find the first valid semver
	for i := len(parts) - 1; i >= 0; i-- {
		if v, err := semver.NewVersion(parts[i]); err == nil {
			return v, nil
		}
	}
	return nil, fmt.Errorf("could not find version in helm chart label")
}

// CheckVersionAgainstConstraint checks *semver.Version against constraint.
// Constraint can be like "1.20.0", ">=1.18.0", "<2.0.0" etc.
func CheckVersionAgainstConstraint(v *semver.Version, constraint string) (bool, error) {
	c, err := semver.NewConstraint(constraint)
	if err != nil {
		return false, fmt.Errorf("invalid semver constraint: %q", constraint)
	}
	return c.Check(v), nil
}

func CheckPartialSyncSupported(configMap *corev1.ConfigMap) (bool, error) {
	// Partial sync is supported starting from Drupal chart 1.x.x and above.
	// todo: update version constraint and commend above before releasing this feature.
	versionConstraint := ">1.18.0"
	v, err := getHelmChartVersionFromConfigMap(configMap)
	if err != nil {
		return false, fmt.Errorf("error getting helm chart version to verify if partial data sync is supported on the selected release: %s", err)
	}
	if ok, err := CheckVersionAgainstConstraint(v, versionConstraint); err != nil {
		return false, fmt.Errorf("failure on checking chart version: %s", err)
	} else if !ok {
		return false, fmt.Errorf("release doesn't support partial data sync, this is a recent change, you might need to rerun the deployment")
	}
	return true, nil
}

func GetReleaseSync(cluster string, namespace string, release string) (schemas.ReleaseSync, error) {
	filter := bson.M{"targetCluster": cluster, "targetNamespace": namespace, "targetRelease": release}
	var releaseSync schemas.ReleaseSync
	err := db.ReleaseSync.FindOne(context.TODO(), filter).Decode(&releaseSync)
	if err != nil {
		return releaseSync, err
	}
	return releaseSync, nil
}

func GetReleaseSyncByPid(pid string) (schemas.ReleaseSync, error) {
	filter := bson.M{"pid": pid}
	var releaseSync schemas.ReleaseSync
	err := db.ReleaseSync.FindOne(context.TODO(), filter).Decode(&releaseSync)
	if err != nil {
		return releaseSync, err
	}
	return releaseSync, nil
}

func GetReleaseSyncStatusByPid(pid string) (string, error) {
	filter := bson.M{"pid": pid}
	var releaseSync schemas.ReleaseSync
	err := db.ReleaseSync.FindOne(context.TODO(), filter).Decode(&releaseSync)
	if err != nil {
		return "", err
	}
	return releaseSync.Status, nil
}

func SetReleaseSyncStatusByPid(pid string, status string) error {
	filter := bson.M{"pid": pid}
	update := bson.M{"$set": bson.M{"status": status}}
	_, err := db.ReleaseSync.UpdateOne(context.TODO(), filter, update)
	if err != nil {
		return err
	}
	return nil
}

func AppendReleaseOutputByPid(pid string, output string) error {
	filter := bson.M{"pid": pid}
	// Read existing output
	releaseSync, err := GetReleaseSyncByPid(pid)
	if err != nil {
		return err
	}
	// Append output to existing output string
	update := bson.M{"$set": bson.M{"output": releaseSync.Output + output + "\n"}}
	context := context.TODO()
	_, err = db.ReleaseSync.UpdateOne(context, filter, update)
	if err != nil {
		return err
	}
	return nil
}

// Create unique Process ID, make sure there's no such id in database
func CreateReleaseSyncPID() (string, error) {
	// Loop until unique PID is found
	for {
		// Create UUID
		uuid := uuid.NewString()

		// Attempt to create a shorter version of UUID to fit the kubernetes resource name length limit
		pid := uuid[:4]

		// Select number of sync processes with matching uuid
		filter := bson.M{"pid": pid}
		count, err := db.ReleaseSync.CountDocuments(context.TODO(), filter)
		if err != nil {
			log.Printf("[CreateReleaseSyncPID] uuid generation error: %v", err)
			return "", err
		}
		if count == 0 {
			return pid, nil
		}
	}
}

func StartReleaseSync(sourceCluster string, sourceNamespace string, sourceRelease string, targetCluster string, targetNamespace string, targetRelease string, syncDatabase string, syncFiles string) (schemas.ReleaseSync, error) {
	pid, err := CreateReleaseSyncPID()
	if err != nil {
		return schemas.ReleaseSync{}, err
	}

	storageConnectionKey := uuid.NewString()
	storageConnectionSecret := obscure.MustObscure(uuid.NewString())
	storageUrl := conf.SyncStorageURL + conf.SyncStorageRoutePrefix

	releaseSync := schemas.ReleaseSync{
		PID:                     pid,
		Created:                 time.Now().Format(time.RFC3339),
		SourceCluster:           sourceCluster,
		SourceNamespace:         sourceNamespace,
		SourceRelease:           sourceRelease,
		TargetCluster:           targetCluster,
		TargetNamespace:         targetNamespace,
		TargetRelease:           targetRelease,
		SyncDatabase:            syncDatabase,
		SyncFiles:               syncFiles,
		StorageConnectionKey:    storageConnectionKey,
		StorageConnectionSecret: storageConnectionSecret,
		StorageConnectionURL:    storageUrl,
		Status:                  "pending",
	}
	_, err = db.ReleaseSync.InsertOne(context.TODO(), releaseSync)
	if err != nil {
		log.Printf("[StartReleaseSync] error inserting release sync: %v", err)
		return releaseSync, err
	}

	// Fork sync process into a background goroutine
	go ReleaseSyncProcess(pid)

	return releaseSync, nil
}

func CreateSyncJob(pid string, releaseSync schemas.ReleaseSync, direction string) (status string, err error) {

	if direction != "source" && direction != "target" && direction != "sync-storage-cleanup" {
		return "failed", fmt.Errorf("[CreateSyncJob] invalid sync direction: %s", direction)
	}

	jobName := ""
	clusterName := ""
	releaseName := ""
	releaseNamespace := ""
	syncMountPath := ""

	if direction == "source" {
		jobName = releaseSync.SourceRelease + "-sync-" + pid
		clusterName = releaseSync.SourceCluster
		releaseName = releaseSync.SourceRelease
		releaseNamespace = releaseSync.SourceNamespace
		syncMountPath = "/app/reference-data"
	} else if direction == "target" {
		jobName = releaseSync.TargetRelease + "-sync-" + pid
		clusterName = releaseSync.TargetCluster
		releaseName = releaseSync.TargetRelease
		releaseNamespace = releaseSync.TargetNamespace
		syncMountPath = "/app/reference-data"
	} else if direction == "sync-storage-cleanup" {
		jobName = releaseSync.SourceRelease + "-sync-" + pid
		clusterName = releaseSync.SourceCluster
		releaseName = releaseSync.SourceRelease
		releaseNamespace = releaseSync.SourceNamespace
		syncMountPath = "/app/reference-data"
	}

	// Get kube client
	clientSet, err := GetKubeClient(clusterName)
	if err != nil {
		return "failed", err
	}

	clusterTimeoutSeconds := conf.KubeClusters[clusterName].TimeoutSeconds
	if clusterTimeoutSeconds == 0 {
		clusterTimeoutSeconds = conf.KubeConnectionTimeoutSeconds
	}
	timeout := int64(clusterTimeoutSeconds)
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Second)
	defer cancel()

	// Break if sync job canceled by user
	releaseSyncStatus, err := GetReleaseSyncStatusByPid(pid)
	if err != nil {
		log.Printf("[ReleaseSyncProcess] Error getting release sync status: %v", err)
		logReleaseSyncProcessOutput(clientSet, releaseNamespace, jobName, pid)
		removeReleaseSyncProcessResources(clientSet, releaseNamespace, jobName)
		return "failed", err
	}
	if releaseSyncStatus != "running" {
		return releaseSyncStatus, fmt.Errorf("job (%s) state changed: %s", direction, releaseSyncStatus)
	}

	// Involved deployments should have sync process configmap. Fail if not found.
	configMapName := releaseName + "-hub"
	configMap, err := clientSet.CoreV1().ConfigMaps(releaseNamespace).Get(ctx, configMapName, metav1.GetOptions{})
	if err != nil {
		return "failed", fmt.Errorf("error getting silta-hub configmap, this is a recent change, you might need to rerun the deployment")
	}

	if IsPartialSync(releaseSync) {
		if ok, err := CheckPartialSyncSupported(configMap); !ok {
			return "failed", err
		}
	}

	jobYaml := ""
	if direction == "source" || direction == "sync-storage-cleanup" {
		jobYaml = configMap.Data["syncPushJob"]
	} else if direction == "target" {
		jobYaml = configMap.Data["syncPullJob"]
	}

	if jobYaml == "" {
		return "failed", fmt.Errorf("job definition not found in silta-hub configmap, this is a recent change, you might need to rerun the deployment")
	}

	// Convert YAML to JSON
	jobJSON, err := yaml.YAMLToJSON([]byte(jobYaml))
	if err != nil {
		return "failed", fmt.Errorf("error loading job definition: %v", err)
	}

	// Load job yaml into job object
	job := &batchv1.Job{}
	if err := json.Unmarshal(jobJSON, job); err != nil {
		return "failed", fmt.Errorf("error unmarshalling job definition: %v", err)
	}

	// Adjust/force job name and namespace
	job.ObjectMeta.Name = jobName
	job.ObjectMeta.Namespace = releaseNamespace

	// Replace command on cleanup job
	if direction == "sync-storage-cleanup" {
		job.Spec.Template.Spec.Containers[0].Args = []string{"rm -rf " + syncMountPath + "/*"}
	}

	// Remove job resource limits and requests, as they are not needed for sync jobs
	// loop through all containers and remove resource limits and requests
	for i := range job.Spec.Template.Spec.Containers {
		job.Spec.Template.Spec.Containers[i].Resources = corev1.ResourceRequirements{
			Limits:   corev1.ResourceList{},
			Requests: corev1.ResourceList{},
		}
	}

	// Upsert env vars controlling what data is synced.
	container := &job.Spec.Template.Spec.Containers[0]
	UpsertEnvVar(container, "REF_DATA_COPY_DB", releaseSync.SyncDatabase)
	UpsertEnvVar(container, "REF_DATA_COPY_FILES", releaseSync.SyncFiles)

	syncStorageSize := configMap.Data["syncStorageSize"]
	if syncStorageSize == "" {
		syncStorageSize = "1Gi"
	}

	storageUser := releaseSync.StorageConnectionKey
	storagePass := releaseSync.StorageConnectionSecret
	storageUrl := releaseSync.StorageConnectionURL
	storageClass := "silta-shared"

	// Create PersistentVolume and PersistentVolumeClaim for sync storage
	pv := &corev1.PersistentVolume{
		ObjectMeta: metav1.ObjectMeta{
			Name: releaseName + "-sync-" + pid,
		},
		Spec: corev1.PersistentVolumeSpec{
			AccessModes:                   []corev1.PersistentVolumeAccessMode{"ReadWriteOnce"},
			Capacity:                      corev1.ResourceList{corev1.ResourceStorage: resource.MustParse(syncStorageSize)},
			PersistentVolumeReclaimPolicy: corev1.PersistentVolumeReclaimDelete,
			StorageClassName:              storageClass,
			MountOptions:                  []string{"rw"},
			PersistentVolumeSource: corev1.PersistentVolumeSource{
				CSI: &corev1.CSIPersistentVolumeSource{
					Driver:       "csi-rclone",
					VolumeHandle: releaseName + "-sync-" + pid,
					VolumeAttributes: map[string]string{
						"remote":           "webdav",
						"webdav-url":       storageUrl,
						"webdav-user":      storageUser,
						"webdav-pass":      storagePass,
						"dir-cache-time":   "10s",
						"vfs-cache-mode":   "writes",
						"remotePath":       "",
						"remotePathSuffix": releaseSync.SourceNamespace + "/" + releaseSync.SourceRelease + "-sync-" + pid,
					},
				},
			},
		},
	}

	_, err = clientSet.CoreV1().PersistentVolumes().Create(ctx, pv, metav1.CreateOptions{})
	if err != nil {
		log.Printf("[ReleaseSyncProcess] Error creating persistent volume: %s", err)
		SetReleaseSyncStatusByPid(pid, "failed")
		AppendReleaseOutputByPid(pid, "[ERROR] Error creating persistent volume on "+clusterName+" cluster")
		return "failed", err
	}

	pvc := &corev1.PersistentVolumeClaim{
		ObjectMeta: metav1.ObjectMeta{
			Name: releaseName + "-sync-" + pid,
		},
		Spec: corev1.PersistentVolumeClaimSpec{
			AccessModes: []corev1.PersistentVolumeAccessMode{"ReadWriteOnce"},
			Resources: corev1.VolumeResourceRequirements{
				Requests: corev1.ResourceList{corev1.ResourceStorage: resource.MustParse(syncStorageSize)},
			},
			StorageClassName: &storageClass,
		},
	}

	_, err = clientSet.CoreV1().PersistentVolumeClaims(releaseNamespace).Create(ctx, pvc, metav1.CreateOptions{})
	if err != nil {
		log.Printf("[ReleaseSyncProcess] Error creating persistent volume claim: %s", err)
		SetReleaseSyncStatusByPid(pid, "failed")
		AppendReleaseOutputByPid(pid, "[ERROR] Error creating persistent volume claim on "+direction+", "+clusterName+" cluster")
		return "failed", err
	}

	// Attach pv and pvc to job
	job.Spec.Template.Spec.Volumes = append(job.Spec.Template.Spec.Volumes, corev1.Volume{
		Name: "sync-volume",
		VolumeSource: corev1.VolumeSource{
			PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
				ClaimName: releaseName + "-sync-" + pid,
			},
		},
	})
	job.Spec.Template.Spec.Containers[0].VolumeMounts = append(job.Spec.Template.Spec.Containers[0].VolumeMounts, corev1.VolumeMount{
		Name:      "sync-volume",
		MountPath: syncMountPath,
	})

	// Create job
	_, err = clientSet.BatchV1().Jobs(releaseNamespace).Create(ctx, job, metav1.CreateOptions{})
	if err != nil {
		log.Printf("[ReleaseSyncProcess] Error creating job: %s", err)
		SetReleaseSyncStatusByPid(pid, "failed")
		logReleaseSyncProcessOutput(clientSet, releaseNamespace, jobName, pid)
		AppendReleaseOutputByPid(pid, "[ERROR] Error creating job on "+direction+", "+clusterName+" cluster")
		removeReleaseSyncProcessResources(clientSet, releaseNamespace, jobName)
		return "failed", err
	}

	// Wait for job to complete
	// 2h should be enough for data transfer and import
	jobTimeout := 7200
	for {

		// Exit loop and do resource cleanup if sync job canceled by user
		releaseSyncStatus, err := GetReleaseSyncStatusByPid(pid)
		if err != nil {
			log.Printf("[ReleaseSyncProcess] Error getting release sync status: %v", err)
			logReleaseSyncProcessOutput(clientSet, releaseNamespace, jobName, pid)
			removeReleaseSyncProcessResources(clientSet, releaseNamespace, jobName)
			return "failed", err
		}
		if releaseSyncStatus != "running" {
			logReleaseSyncProcessOutput(clientSet, releaseNamespace, jobName, pid)
			return releaseSyncStatus, fmt.Errorf("job (%s) state changed: %s", direction, releaseSyncStatus)
		}

		// Get job status
		job, err := clientSet.BatchV1().Jobs(releaseNamespace).Get(context.TODO(), jobName, metav1.GetOptions{})
		if err != nil {
			log.Printf("[ReleaseSyncProcess] Error getting job %s: %s", jobName, err)
			SetReleaseSyncStatusByPid(pid, "failed")
			AppendReleaseOutputByPid(pid, "[ERROR] Error getting job status on "+clusterName+" cluster")
			return "failed", err
		}

		// Exit loop if job has succeeded
		if job.Status.Succeeded > 0 {
			break
		}

		// If job has failed, log error and exit loop
		if job.Status.Failed > 0 {
			log.Printf("[ReleaseSyncProcess] Job %s failed", jobName)
			SetReleaseSyncStatusByPid(pid, "failed")
			logReleaseSyncProcessOutput(clientSet, releaseNamespace, jobName, pid)
			AppendReleaseOutputByPid(pid, "Job failed on "+clusterName+" cluster")
			removeReleaseSyncProcessResources(clientSet, releaseNamespace, jobName)
			return "failed", fmt.Errorf("job failed on %s cluster", clusterName)
		}

		// Exit loop if job has failed
		jobTimeout -= 5
		if jobTimeout <= 0 {
			log.Printf("[ReleaseSyncProcess] Job %s timeout", jobName)
			logReleaseSyncProcessOutput(clientSet, releaseNamespace, jobName, pid)
			SetReleaseSyncStatusByPid(pid, "failed")
			AppendReleaseOutputByPid(pid, "Job creation timeout on "+clusterName+" cluster")
			removeReleaseSyncProcessResources(clientSet, releaseNamespace, jobName)
			return "failed", fmt.Errorf("job creation timeout on %s cluster", clusterName)
		}

		// Wait 5 seconds
		time.Sleep(5 * time.Second)
	}

	// Note: source and target environments have to be awake for the sync to work (db import, etc.)
	// Error: "Drupal is not installed, skipping reference database dump."

	// Get the related pod name
	pods, err := clientSet.CoreV1().Pods(releaseNamespace).List(context.TODO(), metav1.ListOptions{LabelSelector: "job-name=" + jobName})
	if err != nil {
		log.Printf("[ReleaseSyncProcess] error getting pod list: %v", err)
		SetReleaseSyncStatusByPid(pid, "failed")
		logReleaseSyncProcessOutput(clientSet, releaseNamespace, jobName, pid)
		AppendReleaseOutputByPid(pid, "Job log retrieval failed on "+clusterName+" cluster")
		removeReleaseSyncProcessResources(clientSet, releaseNamespace, jobName)
		return "failed", err
	}
	if len(pods.Items) == 0 {
		log.Printf("[ReleaseSyncProcess] no pods found for job %s", jobName)
		SetReleaseSyncStatusByPid(pid, "failed")
		logReleaseSyncProcessOutput(clientSet, releaseNamespace, jobName, pid)
		AppendReleaseOutputByPid(pid, "Job log retrieval failed on "+clusterName+" cluster")
		removeReleaseSyncProcessResources(clientSet, releaseNamespace, jobName)
		return "failed", err
	}

	logReleaseSyncProcessOutput(clientSet, releaseNamespace, jobName, pid)
	removeReleaseSyncProcessResources(clientSet, releaseNamespace, jobName)

	log.Printf("[ReleaseSyncProcess] %s/%s/%s job completed", clusterName, releaseNamespace, releaseName)
	AppendReleaseOutputByPid(pid, fmt.Sprintf("%s/%s/%s job completed", clusterName, releaseNamespace, releaseName))

	return "completed", nil
}

func ReleaseSyncProcess(pid string) {

	// Update status to "running"
	SetReleaseSyncStatusByPid(pid, "running")

	// Get sync process details
	releaseSync, err := GetReleaseSyncByPid(pid)
	if err != nil {
		log.Printf("[ReleaseSyncProcess] error getting release sync: %v", err)
		AppendReleaseOutputByPid(pid, "[ERROR] Error getting release sync")
		SetReleaseSyncStatusByPid(pid, "failed")
		return
	}

	// Compare chart name of source and target deployment, they have to be equal
	sourceChartName := GetReleaseChartName(releaseSync.SourceCluster, releaseSync.SourceNamespace, releaseSync.SourceRelease)
	targetChartName := GetReleaseChartName(releaseSync.TargetCluster, releaseSync.TargetNamespace, releaseSync.TargetRelease)
	if sourceChartName != targetChartName {
		AppendReleaseOutputByPid(pid, "[ERROR] Source and target deployment charts differ (source: "+sourceChartName+", target: "+targetChartName+")")
		SetReleaseSyncStatusByPid(pid, "failed")
		return
	}

	if sourceChartName != "drupal" {
		AppendReleaseOutputByPid(pid, "[ERROR] Only drupal deployment sync is supported currently")
		SetReleaseSyncStatusByPid(pid, "failed")
		return
	}

	if IsPartialSync(releaseSync) {
		AppendReleaseOutputByPid(pid, fmt.Sprintf("Partial sync requested: Database: %s, Files: %s\n", releaseSync.SyncDatabase, releaseSync.SyncFiles))
	}

	// Create source job and related resources on source cluster
	AppendReleaseOutputByPid(pid, "** Pushing source data to sync storage ...")
	status, err := CreateSyncJob(pid, releaseSync, "source")
	if status == "canceled" {
		return
	}
	if err != nil {
		AppendReleaseOutputByPid(pid, fmt.Sprintf("[ERROR] getting source data for %s/%s/%s: %v", releaseSync.SourceCluster, releaseSync.SourceNamespace, releaseSync.SourceRelease, err))
		CreateSyncJob(pid, releaseSync, "sync-storage-cleanup")
		SetReleaseSyncStatusByPid(pid, "failed")
		return
	}

	// Create kubernetes job and related resources on target cluster
	AppendReleaseOutputByPid(pid, "** Pulling source data from sync storage ...")
	status, err = CreateSyncJob(pid, releaseSync, "target")
	if status == "canceled" {
		return
	}
	if err != nil {
		AppendReleaseOutputByPid(pid, fmt.Sprintf("[ERROR] getting target data for %s/%s/%s: %v", releaseSync.TargetCluster, releaseSync.TargetNamespace, releaseSync.TargetRelease, err))
		CreateSyncJob(pid, releaseSync, "sync-storage-cleanup")
		SetReleaseSyncStatusByPid(pid, "failed")
		return
	}

	// Remove files from sync storage
	AppendReleaseOutputByPid(pid, "** Removing source data from sync storage ...")
	status, err = CreateSyncJob(pid, releaseSync, "sync-storage-cleanup")
	if status == "canceled" {
		return
	}
	if err != nil {
		AppendReleaseOutputByPid(pid, fmt.Sprintf("[ERROR] removing source data for %s/%s/%s: %v", releaseSync.SourceCluster, releaseSync.SourceNamespace, releaseSync.SourceRelease, err))
		SetReleaseSyncStatusByPid(pid, "failed")
		return
	}

	// Update status to "completed"
	AppendReleaseOutputByPid(pid, "Sync job successful")
	SetReleaseSyncStatusByPid(pid, "completed")
}

func logReleaseSyncProcessOutput(clientset *kubernetes.Clientset, namespace string, jobName string, pid string) {
	// Get the related pod name
	pods, err := clientset.CoreV1().Pods(namespace).List(context.TODO(), metav1.ListOptions{LabelSelector: "job-name=" + jobName})
	if err != nil {
		log.Printf("[logReleaseSyncProcessOutput] error finding pod for job %s", jobName)
		return
	}
	if len(pods.Items) == 0 {
		log.Printf("[logReleaseSyncProcessOutput] no pods found for job %s", jobName)
		return
	}

	podName := pods.Items[0].Name

	// Get log stream
	req := clientset.CoreV1().Pods(namespace).GetLogs(podName, &corev1.PodLogOptions{})
	logStream, err := req.Stream(context.TODO())
	if err == nil {
		outBytes, err := io.ReadAll(logStream)
		if err == nil {
			out := string(outBytes)
			AppendReleaseOutputByPid(pid, out)
		}
	} else {
		log.Printf("[logReleaseSyncProcessOutput] error getting log stream for pod %s: %v", podName, err)
	}
}

func removeReleaseSyncProcessResources(clientset *kubernetes.Clientset, namespace string, resourceName string) {

	fmt.Printf("[removeReleaseSyncProcessResources] removing resources for %s/%s\n", namespace, resourceName)

	// Remove job and related resources
	// propogation policy is set to background, so the job will be deleted asynchronously
	propagationPolicy := metav1.DeletePropagationBackground
	clientset.BatchV1().Jobs(namespace).Delete(context.TODO(), resourceName, metav1.DeleteOptions{PropagationPolicy: &propagationPolicy})

	// Remove persistent volume claim
	clientset.CoreV1().PersistentVolumeClaims(namespace).Delete(context.TODO(), resourceName, metav1.DeleteOptions{})

	// Remove persistent volume
	clientset.CoreV1().PersistentVolumes().Delete(context.TODO(), resourceName, metav1.DeleteOptions{})
}

func CancelReleaseSync(cluster string, namespace string, release string) error {

	releaseSync, err := GetReleaseSync(cluster, namespace, release)
	if err != nil {
		log.Printf("[CancelReleaseSync] error getting release sync: %v", err)
		return err
	}

	AppendReleaseOutputByPid(releaseSync.PID, "[INFO] sync job canceled")
	SetReleaseSyncStatusByPid(releaseSync.PID, "canceled")

	// remove job and related resources
	sourceClientset, err := GetKubeClient(releaseSync.SourceCluster)
	if err == nil {
		removeReleaseSyncProcessResources(sourceClientset, releaseSync.SourceNamespace, releaseSync.SourceRelease+"-sync-"+releaseSync.PID)
	}
	targetClientset, err := GetKubeClient(releaseSync.TargetCluster)
	if err == nil {
		removeReleaseSyncProcessResources(targetClientset, releaseSync.TargetNamespace, releaseSync.TargetRelease+"-sync-"+releaseSync.PID)
	}

	return nil
}

func RemoveReleaseSyncEntry(cluster string, namespace string, release string) error {

	releaseSync, err := GetReleaseSync(cluster, namespace, release)
	if err != nil {
		log.Printf("[RemoveReleaseSyncEntry] error getting release sync: %v", err)
		return err
	}

	// Remove release sync from database
	filter := bson.M{"pid": releaseSync.PID}
	_, err = db.ReleaseSync.DeleteOne(context.TODO(), filter)
	if err != nil {
		log.Printf("[RemoveReleaseSyncEntry] error deleting release sync: %v", err)
		return err
	}

	return nil
}
