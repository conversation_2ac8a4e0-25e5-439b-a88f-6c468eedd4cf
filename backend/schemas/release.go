package schemas

import (
	helmChart "helm.sh/helm/v3/pkg/chart"
	helmRelease "helm.sh/helm/v3/pkg/release"
)

type ReleaseResponseStruct struct {
	Key          string                 `json:"key"`
	Release      string                 `json:"release"`
	Namespace    string                 `json:"namespace"`
	Status       string                 `json:"status"`
	Version      int                    `json:"version"`
	Cluster      string                 `json:"cluster"`
	ClusterColor string                 `json:"clustercolor"`
	Info         *helmRelease.Info      `json:"info"`
	Chart        *helmChart.Chart       `json:"-"` // skip the field from json
	Values       map[string]interface{} `json:"values"`
}

type NamespaceCache struct {
	Name        string                  `json:"namespace"`
	CacheExpire string                  `json:"cacheExpire"`
	Releases    []ReleaseResponseStruct `json:"releases"`
}
