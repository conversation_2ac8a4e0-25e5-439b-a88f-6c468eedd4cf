package schemas

// Describes Github user, used in User document
type Github struct {
	GithubId        *int64  `json:"githubId" bson:"githubId"`
	Name            *string `json:"name" bson:"name"`
	ScreenName      *string `json:"screenName" bson:"screenName"`
	ProfileImageUrl *string `json:"profileImageUrl" bson:"profileImageUrl"`
	ApiAccessToken  string  `json:"apiAccessToken" bson:"apiAccessToken"`
	OrgScope        string  `json:"orgScope" bson:"orgScope"`
	OrgAdmin        bool    `json:"orgAdmin" bson:"orgAdmin"`
}

// Describes Google user, used in User document
type Google struct {
	GoogleId   string `json:"googleId" bson:"googleId"`
	Name       string `json:"name" bson:"name"`
	Email      string `json:"email" bson:"email"`
	PictureUrl string `json:"pictureUrl" bson:"pictureUrl"`
}

// Describes User document
type User struct {
	Uuid           string `json:"uuid" bson:"uuid"`
	Name           string `json:"name" bson:"name"`
	SessionToken   string `json:"sessionToken" bson:"sessionToken"`
	SessionExpires string `json:"sessionExpires" bson:"sessionExpires"`
	Github         Github `json:"github" bson:"github"`
	Google         Google `json:"google" bson:"google"`
}
