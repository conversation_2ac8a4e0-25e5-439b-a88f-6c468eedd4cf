package schemas

import (
	"github.com/google/go-github/v49/github"
)

// Describes User document
type UserCache struct {
	Uuid               string               `json:"uuid" bson:"uuid"`
	Name               *string              `json:"name" bson:"name"`
	GithubId           *int64               `json:"githubId" bson:"githubId"`
	RepositoriesExpire string               `json:"repositoriesExpire" bson:"repositoriesExpire"`
	Repositories       []*github.Repository `json:"repositories" bson:"repositories"`
	Namespaces         []NamespaceCache     `json:"namespaces" bson:"namespaces"`
}
