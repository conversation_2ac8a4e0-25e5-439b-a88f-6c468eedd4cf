package schemas

import (
	"encoding/json"
	"fmt"
)

// FlexibleString is a type that can unmarshal from both string and number
type FlexibleString string

// UnmarshalJSON implements custom unmarshalling to handle both string and number types
func (fs *FlexibleString) UnmarshalJSON(data []byte) error {
	// Try to unmarshal as string first
	var s string
	if err := json.Unmarshal(data, &s); err == nil {
		*fs = FlexibleString(s)
		return nil
	}

	// If that fails, try as number
	var n float64
	if err := json.Unmarshal(data, &n); err == nil {
		*fs = FlexibleString(fmt.Sprintf("%g", n))
		return nil
	}

	// If both fail, try as integer
	var i int64
	if err := json.Unmarshal(data, &i); err == nil {
		*fs = FlexibleString(fmt.Sprintf("%d", i))
		return nil
	}

	return fmt.Errorf("cannot unmarshal %s into FlexibleString", string(data))
}

// String returns the string value
func (fs FlexibleString) String() string {
	return string(fs)
}

type ClusterInventory struct {
	KubernetesVersion string `json:"kubernetesVersion" bson:"kubernetesVersion"`
	Updated           string `json:"updated" bson:"updated"`
}

type ReleaseInventoryComponents struct {
	Name          string `json:"name" bson:"name"`
	Enabled       bool   `json:"enabled" bson:"enabled"`
	Image         string `json:"image" bson:"image"`
	ParsedVersion string `json:"parsedVersion" bson:"parsedVersion"`
	Source        string `json:"source" bson:"source"`
}

type ReleaseInventory struct {
	Updated      string                       `json:"updated" bson:"updated"`
	Cluster      string                       `json:"cluster" bson:"cluster"`
	Namespace    string                       `json:"namespace" bson:"namespace"`
	Name         string                       `json:"name" bson:"name"`
	Chart        string                       `json:"chart" bson:"chart"`
	ChartVersion string                       `json:"chartVersion" bson:"chartVersion"`
	Version      int                          `json:"version" bson:"version"`
	Deployed     string                       `json:"deployed" bson:"deployed"`
	Status       string                       `json:"status" bson:"status"`
	Components   []ReleaseInventoryComponents `json:"components" bson:"components"`
}

type ImageInventory struct {
	Updated           string `json:"updated" bson:"updated"`
	Cluster           string `json:"cluster" bson:"cluster"`
	Namespace         string `json:"namespace" bson:"namespace"`
	ResourceName      string `json:"resourceName" bson:"resourceName"`
	ParsedReleaseName string `json:"parsedReleaseName" bson:"parsedReleaseName"`
	Url               string `json:"image" bson:"url"`
	Image             string `json:"imageName" bson:"image"`
	Tag               string `json:"imageTag" bson:"tag"`
	Source            string `json:"source" bson:"source"`
}

type DrupalReleaseValuesSchema struct {
	Downscaler struct {
		Enabled bool `json:"enabled"`
	} `json:"downscaler"`
	Mariadb struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Registry   string         `json:"registry"`
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
	} `json:"mariadb"`
	Elasticsearch struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
	} `json:"elasticsearch"`
	Memcached struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Registry   string         `json:"registry"`
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
	} `json:"memcached"`
	Redis struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Registry   string         `json:"registry"`
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
	} `json:"redis"`
	Solr struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
	} `json:"solr"`
	Varnish struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
	} `json:"varnish"`
	Mailpit struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Registry   string         `json:"registry"`
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
			Digest     string         `json:"digest"`
		} `json:"image"`
	} `json:"mailpit"`
	Mailhog struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
	} `json:"mailhog"`
	Clamav struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
	} `json:"clamav"`
	PxcDb struct {
		Enabled bool `json:"enabled"`
	} `json:"pxc-db"`
	SignalSciences struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
	} `json:"signal-sciences"`
}

type FrontendReleaseValuesSchema struct {
	Downscaler struct {
		Enabled bool `json:"enabled"`
	} `json:"downscaler"`
	Mariadb struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Registry   string         `json:"registry"`
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
	} `json:"mariadb"`
	Varnish struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
	} `json:"varnish"`
	Elasticsearch struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
	} `json:"elasticsearch"`
	Mongodb struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Registry   string         `json:"registry"`
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
	} `json:"mongodb"`
	Postgresql struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Registry   string         `json:"registry"`
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
	} `json:"postgresql"`
	Rabbitmq struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Registry   string         `json:"registry"`
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
	} `json:"rabbitmq"`
	SignalSciences struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
	} `json:"signal-sciences"`
	Mailpit struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Registry   string         `json:"registry"`
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
			Digest     string         `json:"digest"`
		} `json:"image"`
	} `json:"mailpit"`
	Mailhog struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
	} `json:"mailhog"`
	Redis struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Registry   string         `json:"registry"`
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
	} `json:"redis"`
	Services map[string]CustomFrontendService `json:"services"`
}

type CustomFrontendService struct {
	Image string `json:"image"`
}

type SimpleReleaseValuesSchema struct {
	Downscaler struct {
		Enabled bool `json:"enabled"`
	} `json:"downscaler"`
	SignalSciences struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
	} `json:"signal-sciences"`
}

type SiltaClusterReleaseValuesSchema struct {
	IngressNginx struct {
		Enabled    bool `json:"enabled"`
		Controller struct {
			Image struct {
				Image  string         `json:"image"`
				Tag    FlexibleString `json:"tag"`
				Digest string         `json:"digest"`
			} `json:"image"`
		} `json:"controller"`
	} `json:"ingress-nginx"`
	NginxTraefik struct {
		Enabled    bool `json:"enabled"`
		Controller struct {
			Image struct {
				Image  string         `json:"image"`
				Tag    FlexibleString `json:"tag"`
				Digest string         `json:"digest"`
			} `json:"image"`
		} `json:"controller"`
	} `json:"nginx-traefik"`
	Traefik struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
	} `json:"traefik"`
	CsiRclone struct {
		Enabled bool   `json:"enabled"`
		Version string `json:"version"`
	} `json:"csi-rclone"`
	Minio struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
		McImage struct {
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"mcImage"`
	} `json:"minio"`
	GitAuth struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
	} `json:"gitAuth"`
	SshKeyServer struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
	} `json:"sshKeyServer"`
	DeploymentRemover struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
	} `json:"deploymentRemover"`
	SiltaDownscaler struct {
		Enabled  bool           `json:"enabled"`
		Image    string         `json:"image"`
		ImageTag FlexibleString `json:"imageTag"`
		Proxy    struct {
			Image    string         `json:"image"`
			ImageTag FlexibleString `json:"imageTag"`
		}
	} `json:"silta-downscaler"`
	SiltaProxy struct {
		Enabled bool           `json:"enabled"`
		Image   string         `json:"image"`
		Tag     FlexibleString `json:"tag"`
	} `json:"silta-proxy"`
	PxcOperator struct {
		Enabled bool `json:"enabled"`
	} `json:"pxc-operator"`
	// This is loaded externally, not a part of chart anymore
	CertManager struct {
		Enabled bool `json:"enabled"`
	} `json:"cert-manager"`
	InstanaAgent struct {
		Enabled bool `json:"enabled"`
		Agent   struct {
			Image struct {
				Name   string         `json:"name"`
				Digest string         `json:"digest"`
				Tag    FlexibleString `json:"tag"`
			} `json:"image"`
		} `json:"agent"`
	} `json:"instana-agent"`
	//TODO: deprecate silta nfs provisioner & nfs server, make a ticket for that
	NfsSubdirExternalProvisioner struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
	} `json:"nfs-subdir-external-provisioner"`
	K8sControllerSidecars struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
	} `json:"k8s-controller-sidecars"`
	DockerRegistry struct {
		Enabled bool `json:"enabled"`
		Image   struct {
			Repository string         `json:"repository"`
			Tag        FlexibleString `json:"tag"`
		} `json:"image"`
	} `json:"docker-registry"`
	Daemonset struct {
		Enabled           bool `json:"enabled"`
		MaxMapCountSetter struct {
			Enabled  bool           `json:"enabled"`
			Image    string         `json:"image"`
			ImageTag FlexibleString `json:"imageTag"`
		} `json:"maxMapCountSetter"`
	} `json:"daemonset"`
}
