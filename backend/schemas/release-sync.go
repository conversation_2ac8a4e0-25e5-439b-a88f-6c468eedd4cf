package schemas

// Describes Release sync document
type ReleaseSync struct {
	PID                     string `json:"pid" bson:"pid"`
	Created                 string `json:"created" bson:"created"`
	SourceCluster           string `json:"sourceCluster" bson:"sourceCluster"`
	SourceNamespace         string `json:"sourceNamespace" bson:"sourceNamespace"`
	SourceRelease           string `json:"sourceRelease" bson:"sourceRelease"`
	TargetCluster           string `json:"cluster" bson:"targetCluster"`
	TargetNamespace         string `json:"namespace" bson:"targetNamespace"`
	TargetRelease           string `json:"release" bson:"targetRelease"`
	SyncDatabase            string `json:"syncDatabase" bson:"syncDatabase"`
	SyncFiles               string `json:"syncFiles" bson:"syncFiles"`
	StorageConnectionKey    string `json:"-" bson:"storageConnectionKey"`
	StorageConnectionSecret string `json:"-" bson:"storageConnectionSecret"`
	StorageConnectionURL    string `json:"storageConnectionURL" bson:"storageConnectionURL"`
	Status                  string `json:"status" bson:"status"`
	Output                  string `json:"output" bson:"output"`
}
