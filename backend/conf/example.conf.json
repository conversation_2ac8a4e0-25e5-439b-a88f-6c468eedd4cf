{"_login_disabled_comment": "Use for development only. Useful for debugging and development. This will disable all authentication and allow anyone to access the dashboard.", "password_login_enabled": false, "password_login_password": "", "_github_login_enabled_comment": "Enable Github authentication and Github API access. If disabled, Github related features will not work.", "github_login_enabled": false, "_github_app_id_comment": "Github OAuth application id. Used for authentication as Github App", "github_app_id": "", "_github_app_private_key_comment": "Github OAuth app private key. Used for authentication as Github App. Replace newlines with \n", "github_app_private_key": "", "_github_app_installation_id_comment": "Github OAuth app installation id. Used for authentication as Github App. Can be found from organisation app listing URL", "github_app_installation_id": "", "_github_client_id_comment": "Github OAuth app client id for user authentication and requesting data as Github user", "github_client_id": "", "_github_client_secret_comment": "Github OAuth app secret for user authentication and requesting data as Github user", "github_client_secret": "", "_github_org_comment": "Github organization to use for dashboard listing filters", "github_org": "wunderio", "_google_client_id_comment": "Google OAuth app client id for user authentication", "google_client_id": "", "google_client_secret": "", "_restricted_namespaces_comment": "Dashboard does not allow write operations in following namespaces.", "restricted_namespaces": ["kube-system", "default", "cert-manager", "silta-cluster"], "_rbac_reload_key_comment": "Secret that allows remote RBAC reloading.", "rbac_reload_key": "", "_circleci_token_comment": "Personal token for CircleCI API. Used to push SA environment variable to projects.", "circleci_token": "", "_clusters_comment": "Create service account like this: keys/make-service-account.sh", "clusters": {"local": {"_enabled_comment": "If disabled, cluster will not be listed in dashboard. Allows storing extra configuration for clusters that are not used.", "enabled": true, "cluster": {"_type_comment": "type can be gke, eks, aks, etc. this does not do anything yet", "type": "minikube", "_name_comment": "name is same as cluster.name in kubeconfig", "name": "local", "_caData_comment": "caData is same as cluster.certificate-authority-data in kubeconfig", "caData": "LS0tLS1C...0tCg==", "_server_comment": "server is same as cluster.server, i.e. https://******* in kubeconfig", "server": "https://************:8443", "_insecureSkipTLSVerify_comment": "If true, skip TLS verification. Useful for development clusters.", "insecureSkipTLSVerify": false}, "user": {"name": "silta-dashboard-sa-kube-system-minikube", "token": "eyJhbGciO...zZInA"}, "kubecost": {"enabled": false, "clusterID": "silta-dev", "namespace": "kubecost", "service": "kubecost-cost-analyzer", "port": "9090", "protocol": "http", "exposeCost": true}, "uiLabelColor": "geekblue", "_create_rbac_comment": "Create RBAC resources for the cluster.", "create_rbac": {"_circleci_comment": "Create CircleCI service account and role binding. Work in progress.", "circleci": false, "_github_contributors_comment": "Use Github to Google auth mapping and create Rolebindings for each namespace.", "github_contributors": false}}}}