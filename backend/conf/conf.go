package conf

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"regexp"
	"time"

	"github.com/joho/godotenv"
)

type KubeClusterConfig struct {
	Name                  string `json:"name"`
	CaData                string `json:"caData"`
	Server                string `json:"server"`
	InsecureSkipTLSVerify bool   `json:"insecureSkipTLSVerify"`
}

type KubeUserConfig struct {
	Name  string `json:"name"`
	Token string `json:"token"`
}

type KubeClusterRbac struct {
	CircleCI           bool `json:"circleci"`
	GithubContributors bool `json:"github_contributors"`
}

type KubeCostConfig struct {
	Enabled    bool   `json:"enabled"`
	ClusterID  string `json:"clusterID"`
	Namespace  string `json:"namespace"`
	Service    string `json:"service"`
	Port       string `json:"port"`
	Protocol   string `json:"protocol"`
	ExposeCost bool   `json:"exposeCost"`
}

type KubeClusterInventory struct {
	Images struct {
		Enabled bool `json:"enabled"`
	} `json:"images"`
	Components struct {
		Enabled bool `json:"enabled"`
	} `json:"components"`
}

type KubeCluster struct {
	Enabled        bool                 `json:"enabled"`
	Cluster        KubeClusterConfig    `json:"cluster"`
	User           KubeUserConfig       `json:"user"`
	KubeCost       KubeCostConfig       `json:"kubecost"`
	Inventory      KubeClusterInventory `json:"inventory"`
	TimeoutSeconds int                  `json:"timeoutSeconds"`
	LabelColor     string               `json:"uiLabelColor"`
	CreateRBAC     KubeClusterRbac      `json:"create_rbac"`
}

type Config struct {
	MongoDatabase           string                 `json:"mongo_database"`
	SessionLifetime         string                 `json:"session_lifetime"`
	PasswordLoginEnabled    bool                   `json:"password_login_enabled"`
	PasswordLoginPassword   string                 `json:"password_login_password"`
	GithubLoginEnabled      bool                   `json:"github_login_enabled"`
	GithubAppID             string                 `json:"github_app_id"`
	GithubAppInstallationID string                 `json:"github_app_installation_id"`
	GithubAppPrivateKey     string                 `json:"github_app_private_key"`
	GithubClientID          string                 `json:"github_client_id"`
	GithubClientSecret      string                 `json:"github_client_secret"`
	GoogleClientID          string                 `json:"google_client_id"`
	GoogleClientSecret      string                 `json:"google_client_secret"`
	GithubOrg               string                 `json:"github_org"`
	RestrictedNamespaces    []string               `json:"restricted_namespaces"`
	RbacReloadKey           string                 `json:"rbac_reload_key"`
	CircleCiToken           string                 `json:"circleci_token"`
	KubeClusters            map[string]KubeCluster `json:"clusters"`
}

var (
	// Port is the port the application will run on
	Port string

	FrontendURL    string
	BackendURL     string
	SyncStorageURL string

	// RoutePrefix is the prefix for all routes, used in vhost+proxied configuration
	FrontendRoutePrefix    string
	BackendRoutePrefix     string
	SyncStorageRoutePrefix string

	// MongoURI is the URI for the MongoDB database
	MongoURI      string
	MongoUsername string
	MongoPassword string

	// MongoDatabase is the name of the MongoDB database
	MongoDatabase string

	// SessionLifetime is the time in minutes that a session will expire
	SessionLifetime time.Duration

	// PasswordLoginEnabled is used for development only. Useful for debugging and development.
	PasswordLoginEnabled  bool
	PasswordLoginPassword string

	GithubLoginEnabled      bool
	GithubAppID             string
	GithubAppInstallationID string
	GithubAppPrivateKey     string

	GithubClientID     string
	GithubClientSecret string

	// Will only list namespaces that match accessible repository names in this organisation
	GithubOrg string

	GoogleClientID     string
	GoogleClientSecret string

	// RestrictedNamespaces is a list of namespaces that can't be modified by users
	RestrictedNamespaces []string

	KubeClusters map[string]KubeCluster

	// RbacReloadKey is the key that can be used to trigger RBAC recreation using web request
	RbacReloadKey string

	// CircleCiToken is the token used to push SA as environment variable to CCI projects
	CircleCiToken string

	KubeConnectionTimeoutSeconds = 5
)

func getSvcCfg(key, fallback string) string {
	if value, ok := os.LookupEnv(key); ok {
		return value
	}
	return fallback
}

func getAppCfg(key, fallback string) string {
	if key != "" {
		return key
	}
	return fallback
}

func init() {

	// Try to load configuration from file
	ac, err := readConfFromFile("conf/conf.json")
	if err != nil {

		// Try to load configuration from .env file
		godotenv.Load(".env")

		// Try to load configuration from environment variable
		ac, err = readConfFromEnv("APP_CONFIG_JSON")
		if err != nil {
			log.Fatalf("failed to read config from env variable: %v\n", err)
		}
	}

	// Get configuration
	Port = getSvcCfg("PORT", "4000")

	FrontendRoutePrefix = getSvcCfg("FRONTEND_ROUTE_PREFIX", "")
	BackendRoutePrefix = getSvcCfg("BACKEND_ROUTE_PREFIX", "")
	SyncStorageRoutePrefix = getSvcCfg("SYNC_STORAGE_ROUTE_PREFIX", "")

	// Use variables from environment if they are set
	FrontendURL = getSvcCfg("FRONTEND_PUBLIC_URL", "")
	BackendURL = getSvcCfg("BACKEND_PUBLIC_URL", "")
	SyncStorageURL = getSvcCfg("SYNC_STORAGE_URL", "")
	if FrontendURL == "" {
		if getSvcCfg("ENVIRONMENT_DOMAIN", "") != "" {
			FrontendURL = fmt.Sprintf("https://%s", getSvcCfg("ENVIRONMENT_DOMAIN", ""))
		} else {
			FrontendURL = "http://localhost:3000"
		}
	}
	if BackendURL == "" {
		if getSvcCfg("ENVIRONMENT_DOMAIN", "") != "" {
			BackendURL = fmt.Sprintf("https://%s", getSvcCfg("ENVIRONMENT_DOMAIN", ""))
		} else {
			BackendURL = getSvcCfg("BACKEND_PUBLIC_URL", "http://localhost:4000")
		}
	}
	if SyncStorageURL == "" {
		if getSvcCfg("ENVIRONMENT_DOMAIN", "") != "" {
			SyncStorageURL = fmt.Sprintf("https://%s", getSvcCfg("ENVIRONMENT_DOMAIN", ""))
		} else {
			SyncStorageURL = getSvcCfg("SYNC_STORAGE_URL", "http://localhost:7000")
		}
	}

	// Database
	MongoURI = getSvcCfg("MONGODB_URI", "")
	if MongoURI == "" {
		// Silta specific case
		if getSvcCfg("MONGODB_HOST", "") != "" {
			MongoURI = fmt.Sprintf("mongodb://%s:%s@%s:27017", getSvcCfg("MONGODB_USER", ""), getSvcCfg("MONGODB_PASS", ""), getSvcCfg("MONGODB_HOST", ""))
		} else {
			MongoURI = "*****************************************"
		}
	}

	// MongoDatabase is the name of the MongoDB database
	MongoDatabase = getAppCfg(ac.MongoDatabase, "dashboard")

	// SessionLifetime is the time in minutes that a session Token should expire
	sl := getAppCfg(ac.SessionLifetime, "24h")
	SessionLifetime, err = time.ParseDuration(sl)
	if err != nil {
		SessionLifetime = 24 * time.Hour
	}

	// PasswordLoginEnabled is used for development only. Useful for debugging and development.
	PasswordLoginEnabled = ac.PasswordLoginEnabled
	PasswordLoginPassword = ac.PasswordLoginPassword
	if PasswordLoginEnabled && PasswordLoginPassword == "" {
		log.Fatal("Password login is enabled, but no password is set! Set conf.PasswordLoginPassword to a non-empty value to enable password login.")
	}

	GithubLoginEnabled = ac.GithubLoginEnabled
	if ac.GithubLoginEnabled {
		GithubAppID = getAppCfg(ac.GithubAppID, "")
		if GithubAppID == "" {
			log.Fatal("Github App ID (conf.GithubAppID) is required!")
		}

		GithubAppInstallationID = getAppCfg(ac.GithubAppInstallationID, "")
		if GithubAppInstallationID == "" {
			log.Fatal("Github App Installation ID (conf.GithubAppInstallationID) is required!")
		}

		GithubAppPrivateKey = getAppCfg(ac.GithubAppPrivateKey, "")
		if GithubAppPrivateKey == "" {
			log.Fatal("Github App Private Key (conf.GithubAppPrivateKey) is required!")
		}

		GithubClientID = getAppCfg(ac.GithubClientID, "")
		GithubClientSecret = getAppCfg(ac.GithubClientSecret, "")
		if GithubClientID == "" || GithubClientSecret == "" {
			log.Fatal("Github Client ID (conf.GithubClientID) and Secret (conf.GithubClientSecret) are required!")
		}

		// Will only list namespaces that match accessible repository names in this organisation
		GithubOrg = getAppCfg(ac.GithubOrg, "")
		if GithubOrg == "" {
			log.Fatal("Github Organisation (conf.GithubOrg) is required")
		}
	}

	GoogleClientID = getAppCfg(ac.GoogleClientID, "")
	GoogleClientSecret = getAppCfg(ac.GoogleClientSecret, "")
	if GoogleClientID == "" || GoogleClientSecret == "" {
		log.Println("Google Client ID (conf.GoogleClientID) or Secret (conf.GoogleClientSecret) are missing. Google account linking and RBAC assignments won't work!")
	}

	KubeClusters = ac.KubeClusters
	if len(KubeClusters) == 0 {
		log.Fatal("No Kubernetes clusters (conf.KubeClusters) defined!")
	}

	// Make sure all Clusters have a valid machine name (it's used as an environment variable name in CircleCI integration)
	for name := range KubeClusters {
		machineNameRegexp := regexp.MustCompile(`^[a-zA-Z0-9_]+$`)
		if !machineNameRegexp.MatchString(name) {
			log.Fatalf("Cluster name %q is invalid. It must match the regular expression %q", name, machineNameRegexp.String())

		}
	}

	RestrictedNamespaces = ac.RestrictedNamespaces

	RbacReloadKey = getAppCfg(ac.RbacReloadKey, "")
	if RbacReloadKey == "" {
		log.Println("RBAC reload secret (conf.RbacReloadKey) is missing. Remote RBAC reload functionality won't work!")
	}

	CircleCiToken = getAppCfg(ac.CircleCiToken, "")
	if CircleCiToken == "" {
		log.Println("CircleCI Personal Token (conf.CircleCiToken) is missing. CircleCI integration won't work!")
	}
}

func readConfFromEnv(envVariable string) (*Config, error) {
	c := &Config{}
	appConfigJson := os.Getenv(envVariable)
	if appConfigJson == "" {
		return nil, fmt.Errorf("env variable %q is empty", envVariable)
	}
	err := json.Unmarshal([]byte(appConfigJson), c)
	if err != nil {
		log.Fatalf("failed to read config from env: %v", err)
	}
	return c, err
}

func readConfFromFile(filename string) (*Config, error) {
	buf, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	c := &Config{}
	err = json.Unmarshal(buf, c)
	if err != nil {
		return nil, fmt.Errorf("in file %q: %w", filename, err)
	}

	return c, err
}
func GetConfig() *Config {
	ac, err := readConfFromFile("conf/conf.json")
	if err != nil {
		ac, err = readConfFromEnv("APP_CONFIG_JSON")
		if err != nil {
			log.Fatalf("failed to read config: %v\n", err)
		}
	}
	return ac
}
