{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/api/backups/{clustername}/{namespacename}/{releasename}/{filename}": {"get": {"description": "Backups item download", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["files"], "summary": "Backups item download", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Release name", "name": "releasename", "in": "path", "required": true}, {"type": "string", "description": "Filename", "name": "filename", "in": "path", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/api/circleci/reload-ns-rbac": {"post": {"description": "Initiates RBAC regeneration for linked (Github to Google) users and creates CircleCI Service Account for provided namespace.\nService Account key is injected into CircleCI project environment variables.\nAfter successful completion, only CircleCI service account and users with push access to the repository will have access to the namespace.\nThis endpoint also performs key rotation when it is older than 30 days.", "consumes": ["application/x-www-form-urlencoded"], "produces": ["application/json"], "tags": ["rbac"], "summary": "Reload namespace RBAC from CircleCI project", "parameters": [{"type": "string", "description": "Secret key for reloading RBAC", "name": "rbac_reload_key", "in": "formData", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespace", "in": "formData", "required": true}, {"type": "string", "description": "Cluster name", "name": "cluster", "in": "formData", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Missing or incorrect parameters", "schema": {"type": "string"}}}}}, "/api/events/{clustername}/{namespacename}/": {"get": {"description": "Retrieve pod events", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["logs"], "summary": "Retrieve pod events", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}, "post": {"description": "Retrieve pod events", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["logs"], "summary": "Retrieve pod events", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/api/events/{clustername}/{namespacename}/{podname}": {"get": {"description": "Retrieve pod events", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["logs"], "summary": "Retrieve pod events", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Pod name", "name": "podname", "in": "path", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}, "post": {"description": "Retrieve pod events", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["logs"], "summary": "Retrieve pod events", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Pod name", "name": "podname", "in": "path", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/api/logs/{clustername}/{namespacename}/{podname}/{containername}": {"get": {"description": "Retrieve container logs", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["logs"], "summary": "Retrieve container logs", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Pod name", "name": "podname", "in": "path", "required": true}, {"type": "string", "description": "Container name", "name": "containername", "in": "path", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}, "post": {"description": "Retrieve container logs", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["logs"], "summary": "Retrieve container logs", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Pod name", "name": "podname", "in": "path", "required": true}, {"type": "string", "description": "Container name", "name": "containername", "in": "path", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/api/namespace/{namespacename}/releases": {"get": {"description": "List releases in namespace", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["release"], "summary": "List releases in namespace", "parameters": [{"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/schemas.ReleaseResponseStruct"}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}, "post": {"description": "List releases in namespace", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["release"], "summary": "List releases in namespace", "parameters": [{"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/schemas.ReleaseResponseStruct"}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/api/namespaces": {"get": {"description": "List user namespaces", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["namespaces"], "summary": "List user namespaces", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}, "post": {"description": "List user namespaces", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["namespaces"], "summary": "List user namespaces", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/api/release/uninstall": {"post": {"description": "Uninstall release from namespace", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["release"], "summary": "Uninstall release", "parameters": [{"type": "string", "description": "Cluster name", "name": "cluster", "in": "formData", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespace", "in": "formData", "required": true}, {"type": "string", "description": "Release name", "name": "release", "in": "formData", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "400": {"description": "Missing parameters", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/api/release/{clustername}/{namespacename}/{releasename}": {"get": {"description": "Get release information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["release"], "summary": "Release information", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Release name", "name": "releasename", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/schemas.ReleaseResponseStruct"}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}, "post": {"description": "Get release information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["release"], "summary": "Release information", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Release name", "name": "releasename", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/schemas.ReleaseResponseStruct"}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/api/release/{clustername}/{namespacename}/{releasename}/backups": {"get": {"description": "Release backup listing", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["release"], "summary": "Release backup listing", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Release name", "name": "releasename", "in": "path", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/api/release/{clustername}/{namespacename}/{releasename}/backups/start": {"post": {"description": "Release backup start", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["release"], "summary": "Release backup start", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Release name", "name": "releasename", "in": "path", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/api/release/{clustername}/{namespacename}/{releasename}/notes": {"get": {"description": "Get release notes", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["release"], "summary": "Get release notes", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Release name", "name": "releasename", "in": "path", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}, "post": {"description": "Get release notes", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["release"], "summary": "Get release notes", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Release name", "name": "releasename", "in": "path", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/api/release/{clustername}/{namespacename}/{releasename}/pods": {"get": {"description": "List pods related to a release", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["release"], "summary": "List pods", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Release name", "name": "releasename", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}, "post": {"description": "List pods related to a release", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["release"], "summary": "List pods", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Release name", "name": "releasename", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/api/release/{clustername}/{namespacename}/{releasename}/recommendations": {"get": {"description": "Get resource allocation recommendations", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["release"], "summary": "Get resource allocation recommendations", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Release name", "name": "releasename", "in": "path", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}, "post": {"description": "Get resource allocation recommendations", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["release"], "summary": "Get resource allocation recommendations", "parameters": [{"type": "string", "description": "Cluster name", "name": "clustername", "in": "path", "required": true}, {"type": "string", "description": "Namespace name", "name": "namespacename", "in": "path", "required": true}, {"type": "string", "description": "Release name", "name": "releasename", "in": "path", "required": true}], "responses": {"200": {"description": "ok", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/auth/github": {"get": {"description": "Login with <PERSON><PERSON><PERSON>", "tags": ["auth"], "summary": "Login with <PERSON><PERSON><PERSON>", "responses": {}}, "post": {"description": "Login with <PERSON><PERSON><PERSON>", "tags": ["auth"], "summary": "Login with <PERSON><PERSON><PERSON>", "responses": {}}}, "/auth/github/callback": {"get": {"description": "<PERSON><PERSON> GitHub login callback", "tags": ["auth"], "summary": "<PERSON><PERSON> GitHub login callback", "responses": {}}, "post": {"description": "<PERSON><PERSON> GitHub login callback", "tags": ["auth"], "summary": "<PERSON><PERSON> GitHub login callback", "responses": {}}}, "/auth/google": {"get": {"description": "Link Google account", "tags": ["auth"], "summary": "Link Google account", "responses": {}}, "post": {"description": "Link Google account", "tags": ["auth"], "summary": "Link Google account", "responses": {}}}, "/auth/google/callback": {"get": {"description": "Handle Google login callback", "tags": ["auth"], "summary": "Handle Google login callback", "responses": {}}, "post": {"description": "Handle Google login callback", "tags": ["auth"], "summary": "Handle Google login callback", "responses": {}}}, "/auth/google/unlink": {"get": {"description": "Unlink Google account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Unlink Google account", "responses": {"302": {"description": "Found", "schema": {"$ref": "#/definitions/routes.apiAuthResponseStruct"}}}}, "post": {"description": "Unlink Google account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Unlink Google account", "responses": {"302": {"description": "Found", "schema": {"$ref": "#/definitions/routes.apiAuthResponseStruct"}}}}}, "/auth/login/methods": {"get": {"description": "Get enabled login methods", "produces": ["application/json"], "tags": ["auth"], "summary": "Get enabled login methods", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}}}}, "/auth/login/success": {"get": {"description": "Get user login status", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Get user login status", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/routes.apiAuthResponseStruct"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/routes.apiAuthResponseStruct"}}}}, "post": {"description": "Get user login status", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Get user login status", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/routes.apiAuthResponseStruct"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/routes.apiAuthResponseStruct"}}}}}, "/auth/logout": {"get": {"description": "Logout user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Logout user", "responses": {"302": {"description": "Found", "schema": {"$ref": "#/definitions/routes.apiAuthResponseStruct"}}}}, "post": {"description": "Logout user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Logout user", "responses": {"302": {"description": "Found", "schema": {"$ref": "#/definitions/routes.apiAuthResponseStruct"}}}}}, "/auth/password": {"post": {"description": "Login with password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Login with password", "parameters": [{"description": "Password", "name": "password", "in": "body", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "401": {"description": "Unauthorized", "schema": {"type": "object"}}}}}}, "definitions": {"release.Info": {"type": "object", "properties": {"deleted": {"description": "Deleted tracks when this object was deleted.", "type": "string"}, "description": {"description": "Description is human-friendly \"log entry\" about this release.", "type": "string"}, "first_deployed": {"description": "FirstDeployed is when the release was first deployed.", "type": "string"}, "last_deployed": {"description": "LastDeployed is when the release was last deployed.", "type": "string"}, "notes": {"description": "Contains the rendered templates/NOTES.txt if available", "type": "string"}, "resources": {"description": "Contains the deployed resources information", "type": "object", "additionalProperties": {"type": "array", "items": {}}}, "status": {"description": "Status is the current state of the release", "allOf": [{"$ref": "#/definitions/release.Status"}]}}}, "release.Status": {"type": "string", "enum": ["unknown", "deployed", "uninstalled", "superseded", "failed", "uninstalling", "pending-install", "pending-upgrade", "pending-rollback"], "x-enum-varnames": ["StatusUnknown", "StatusDeployed", "StatusUninstalled", "StatusSuperseded", "StatusFailed", "StatusUninstalling", "StatusPendingInstall", "StatusPendingUpgrade", "StatusPendingRollback"]}, "routes.apiAuthResponseStruct": {"type": "object", "properties": {"authenticated": {"type": "boolean"}, "message": {"type": "string"}, "user": {"$ref": "#/definitions/routes.apiUserResponseStruct"}}}, "routes.apiUserResponseStruct": {"type": "object", "properties": {"githubId": {"type": "string"}, "githubOrgScope": {"type": "string"}, "githubProfileImageUrl": {"type": "string"}, "githubScreenName": {"type": "string"}, "googleEmail": {"type": "string"}, "googleId": {"type": "string"}, "googlePictureUrl": {"type": "string"}, "name": {"type": "string"}}}, "schemas.ReleaseResponseStruct": {"type": "object", "properties": {"cluster": {"type": "string"}, "clustercolor": {"type": "string"}, "info": {"$ref": "#/definitions/release.Info"}, "key": {"type": "string"}, "namespace": {"type": "string"}, "release": {"type": "string"}, "status": {"type": "string"}, "values": {"type": "object", "additionalProperties": true}, "version": {"type": "integer"}}}}}