definitions:
  release.Info:
    properties:
      deleted:
        description: Deleted tracks when this object was deleted.
        type: string
      description:
        description: Description is human-friendly "log entry" about this release.
        type: string
      first_deployed:
        description: FirstDeployed is when the release was first deployed.
        type: string
      last_deployed:
        description: LastDeployed is when the release was last deployed.
        type: string
      notes:
        description: Contains the rendered templates/NOTES.txt if available
        type: string
      resources:
        additionalProperties:
          items: {}
          type: array
        description: Contains the deployed resources information
        type: object
      status:
        allOf:
        - $ref: '#/definitions/release.Status'
        description: Status is the current state of the release
    type: object
  release.Status:
    enum:
    - unknown
    - deployed
    - uninstalled
    - superseded
    - failed
    - uninstalling
    - pending-install
    - pending-upgrade
    - pending-rollback
    type: string
    x-enum-varnames:
    - StatusUnknown
    - StatusDeployed
    - StatusUninstalled
    - StatusSuperseded
    - StatusFailed
    - StatusUninstalling
    - StatusPendingInstall
    - StatusPendingUpgrade
    - StatusPendingRollback
  routes.apiAuthResponseStruct:
    properties:
      authenticated:
        type: boolean
      message:
        type: string
      user:
        $ref: '#/definitions/routes.apiUserResponseStruct'
    type: object
  routes.apiUserResponseStruct:
    properties:
      githubId:
        type: string
      githubOrgScope:
        type: string
      githubProfileImageUrl:
        type: string
      githubScreenName:
        type: string
      googleEmail:
        type: string
      googleId:
        type: string
      googlePictureUrl:
        type: string
      name:
        type: string
    type: object
  schemas.ReleaseResponseStruct:
    properties:
      cluster:
        type: string
      clustercolor:
        type: string
      info:
        $ref: '#/definitions/release.Info'
      key:
        type: string
      namespace:
        type: string
      release:
        type: string
      status:
        type: string
      values:
        additionalProperties: true
        type: object
      version:
        type: integer
    type: object
info:
  contact: {}
paths:
  /api/backups/{clustername}/{namespacename}/{releasename}/{filename}:
    get:
      consumes:
      - application/json
      description: Backups item download
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Release name
        in: path
        name: releasename
        required: true
        type: string
      - description: Filename
        in: path
        name: filename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Backups item download
      tags:
      - files
  /api/circleci/reload-ns-rbac:
    post:
      consumes:
      - application/x-www-form-urlencoded
      description: |-
        Initiates RBAC regeneration for linked (Github to Google) users and creates CircleCI Service Account for provided namespace.
        Service Account key is injected into CircleCI project environment variables.
        After successful completion, only CircleCI service account and users with push access to the repository will have access to the namespace.
        This endpoint also performs key rotation when it is older than 30 days.
      parameters:
      - description: Secret key for reloading RBAC
        in: formData
        name: rbac_reload_key
        required: true
        type: string
      - description: Namespace name
        in: formData
        name: namespace
        required: true
        type: string
      - description: Cluster name
        in: formData
        name: cluster
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Missing or incorrect parameters
          schema:
            type: string
      summary: Reload namespace RBAC from CircleCI project
      tags:
      - rbac
  /api/events/{clustername}/{namespacename}/:
    get:
      consumes:
      - application/json
      description: Retrieve pod events
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Retrieve pod events
      tags:
      - logs
    post:
      consumes:
      - application/json
      description: Retrieve pod events
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Retrieve pod events
      tags:
      - logs
  /api/events/{clustername}/{namespacename}/{podname}:
    get:
      consumes:
      - application/json
      description: Retrieve pod events
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Pod name
        in: path
        name: podname
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Retrieve pod events
      tags:
      - logs
    post:
      consumes:
      - application/json
      description: Retrieve pod events
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Pod name
        in: path
        name: podname
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Retrieve pod events
      tags:
      - logs
  /api/logs/{clustername}/{namespacename}/{podname}/{containername}:
    get:
      consumes:
      - application/json
      description: Retrieve container logs
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Pod name
        in: path
        name: podname
        required: true
        type: string
      - description: Container name
        in: path
        name: containername
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Retrieve container logs
      tags:
      - logs
    post:
      consumes:
      - application/json
      description: Retrieve container logs
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Pod name
        in: path
        name: podname
        required: true
        type: string
      - description: Container name
        in: path
        name: containername
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Retrieve container logs
      tags:
      - logs
  /api/namespace/{namespacename}/releases:
    get:
      consumes:
      - application/json
      description: List releases in namespace
      parameters:
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/schemas.ReleaseResponseStruct'
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: List releases in namespace
      tags:
      - release
    post:
      consumes:
      - application/json
      description: List releases in namespace
      parameters:
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/schemas.ReleaseResponseStruct'
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: List releases in namespace
      tags:
      - release
  /api/namespaces:
    get:
      consumes:
      - application/json
      description: List user namespaces
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: List user namespaces
      tags:
      - namespaces
    post:
      consumes:
      - application/json
      description: List user namespaces
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: List user namespaces
      tags:
      - namespaces
  /api/release/{clustername}/{namespacename}/{releasename}:
    get:
      consumes:
      - application/json
      description: Get release information
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Release name
        in: path
        name: releasename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/schemas.ReleaseResponseStruct'
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Release information
      tags:
      - release
    post:
      consumes:
      - application/json
      description: Get release information
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Release name
        in: path
        name: releasename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/schemas.ReleaseResponseStruct'
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Release information
      tags:
      - release
  /api/release/{clustername}/{namespacename}/{releasename}/backups:
    get:
      consumes:
      - application/json
      description: Release backup listing
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Release name
        in: path
        name: releasename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Release backup listing
      tags:
      - release
  /api/release/{clustername}/{namespacename}/{releasename}/backups/start:
    post:
      consumes:
      - application/json
      description: Release backup start
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Release name
        in: path
        name: releasename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Release backup start
      tags:
      - release
  /api/release/{clustername}/{namespacename}/{releasename}/notes:
    get:
      consumes:
      - application/json
      description: Get release notes
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Release name
        in: path
        name: releasename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Get release notes
      tags:
      - release
    post:
      consumes:
      - application/json
      description: Get release notes
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Release name
        in: path
        name: releasename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Get release notes
      tags:
      - release
  /api/release/{clustername}/{namespacename}/{releasename}/pods:
    get:
      consumes:
      - application/json
      description: List pods related to a release
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Release name
        in: path
        name: releasename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              items:
                type: string
              type: array
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: List pods
      tags:
      - release
    post:
      consumes:
      - application/json
      description: List pods related to a release
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Release name
        in: path
        name: releasename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              items:
                type: string
              type: array
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: List pods
      tags:
      - release
  /api/release/{clustername}/{namespacename}/{releasename}/recommendations:
    get:
      consumes:
      - application/json
      description: Get resource allocation recommendations
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Release name
        in: path
        name: releasename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Get resource allocation recommendations
      tags:
      - release
    post:
      consumes:
      - application/json
      description: Get resource allocation recommendations
      parameters:
      - description: Cluster name
        in: path
        name: clustername
        required: true
        type: string
      - description: Namespace name
        in: path
        name: namespacename
        required: true
        type: string
      - description: Release name
        in: path
        name: releasename
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Get resource allocation recommendations
      tags:
      - release
  /api/release/uninstall:
    post:
      consumes:
      - application/json
      description: Uninstall release from namespace
      parameters:
      - description: Cluster name
        in: formData
        name: cluster
        required: true
        type: string
      - description: Namespace name
        in: formData
        name: namespace
        required: true
        type: string
      - description: Release name
        in: formData
        name: release
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            type: string
        "400":
          description: Missing parameters
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: Uninstall release
      tags:
      - release
  /auth/github:
    get:
      description: Login with Github
      responses: {}
      summary: Login with Github
      tags:
      - auth
    post:
      description: Login with Github
      responses: {}
      summary: Login with Github
      tags:
      - auth
  /auth/github/callback:
    get:
      description: Handle GitHub login callback
      responses: {}
      summary: Handle GitHub login callback
      tags:
      - auth
    post:
      description: Handle GitHub login callback
      responses: {}
      summary: Handle GitHub login callback
      tags:
      - auth
  /auth/google:
    get:
      description: Link Google account
      responses: {}
      summary: Link Google account
      tags:
      - auth
    post:
      description: Link Google account
      responses: {}
      summary: Link Google account
      tags:
      - auth
  /auth/google/callback:
    get:
      description: Handle Google login callback
      responses: {}
      summary: Handle Google login callback
      tags:
      - auth
    post:
      description: Handle Google login callback
      responses: {}
      summary: Handle Google login callback
      tags:
      - auth
  /auth/google/unlink:
    get:
      consumes:
      - application/json
      description: Unlink Google account
      produces:
      - application/json
      responses:
        "302":
          description: Found
          schema:
            $ref: '#/definitions/routes.apiAuthResponseStruct'
      summary: Unlink Google account
      tags:
      - auth
    post:
      consumes:
      - application/json
      description: Unlink Google account
      produces:
      - application/json
      responses:
        "302":
          description: Found
          schema:
            $ref: '#/definitions/routes.apiAuthResponseStruct'
      summary: Unlink Google account
      tags:
      - auth
  /auth/login/methods:
    get:
      description: Get enabled login methods
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
      summary: Get enabled login methods
      tags:
      - auth
  /auth/login/success:
    get:
      consumes:
      - application/json
      description: Get user login status
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/routes.apiAuthResponseStruct'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/routes.apiAuthResponseStruct'
      summary: Get user login status
      tags:
      - auth
    post:
      consumes:
      - application/json
      description: Get user login status
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/routes.apiAuthResponseStruct'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/routes.apiAuthResponseStruct'
      summary: Get user login status
      tags:
      - auth
  /auth/logout:
    get:
      consumes:
      - application/json
      description: Logout user
      produces:
      - application/json
      responses:
        "302":
          description: Found
          schema:
            $ref: '#/definitions/routes.apiAuthResponseStruct'
      summary: Logout user
      tags:
      - auth
    post:
      consumes:
      - application/json
      description: Logout user
      produces:
      - application/json
      responses:
        "302":
          description: Found
          schema:
            $ref: '#/definitions/routes.apiAuthResponseStruct'
      summary: Logout user
      tags:
      - auth
  /auth/password:
    post:
      consumes:
      - application/json
      description: Login with password
      parameters:
      - description: Password
        in: body
        name: password
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
        "401":
          description: Unauthorized
          schema:
            type: object
      summary: Login with password
      tags:
      - auth
swagger: "2.0"
