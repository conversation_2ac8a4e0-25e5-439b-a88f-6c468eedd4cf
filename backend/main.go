package main

import (
	"encoding/gob"
	"flag"
	"log"
	"net/http"
	"net/url"
	"os"
	"time"

	// HTTP router that provides parameters, wildcards and regexp patterns
	"github.com/alexedwards/flow"

	// Cors	middleware
	"github.com/rs/cors"

	// HTTP Session Management
	"github.com/alexedwards/scs/mongodbstore"
	"github.com/alexedwards/scs/v2"

	// Chainable login http.Handler's for popular oauth providers.
	"github.com/dghubble/gologin/v2"
	"github.com/dghubble/gologin/v2/github"
	"github.com/dghubble/gologin/v2/google"

	// OAuth2 library
	"golang.org/x/oauth2"

	// Github OAuth2 endpoint. name overriden to avoid confusion with dghubble github package
	githubOAuth2 "golang.org/x/oauth2/github"
	googleOAuth2 "golang.org/x/oauth2/google"

	// Swagger Docs

	"github.com/wunderio/silta-dashboard/backend/docs"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/db"
	"github.com/wunderio/silta-dashboard/backend/routes"
	"github.com/wunderio/silta-dashboard/backend/schemas"
	"github.com/wunderio/silta-dashboard/backend/services"
)

var sessionManager *scs.SessionManager

// Start the API web server
func apiserver() {

	// Initialize a new session manager and configure the session lifetime.
	sessionManager = scs.New()
	sessionManager.Cookie.Persist = false
	sessionManager.Lifetime = conf.SessionLifetime
	// Local mode
	sessionManager.Cookie.Secure = false
	// Store session in MongoDB
	sessionManager.Store = mongodbstore.New(db.Database)

	githubOauth2Config := &oauth2.Config{
		ClientID:     conf.GithubClientID,
		ClientSecret: conf.GithubClientSecret,
		RedirectURL:  conf.BackendURL + conf.BackendRoutePrefix + "/auth/github/callback",
		Endpoint:     githubOAuth2.Endpoint,
		// "repo:status" scope insufficient for repos+affiliation=collaborator, needs full "repos" scope. otherwise /user/repos gets partial list only
		Scopes: []string{"repo"},
	}

	googleOauth2Config := &oauth2.Config{
		ClientID:     conf.GoogleClientID,
		ClientSecret: conf.GoogleClientSecret,
		RedirectURL:  conf.BackendURL + conf.BackendRoutePrefix + "/auth/google/callback",
		Endpoint:     googleOAuth2.Endpoint,
		Scopes:       []string{"profile", "email"},
	}

	// Define Mux Router
	// mux := http.NewServeMux()
	mux := flow.New()

	rh := routes.RouteHandler{
		Session: sessionManager,
		Conf:    conf.GetConfig(),
	}

	// Login methods
	mux.HandleFunc(conf.BackendRoutePrefix+"/auth/login/methods", rh.LoginMethodsHandler)

	// Session state display handler
	mux.HandleFunc(conf.BackendRoutePrefix+"/auth/login/success", rh.LoginStatusHandler)

	// Password login
	mux.HandleFunc(conf.BackendRoutePrefix+"/auth/password", rh.PasswordLoginHandler, "POST")

	// Session logout handler
	mux.HandleFunc(conf.BackendRoutePrefix+"/auth/logout", rh.LogoutHandler)

	// Github Oauth callbacks
	stateConfig := gologin.DefaultCookieConfig
	mux.Handle(conf.BackendRoutePrefix+"/auth/github", rh.GithubLoginHandler(stateConfig, github.LoginHandler(githubOauth2Config, nil)))
	mux.Handle(conf.BackendRoutePrefix+"/auth/github/callback", github.StateHandler(stateConfig, github.CallbackHandler(githubOauth2Config, rh.GitHubCallbackHandler(), nil)))

	// Google Oauth callbacks
	mux.Handle(conf.BackendRoutePrefix+"/auth/google", rh.GoogleLoginHandler(stateConfig, google.LoginHandler(googleOauth2Config, nil)))
	mux.Handle(conf.BackendRoutePrefix+"/auth/google/callback", google.StateHandler(stateConfig, google.CallbackHandler(googleOauth2Config, rh.GoogleCallbackHandler(), nil)))
	mux.HandleFunc(conf.BackendRoutePrefix+"/auth/google/unlink", rh.GoogleUnlinkHandler)

	// On demand namespace rbac creation
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/circleci/reload-ns-rbac", rh.CircleCiNamespaceRbacCreationHandler, "POST")

	// Namespaces
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/namespaces", rh.NamespaceListHandler)

	// Releases
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/namespace/:namespacename/releases", rh.ReleaseListHandler)

	// Release info
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/release/:clustername/:namespacename/:releasename", rh.ReleaseInfoHandler)

	// Pods in release
	// TODO: Rewrite to actual release pod list (ReleasePodListHandler).
	// Except if release name is "no-release", it's a fallback, list pods based on label selector.
	// mux.HandleFunc(conf.BackendRoutePrefix+"/api/release/:clustername/:namespacename/no-release/pods", rh.PodsListHandler)
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/release/:clustername/:namespacename/:releasename/pods", rh.PodsListHandler)

	// Release sync
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/release/:clustername/:namespacename/:releasename/sync/status", rh.ReleaseSyncStatusHandler)
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/release/:clustername/:namespacename/:releasename/sync/start", rh.ReleaseSyncStartHandler, "POST")
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/release/:clustername/:namespacename/:releasename/sync/cancel", rh.ReleaseSyncCancelHandler)
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/release/:clustername/:namespacename/:releasename/sync/ack", rh.ReleaseSyncStatusAckHandler)

	// Uninstall release, only accept POST requests
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/release/uninstall", rh.ReleaseUninstallHandler, "POST")

	// Container logs
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/logs/:clustername/:namespacename/:podname/:containername", rh.ContainerLogHandler)

	// Pod events
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/events/:clustername/:namespacename", rh.EventLogHandler)
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/events/:clustername/:namespacename/:podname", rh.EventLogHandler)

	// Release notes
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/release/:clustername/:namespacename/:releasename/notes", rh.ReleaseNotesHandler)

	// Resource allocation recommendations
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/release/:clustername/:namespacename/:releasename/recommendations", rh.ResourceAllocationHandler)

	// Backups
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/release/:clustername/:namespacename/:releasename/backups", rh.BackupListingHandler)
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/release/:clustername/:namespacename/:releasename/backups/start", rh.BackupStartHandler)
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/backups/:clustername/:namespacename/:releasename/...", rh.BackupDownloadHandler)

	// Inventory
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/inventory/clusters", rh.InventoryClustersHandler)
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/inventory/images", rh.InventoryImagesHandler)
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/inventory/images/popular", rh.InventoryImagesPopularHandler)
	mux.HandleFunc(conf.BackendRoutePrefix+"/api/inventory/components", rh.InventoryComponentsHandler)

	// Index
	mux.HandleFunc(conf.BackendRoutePrefix+"/", rh.IndexHandler)

	// Swagger docs
	// parse conf.BackendURL, extract scheme and host from it
	u, err := url.ParseRequestURI(conf.BackendURL)
	if err != nil {
		log.Fatal(err)
	}
	docs.SwaggerInfo.Host = u.Host
	docs.SwaggerInfo.Schemes = []string{u.Scheme}
	docs.SwaggerInfo.BasePath = conf.BackendRoutePrefix

	mux.HandleFunc(conf.BackendRoutePrefix+"/swagger/...", rh.SwaggerDocHandler)

	c := cors.New(cors.Options{
		AllowedOrigins:   []string{conf.FrontendURL},
		AllowCredentials: true,
		AllowedHeaders:   []string{"Access-Control-Allow-Credentials", "Content-Type", "Cache-Control"},
		AllowedMethods:   []string{http.MethodGet, http.MethodPost},
		Debug:            false,
	})

	// Start HTTP server
	server := &http.Server{
		Addr:        ":" + conf.Port,
		Handler:     sessionManager.LoadAndSave(c.Handler(mux)),
		ReadTimeout: time.Second * 30,
		// Download for larger files break on 30s timeout
		// WriteTimeout: time.Second * 30,
	}

	log.Printf("Starting server on %s", conf.Port)
	err = server.ListenAndServe()
	if err != nil {
		log.Fatal(err)
	}
}

func isFlagPassed(name string) bool {
	found := false
	flag.Visit(func(f *flag.Flag) {
		if f.Name == name {
			found = true
		}
	})
	return found
}

// Define router and start server
func main() {
	gob.Register(&schemas.User{})

	flag.Bool("recreate-gh-rbac", false, "Recreate Github RBAC")
	flag.Bool("recreate-cci-rbac", false, "Recreate CircleCI RBAC")
	flag.Bool("cluster-inventory", false, "Gather cluster inventory")
	flag.Parse()

	// Connect to MongoDB
	db.Connect()

	if isFlagPassed("recreate-gh-rbac") || isFlagPassed("recreate-cci-rbac") {
		log.Println("Recreating RBAC ...")

		err := services.CreateAllNamespaceRBAC(isFlagPassed("recreate-gh-rbac"), isFlagPassed("recreate-cci-rbac"))
		if err != nil {
			log.Fatal(err)
		}

		os.Exit(0)
		return
	}

	if isFlagPassed("cluster-inventory") {
		log.Println("Gathering cluster inventory ...")

		services.CreateClusterInventory()
		os.Exit(0)
		return
	}

	// Start API server
	apiserver()
}
