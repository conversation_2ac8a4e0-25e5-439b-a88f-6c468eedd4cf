package routes

import (
	"encoding/json"
	"log"
	"net/http"
	"strconv"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/services"
)

type apiUserResponseStruct struct {
	Name                  string   `json:"name"`
	Roles                 []string `json:"roles"`
	GithubScreenName      string   `json:"githubScreenName"`
	GithubId              string   `json:"githubId"`
	GithubProfileImageUrl string   `json:"githubProfileImageUrl"`
	GithubOrgScope        string   `json:"githubOrgScope"`
	GoogleId              string   `json:"googleId"`
	GooglePictureUrl      string   `json:"googlePictureUrl"`
	GoogleEmail           string   `json:"googleEmail"`
}

type apiAuthResponseStruct struct {
	Authenticated bool                  `json:"authenticated"`
	Message       string                `json:"message"`
	User          apiUserResponseStruct `json:"user"`
}

// Destroy session and redirect to frontend
// Swagger documentation block:
// @Summary Logout user
// @Description Logout user
// @Tags auth
// @Accept json
// @Produce json
// @Success 302 {object} apiAuthResponseStruct
// @Router /auth/logout [get]
// @Router /auth/logout [post]
func (rh *RouteHandler) LogoutHandler(w http.ResponseWriter, r *http.Request) {

	rh.Session.Destroy(r.Context())
	http.Redirect(w, r, conf.FrontendURL+conf.FrontendRoutePrefix, http.StatusFound)
}

// HandleLoginStatus is the handler for the /auth/login/success route
// Swagger documentation block:
// @Summary Get user login status
// @Description Get user login status
// @Tags auth
// @Accept json
// @Produce json
// @Success 200 {object} apiAuthResponseStruct
// @Failure 401 {object} apiAuthResponseStruct
// @Router /auth/login/success [get]
// @Router /auth/login/success [post]
func (rh *RouteHandler) LoginStatusHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		rh.StatusUnauthorized(w, r)
		return
	}

	// User is authenticated, return user data
	user, err := services.GetUserBySessionToken(sessionToken)
	if err != nil {
		log.Printf("[LoginStatusHandler] User not found: %s\n", err)
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// User found, return user data
	resp := apiAuthResponseStruct{
		Authenticated: true,
		Message:       "user has successfully authenticated",
		User: apiUserResponseStruct{
			Name:             user.Name,
			GoogleId:         user.Google.GoogleId,
			GooglePictureUrl: user.Google.PictureUrl,
			GoogleEmail:      user.Google.Email,
		},
	}

	userRoles := []string{}

	if conf.PasswordLoginEnabled && user.Name == "local-user" {
		resp.User.GithubOrgScope = "internal"
		userRoles = append(userRoles, "internal")
		userRoles = append(userRoles, "admin")
	}

	// user.Github is not empty, add Github data to response
	if conf.GithubLoginEnabled {
		if user.Github.GithubId != nil {
			resp.User.GithubId = strconv.FormatInt(*user.Github.GithubId, 10)
			resp.User.GithubScreenName = *user.Github.ScreenName
			resp.User.GithubProfileImageUrl = *user.Github.ProfileImageUrl
			resp.User.GithubOrgScope = user.Github.OrgScope

			if user.Github.OrgScope == "internal" {
				userRoles = append(userRoles, "internal")
			}

			if user.Github.OrgAdmin {
				userRoles = append(userRoles, "admin")
			}
		}
	}

	resp.User.Roles = userRoles

	json.NewEncoder(w).Encode(resp)
}
