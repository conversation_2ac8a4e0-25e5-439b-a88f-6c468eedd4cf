package routes

import (
	"log"
	"net/http"

	httpSwagger "github.com/swaggo/http-swagger"
	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/services"
)

func (rh *RouteHandler) SwaggerDocHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: api documentation")
		rh.StatusUnauthorized(w, r)
		return
	}

	out := httpSwagger.Handler(httpSwagger.URL(conf.BackendURL + conf.BackendRoutePrefix + "/swagger/doc.json"))
	out.ServeHTTP(w, r)

}
