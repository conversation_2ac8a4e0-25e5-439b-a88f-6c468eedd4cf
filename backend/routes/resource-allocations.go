package routes

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/services"
	"gopkg.in/yaml.v2"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"

	"github.com/alexedwards/flow"
)

type sizingRequest struct {
	Cpu    any `json:"cpu"`
	Memory any `json:"memory"`
	Total  any `json:"total"`
}

type sizingRecommendation struct {
	ClusterID          string        `json:"clusterID"`
	Namespace          string        `json:"namespace"`
	ControllerKind     string        `json:"controllerKind"`
	ControllerName     string        `json:"controllerName"`
	ContainerName      string        `json:"containerName"`
	RecommendedRequest sizingRequest `json:"recommendedRequest"`
	MonthlySavings     sizingRequest `json:"monthlySavings"`
	LatestKnownRequest sizingRequest `json:"latestKnownRequest"`
	CurrentEfficiency  sizingRequest `json:"currentEfficiency"`
}

type sizingResponse struct {
	TotalMonthlySavings any                    `json:"totalMonthlySavings"`
	Count               int                    `json:"count"`
	Recommendations     []sizingRecommendation `json:"recommendations"`
}

type allocationRecommendationRequest struct {
	Cpu    string `json:"cpu,omitempty"`
	Memory string `json:"memory,omitempty"`
}
type allocationRecommendation struct {
	Type                    string                          `json:"Type"`
	Resource                string                          `json:"Resource"`
	Container               string                          `json:"Container"`
	CurrentRequest          allocationRecommendationRequest `json:"CurrentRequest"`
	RecommendedRequest      allocationRecommendationRequest `json:"RecommendedRequest"`
	EstimatedMonthlySavings allocationRecommendationRequest `json:"EstimatedMonthlySavings"`
	Snippet                 string                          `json:"Snippet"`
}

// Configuration snippet for each resource
type resourceConfiguration struct {
	Cpu    string `json:",omitempty" yaml:",omitempty"`
	Memory string `json:",omitempty" yaml:",omitempty"`
}

// Resource allocation handler
// Swagger documentation block:
// @Summary Get resource allocation recommendations
// @Description Get resource allocation recommendations
// @Tags release
// @Accept json
// @Produce json
// @Param clustername  path  string true "Cluster name"
// @Param namespacename path  string true "Namespace name"
// @Param releasename  path  string true "Release name"
// @Success 200 {string} string "ok"
// @Failure 401 {string} string "Unauthorized"
// @Router /api/release/{clustername}/{namespacename}/{releasename}/recommendations [get]
// @Router /api/release/{clustername}/{namespacename}/{releasename}/recommendations [post]
func (rh *RouteHandler) ResourceAllocationHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: recommendations list")
		rh.StatusUnauthorized(w, r)
		return
	}

	clusterKey := flow.Param(r.Context(), "clustername")
	namespaceName := flow.Param(r.Context(), "namespacename")
	releaseName := flow.Param(r.Context(), "releasename")

	// Check if user has access to namespace
	namespaceAccess := services.ValidateNamespaceAccess(sessionToken, namespaceName)
	if !namespaceAccess {
		log.Println("Unauthorized access attempt: recommendations list (no access to project)")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	log.Printf("[AUDIT] User '%s' requested resource assignment recommendations for '%s' deployment in '%s' namespace", user.Name, releaseName, namespaceName)

	// Check if kubecost is configured for cluster
	if !conf.KubeClusters[clusterKey].KubeCost.Enabled {
		w.Write([]byte("[]"))
		return
	}

	// Get clusterID from config
	kubecostClusterID := conf.KubeClusters[clusterKey].KubeCost.ClusterID
	kubecostApiNamespace := conf.KubeClusters[clusterKey].KubeCost.Namespace
	kubecostApiService := conf.KubeClusters[clusterKey].KubeCost.Service
	kubecostApiPort := conf.KubeClusters[clusterKey].KubeCost.Port
	kubecostApiProtocol := conf.KubeClusters[clusterKey].KubeCost.Protocol
	kubecostApiServer := fmt.Sprintf("%s://%s.%s.svc.cluster.local:%s", kubecostApiProtocol, kubecostApiService, kubecostApiNamespace, kubecostApiPort)

	// If kubecost is not fully configured, print error and return empty json
	if kubecostClusterID == "" || kubecostApiNamespace == "" || kubecostApiService == "" || kubecostApiPort == "" || kubecostApiProtocol == "" {
		log.Printf("Error: kubecost is not fully configured for cluster '%s'", clusterKey)
		w.Write([]byte("[]"))
		return
	}

	// create http request to service in cluster
	cluster := conf.KubeClusters[clusterKey]

	CaData, err := base64.StdEncoding.DecodeString(cluster.Cluster.CaData)
	if err != nil {
		log.Printf("Error connecting to cluster: %s", err)
		return
	}

	// Load kubernetes configuration from cluster and user
	restConfig := &rest.Config{
		Host:        cluster.Cluster.Server,
		BearerToken: cluster.User.Token,
		TLSClientConfig: rest.TLSClientConfig{
			CAData: CaData,
		},
	}
	clientset, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		log.Printf("Error connecting to kubernetes cluster: %s", err.Error())
	}

	// Hardcoded query params for kubecost api
	var kubecostQueryParams = map[string]string{
		// Make sure you have the appropriate data pipeline enabled.
		"algorithmCPU": "quantile",
		"algorithmRAM": "quantile",
		// Only relevant with quantile algorithm
		"qCPU":                 "0.95",
		"qRAM":                 "0.95",
		"targetCPUUtilization": "0.8",
		"targetRAMUtilization": "0.8",
		"window":               "7d",
		"sortBy":               "totalSavings",
		"sortByOrder":          "descending",
	}

	// Set filter to namespace and cluster
	kubecostQueryParams["filter"] = "namespace%3A" + "%22" + namespaceName + "%22" +
		"%2B" + "cluster%3A" + "%22" + kubecostClusterID + "%22"

	apiUrl := kubecostQueryUrl(kubecostApiServer+"/model/savings/requestSizingV2?", kubecostQueryParams)

	srj, err := kubecostApiRequest(clientset, restConfig, kubecostApiNamespace, kubecostApiService, kubecostApiPort, apiUrl)
	if err != nil {
		log.Printf("Error getting sizing recommendations: %s", err.Error())
		w.Write([]byte("[]"))
		return
	}

	// If response contains error below, retry with 1d window.
	// 2023/11/28 23:58:15 Sizing recommendations API response: Error Recommending Request Sizes: Failed to query histogram data for
	// [2023-11-22 00:00:00 +0000 UTC, 2023-11-29 00:00:00 +0000 UTC]:
	// querying window {Start:2023-11-27 00:00:00 +0000 UTC End:2023-11-28 00:00:00 +0000 UTC}: window is expected but not available
	if strings.Contains(srj, "Error Recommending Request Sizes: Failed to query histogram data for") && strings.Contains(srj, "window is expected but not available") {
		log.Println("Info: Selected window is not available, retrying with 1d window.")

		kubecostQueryParams["window"] = "1d"
		apiUrl = kubecostQueryUrl(kubecostApiServer+"/model/savings/requestSizingV2?", kubecostQueryParams)

		srj, err = kubecostApiRequest(clientset, restConfig, kubecostApiNamespace, kubecostApiService, kubecostApiPort, apiUrl)
		if err != nil {
			log.Printf("Error getting sizing recommendations: %s", err.Error())
			w.Write([]byte("[]"))
			return
		}
	}

	// If response contains either of errors below, retry with default algorithm
	apiErrorMessages := []string{
		"Error Recommending Request Sizes: Quantile algorithm requested but no histogram querier is available. Make sure you have the appropriate data pipeline enabled.",
		"Error Recommending Request Sizes: the only supported CPU algorithm for Waterfowl is Max",
		"Error Recommending Request Sizes: the quantile algorithm is unsupported for CPU",
	}
	for _, apiErrorMessage := range apiErrorMessages {
		if strings.Contains(srj, apiErrorMessage) {

			// https://docs.kubecost.com/architecture/containerstats-pipeline
			// helm upgrade --install --repo https://kubecost.github.io/cost-analyzer/ kubecost cost-analyzer -n kubecost --reuse-values --set kubecostModel.containerStatsEnabled=true
			log.Println("Info: Containerstats pipeline is not enabled in this kubecost instance, retrying with default algorithm (max).")

			kubecostQueryParams["algorithmCPU"] = "max"
			kubecostQueryParams["algorithmRAM"] = "max"
			apiUrl = kubecostQueryUrl(kubecostApiServer+"/model/savings/requestSizingV2?", kubecostQueryParams)

			srj, err = kubecostApiRequest(clientset, restConfig, kubecostApiNamespace, kubecostApiService, kubecostApiPort, apiUrl)
			if err != nil {
				log.Printf("Error getting sizing recommendations: %s", err.Error())
				w.Write([]byte("[]"))
				return
			}

			break
		}
	}

	var sizingAPIResponse sizingResponse

	err = json.Unmarshal([]byte(srj), &sizingAPIResponse)
	if err != nil {
		log.Printf("Error unmarshalling sizing recommendations json: %s", err.Error())
		log.Printf("Sizing recommendations API response: %s", srj)
		w.Write([]byte("[]"))
		return
	}

	// Load release statefulsets and deployments
	rss := services.GetReleaseResources(clusterKey, namespaceName, releaseName)

	// Get chart name using helm api
	chartName := services.GetReleaseChartName(clusterKey, namespaceName, releaseName)

	var allocationRecommendations []allocationRecommendation

	// Match release resources with sizing recommendations
	for _, sr := range sizingAPIResponse.Recommendations {

		// match sizing recommendation with release resource
		for _, res := range rss {

			if sr.ControllerName == res.Name && sr.ControllerKind == res.Type {

				var containers []v1.Container

				var metadata metav1.ObjectMeta

				if res.Type == "deployment" {
					containers = res.Deployment.Spec.Template.Spec.Containers
					metadata = res.Deployment.ObjectMeta
				} else if res.Type == "statefulset" {
					containers = res.Statefulset.Spec.Template.Spec.Containers
					metadata = res.Statefulset.ObjectMeta
				}

				// iterate containers and match with sizing recommendations
				for _, container := range containers {

					// match sizing recommendation with  container
					if sr.ControllerName == res.Name && sr.ContainerName == container.Name && sr.ControllerKind == res.Type {

						// Build resource configuration object and set values
						recommendationResources := make(map[string]resourceConfiguration)
						recommendationResources["requests"] = resourceConfiguration{
							Cpu:    fmt.Sprintf("%s", sr.RecommendedRequest.Cpu),
							Memory: fmt.Sprintf("%s", sr.RecommendedRequest.Memory),
						}

						rc := resourceConfiguration{}

						// Parse current limit as milliValue
						currentLimitCpuMilli := container.Resources.Limits.Cpu().MilliValue()
						currentLimitMemoryMilli := container.Resources.Limits.Memory().MilliValue()

						limitMultiplier := 1.5

						// Multiply recommended request by 1.5 and store as recommendedLimitCpu
						recommendedLimitCpuString, err := services.CalculateLimit(fmt.Sprintf("%s", sr.RecommendedRequest.Cpu), limitMultiplier)
						if err == nil && recommendedLimitCpuString != "" {

							// Parse recommended limit as milliValue
							recommendedLimitCpu, _ := resource.ParseQuantity(recommendedLimitCpuString)
							recommendedLimitCpuMilli := recommendedLimitCpu.MilliValue()

							// If current limits are less than recommended limit, set recommended limit as limit
							if (currentLimitCpuMilli > 0) && (currentLimitCpuMilli < recommendedLimitCpuMilli) {
								rc.Cpu = recommendedLimitCpuString
							}
						}
						// Multiply recommended request by 1.5 and store as recommendedLimitMemory
						recommendedLimitMemoryString, err := services.CalculateLimit(fmt.Sprintf("%s", sr.RecommendedRequest.Memory), limitMultiplier)
						if err == nil && recommendedLimitMemoryString != "" {

							// Parse recommended limit as milliValue
							recommendedLimitMemory, _ := resource.ParseQuantity(recommendedLimitMemoryString)
							recommendedLimitMemoryMilli := recommendedLimitMemory.MilliValue()

							// If current limits are less than recommended limit, set recommended limit as limit
							if (currentLimitMemoryMilli > 0) && (currentLimitMemoryMilli < recommendedLimitMemoryMilli) {
								rc.Memory = recommendedLimitMemoryString
							}
						}

						// If any limits are adjusted add them to snippet
						if rc.Cpu != "" || rc.Memory != "" {
							recommendationResources["limits"] = rc
						}

						// Build recommendation snippet
						recommendationSnippet := make(map[string]interface{})

						// These will need readjustment as configuration is changed
						if chartName == "drupal" || chartName == "frontend" || chartName == "simple" {

							// resources are set under service with identical name to container
							if container.Name == "nginx" {
								recommendationSnippet[container.Name] = make(map[string]interface{})
								recommendationSnippet[container.Name].(map[string]interface{})["resources"] = recommendationResources
							}
							if container.Name == "sigsci" {
								recommendationSnippet["signalsciences"] = make(map[string]interface{})
								recommendationSnippet["signalsciences"].(map[string]interface{})["resources"] = recommendationResources
							}
						}

						if chartName == "frontend" {

							if metadata.Labels["silta-frontend"] == "service" {
								recommendationSnippet["services"] = make(map[string]interface{})
								recommendationSnippet["services"].(map[string]interface{})[container.Name] = make(map[string]interface{})
								recommendationSnippet["services"].(map[string]interface{})[container.Name].(map[string]interface{})["resources"] = recommendationResources
							}
						}

						if chartName == "drupal" {

							if container.Name == "php" || container.Name == "shell" || container.Name == "memcached" || container.Name == "varnish" {
								// resources are set under service with identical name to container
								recommendationSnippet[container.Name] = make(map[string]interface{})
								recommendationSnippet[container.Name].(map[string]interface{})["resources"] = recommendationResources

							} else if container.Name == "redis" {
								// resources are set under "master" key
								recommendationSnippet[container.Name] = make(map[string]interface{})
								recommendationSnippet[container.Name].(map[string]interface{})["master"] = make(map[string]interface{})
								recommendationSnippet[container.Name].(map[string]interface{})["master"].(map[string]interface{})["resources"] = recommendationResources
							}
						}

						if chartName == "drupal" || chartName == "frontend" {
							// **Deprecated** mailhog is deprecated and will be removed from future Silta releases, mailpit will be used instead.
							if container.Name == "elasticsearch" || container.Name == "mailhog" || container.Name == "mailpit" || container.Name == "solr" {
								// resources are set under service with identical name to container
								recommendationSnippet[container.Name] = make(map[string]interface{})
								recommendationSnippet[container.Name].(map[string]interface{})["resources"] = recommendationResources

							} else if container.Name == "mariadb" {
								// resources are set under "master" key
								recommendationSnippet[container.Name] = make(map[string]interface{})
								recommendationSnippet[container.Name].(map[string]interface{})["master"] = make(map[string]interface{})
								recommendationSnippet[container.Name].(map[string]interface{})["master"].(map[string]interface{})["resources"] = recommendationResources
							}
						}

						// if recommendationSnippet is empty string, skip
						recommendationSnippetYaml := []byte{}

						if len(recommendationSnippet) > 0 {
							recommendationSnippetYaml, err = yaml.Marshal(recommendationSnippet)
							if err != nil {
								log.Printf("Error marshalling recommendationSnippet: %s", err.Error())
							}
						}

						// Calculate estimated monthly savings
						ems := allocationRecommendationRequest{}

						// Only expose cost if user is internal and cost is exposed
						if conf.KubeClusters[clusterKey].KubeCost.ExposeCost && (user.Github.OrgScope == "internal") {
							if sr.MonthlySavings.Cpu != nil {
								ems.Cpu = fmt.Sprintf("%v", sr.MonthlySavings.Cpu)
							}
							if sr.MonthlySavings.Memory != nil {
								ems.Memory = fmt.Sprintf("%v", sr.MonthlySavings.Memory)
							}
						}

						allocationRecommendations = append(allocationRecommendations, allocationRecommendation{
							Type:      res.Type,
							Resource:  res.Name,
							Container: container.Name,
							CurrentRequest: allocationRecommendationRequest{
								Cpu:    fmt.Sprintf("%v", container.Resources.Requests.Cpu().String()),
								Memory: fmt.Sprintf("%v", container.Resources.Requests.Memory().String()),
							},
							RecommendedRequest: allocationRecommendationRequest{
								Cpu:    fmt.Sprintf("%s", sr.RecommendedRequest.Cpu),
								Memory: fmt.Sprintf("%s", sr.RecommendedRequest.Memory),
							},
							EstimatedMonthlySavings: ems,
							Snippet:                 string(recommendationSnippetYaml),
						})
					}
				}
			}
		}
	}

	// create map that wraps allocationRecommendations
	allocationRecommendationsResponse := make(map[string]interface{})
	allocationRecommendationsResponse["recommendations"] = allocationRecommendations

	// Only expose cost if user is internal and cost is exposed
	allocationRecommendationsResponse["c"] = conf.KubeClusters[clusterKey].KubeCost.ExposeCost && (user.Github.OrgScope == "internal")

	json, err := json.Marshal(allocationRecommendationsResponse)
	if err != nil {
		log.Printf("Error marshalling allocation recommendations: %s", err.Error())
		json = []byte("[]")
	}

	w.Write(json)
}

// Build query string from map
func kubecostQueryUrl(apiUrl string, params map[string]string) string {
	var query []string
	for k, v := range params {
		query = append(query, fmt.Sprintf("%s=%s", k, v))
	}
	return apiUrl + strings.Join(query, "&")
}

// Make request to kubecost api
func kubecostApiRequest(clientset *kubernetes.Clientset, restConfig *rest.Config, kubecostApiNamespace string, kubecostApiService string, kubecostApiPort string, apiUrl string) (string, error) {
	fc, err := services.PortForward(context.TODO(), clientset, restConfig, kubecostApiNamespace, kubecostApiService, kubecostApiPort)
	if err != nil {
		log.Printf("Error portforwarding: %s", err.Error())
		return "", err
	}
	srj, err := services.GetServiceOutput(fc, apiUrl)
	if err != nil {
		log.Printf("Error getting service output: %s", err.Error())
		return "", err
	}
	return srj, nil
}
