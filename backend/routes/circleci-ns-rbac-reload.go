package routes

import (
	"io"
	"log"
	"net/http"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/services"
)

// CircleCiNamespaceRbacCreation<PERSON><PERSON><PERSON> is the handler for the /circleci-ns-rbac-reload route
// Swagger documentation block:
// @Summary Reload namespace RBAC from CircleCI project
// @Description Initiates RBAC regeneration for linked (Github to Google) users and creates CircleCI Service Account for provided namespace.
// @Description Service Account key is injected into CircleCI project environment variables.
// @Description After successful completion, only CircleCI service account and users with push access to the repository will have access to the namespace.
// @Description This endpoint also performs key rotation when it is older than 30 days.
// @Tags rbac
// @Accept x-www-form-urlencoded
// @Produce json
// @Param rbac_reload_key formData string true "Secret key for reloading RBAC"
// @Param namespace formData string true "Namespace name"
// @Param cluster formData string true "Cluster name"
// @Success 200 {string} string "Success"
// @Failure 400 {string} string "Missing or incorrect parameters"
// @Router /api/circleci/reload-ns-rbac [post]
func (rh *RouteHandler) CircleCiNamespaceRbacCreationHandler(w http.ResponseWriter, r *http.Request) {

	// Get params from POST request
	err := r.ParseForm()
	if err != nil {
		log.Printf("Error parsing request body: %+v", err)
		w.WriteHeader(http.StatusBadRequest)
		io.WriteString(w, "Missing parameters")
		return
	}

	secretKey := r.FormValue("rbac_reload_key")
	namespaceName := r.FormValue("namespace")
	clusterKey := r.FormValue("cluster")

	// If any variable is empty or incorrect, return error
	if namespaceName == "" || clusterKey == "" || secretKey == "" || secretKey != conf.RbacReloadKey || conf.RbacReloadKey == "" || conf.CircleCiToken == "" {

		// Log rbac_reload_key check
		if secretKey != conf.RbacReloadKey {
			log.Println("Unsuccessful namespace rbac reload: incorrect rbac_reload_key parameter")
		}

		log.Println("Unsuccessful namespace rbac reload: missing parameters")
		w.WriteHeader(http.StatusBadRequest)
		io.WriteString(w, "Missing or incorrect parameters")
		return
	}

	// Log access
	log.Printf("[AUDIT] CircleCI '%s/%s' namespace rbac reload requested", clusterKey, namespaceName)

	// Load project from Github
	collaborators, err := services.GetAllGitRepositoryCollaborators(conf.GithubOrg, namespaceName, "internal")
	if err != nil {
		log.Println("Error requesting github collaborators:", err)
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	// Create empty collaborator list of type projectCollaborator and assign to collaborators variable
	pushers := make([]services.ProjectCollaborator, 0)

	// Select collaborators with push access
	for _, collaborator := range collaborators {
		if collaborator.Permissions["push"] {

			// Add collaborator to projectList
			pushers = append(pushers, services.ProjectCollaborator{
				Login: collaborator.Login,
				ID:    *collaborator.ID,
			})
		}
	}

	// Create empty namespace for rbac resources (if ns does not exist)
	err = services.KubernetesCreateNamespace(clusterKey, namespaceName)
	if err != nil {
		log.Println("Error while creating namespace:", err)
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	// Add project to projectList
	project := services.Project{
		Name:          namespaceName,
		Collaborators: pushers,
	}

	// Load user mappings from database
	userMappings, err := services.GetGhToGmailMappings()
	if err != nil {
		log.Println("Error while reading Github to Google mappings:", err)
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	// Create namespace rbac
	err = services.CreateNamespaceRBAC(clusterKey, namespaceName, project, userMappings)
	if err != nil {
		log.Println("Error while creating namespace RBAC:", err)
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	// Return success
	w.WriteHeader(http.StatusOK)
	io.WriteString(w, "Success")
}
