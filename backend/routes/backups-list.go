package routes

import (
	"encoding/json"
	"log"
	"net/http"

	"github.com/wunderio/silta-dashboard/backend/services"

	"github.com/alexedwards/flow"
)

// Resource allocation handler
// Swagger documentation block:
// @Summary Release backup listing
// @Description Release backup listing
// @Tags release
// @Accept json
// @Produce json
// @Param clustername  path  string true "Cluster name"
// @Param namespacename path  string true "Namespace name"
// @Param releasename  path  string true "Release name"
// @Success 200 {string} string "ok"
// @Failure 401 {string} string "Unauthorized"
// @Router /api/release/{clustername}/{namespacename}/{releasename}/backups [get]
func (rh *RouteHandler) BackupListingHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: backups list")
		rh.StatusUnauthorized(w, r)
		return
	}

	clusterKey := flow.Param(r.Context(), "clustername")
	namespaceName := flow.Param(r.Context(), "namespacename")
	releaseName := flow.Param(r.Context(), "releasename")

	// Check if user has access to namespace
	namespaceAccess := services.ValidateNamespaceAccess(sessionToken, namespaceName)
	if !namespaceAccess {
		log.Println("Unauthorized access attempt: backups list (no access to project)")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	log.Printf("[AUDIT] User '%s' requested backup list for '%s' deployment in '%s' namespace", user.Name, releaseName, namespaceName)

	// Get helm release values
	releaseInfo, err := services.GetReleaseInfo(clusterKey, namespaceName, releaseName)
	if err != nil {
		log.Printf("Error getting release info: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// if chart name is neither drupal nor frontend, return empty json
	if releaseInfo.Chart.Name() != "drupal" && releaseInfo.Chart.Name() != "frontend" {
		w.Write([]byte("[]"))
		return
	}

	// Get backups
	backups, err := services.GetBackupsList(clusterKey, namespaceName, releaseName)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// Get backups schedule from releaseInfo.values.backup.schedule
	backupsSchedule := ""
	if releaseInfo.Values != nil {
		if releaseInfo.Values["backup"] != nil {
			if releaseInfo.Values["backup"].(map[string]interface{})["schedule"] != nil {
				backupsSchedule = releaseInfo.Values["backup"].(map[string]interface{})["schedule"].(string)
			}
		}
	}

	// Create json response
	json, err := json.Marshal(map[string]interface{}{
		"backups":  backups,
		"schedule": backupsSchedule,
	})
	if err != nil {
		log.Printf("Error creating json response: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	w.Write(json)
}
