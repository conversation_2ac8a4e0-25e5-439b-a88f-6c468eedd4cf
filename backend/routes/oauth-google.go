package routes

import (
	"context"
	"log"
	"net/http"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/db"
	"github.com/wunderio/silta-dashboard/backend/schemas"
	"github.com/wunderio/silta-dashboard/backend/services"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/dghubble/gologin/v2/google"
)

// GoogleHubCallback provides a http.Handler that handles Google login callbacks.
// Swagger documentation block:
// @Summary Handle Google login callback
// @Description Handle Google login callback
// @Tags auth
// @Router /auth/google/callback [get]
// @Router /auth/google/callback [post]
func (rh *RouteHandler) GoogleCallbackHandler() http.Handler {
	fn := func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		googleUser, err := google.UserFromContext(ctx)
		if err != nil {
			log.Printf("[GoogleCallbackHandler] Error: %s\n", err)
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		log.Printf("[GoogleCallbackHandler] Google User '%s' retrieved\n", googleUser.Email)

		// TODO:MAYBE Filter user by domain/org/Hd?, allow selected email addresses.
		//            Setting Google OAuth2 app to "internal" takes care of this.

		// User data: google.User{
		// 	Email:<EMAIL>
		// 	FamilyName:Surname
		// 	Gender:
		// 	GivenName:Name
		// 	Hd:example.com
		// 	Id:123456789012345678901
		// 	Link:https://plus.google.com/123456789012345678901
		// 	Locale:en
		// 	Name:Name Surname
		// 	Picture:https://lh3.googleusercontent.com/a/ABCDEFG1234=s96-c
		// 	VerifiedEmail:0xc000399670
		// 	ServerResponse:{
		// 		HTTPStatusCode:200
		// 		Header:map[Alt-Svc:[h3=":443"; ma=2592000,h3-29=":443"; ma=2592000]
		// 		Cache-Control:[no-cache, no-store, max-age=0, must-revalidate]
		// 		Content-Type:[application/json; charset=UTF-8]
		// 		Date:[Wed, 15 Feb 2023 12:19:32 GMT]
		// 		Expires:[Mon, 01 Jan 1990 00:00:00 GMT]
		// 		Pragma:[no-cache]
		// 		Server:[ESF]
		// 		Vary:[Origin X-Origin Referer]
		// 		X-Content-Type-Options:[nosniff]
		// 		X-Frame-Options:[SAMEORIGIN]
		// 		X-Xss-Protection:[0]]
		// 	}
		// 	ForceSendFields:[]
		// 	NullFields:[]
		// }

		// Validate user
		sessionToken := rh.Session.GetString(r.Context(), "session_token")
		validSession := services.ValidateSession(sessionToken)

		// User is not correctly authenticated, return 401
		if !validSession {
			rh.StatusUnauthorized(w, r)
			return
		}

		// User is authenticated, return user data
		getUser, err := services.GetUserBySessionToken(sessionToken)
		if err != nil {
			log.Printf("[GoogleCallbackHandler] User not found: %s\n", err)
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// if user is external, return 401
		if getUser.Github.OrgScope != "internal" {
			log.Printf("[GoogleCallbackHandler] User is external, returning 401\n")
			rh.StatusUnauthorized(w, r)
			return
		}

		log.Println("[GoogleCallbackHandler] User found, updating user")

		// Update user object
		getUser.Google = schemas.Google{
			Name:       googleUser.Name,
			Email:      googleUser.Email,
			GoogleId:   googleUser.Id,
			PictureUrl: googleUser.Picture,
		}

		// Update user
		filter := bson.M{"uuid": getUser.Uuid}
		_, err = db.Users.UpdateOne(context.TODO(), filter, bson.M{"$set": getUser})
		if err != nil {
			log.Printf("[GoogleCallbackHandler] User update error: %s\n", err)
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// Redirect to profile page
		http.Redirect(w, r, conf.FrontendURL+conf.FrontendRoutePrefix+"/profile", http.StatusFound)
	}
	return http.HandlerFunc(fn)
}

// HandleLoginStatus is the handler for the /auth/google/unlink route
//
//	@Summary		Unlink Google account
//	@Description	Unlink Google account
//	@Tags			auth
//	@Accept			json
//	@Produce		json
//	@Success		302	{object}	apiAuthResponseStruct
//	@Router			/auth/google/unlink [get]
//	@Router			/auth/google/unlink [post]
func (rh *RouteHandler) GoogleUnlinkHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		rh.StatusUnauthorized(w, r)
		return
	}

	// User is authenticated, return user data
	getUser, err := services.GetUserBySessionToken(sessionToken)
	if err != nil {
		log.Printf("[LoginStatusHandler] User not found: %s\n", err)
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// User found, remove google data from user object
	getUser.Google = schemas.Google{}

	// Update user
	filter := bson.M{"uuid": getUser.Uuid}
	_, err = db.Users.UpdateOne(context.TODO(), filter, bson.M{"$set": getUser})
	if err != nil {
		log.Printf("[GoogleCallbackHandler] User update error: %s\n", err)
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Redirect to profile page
	http.Redirect(w, r, conf.FrontendURL+conf.FrontendRoutePrefix+"/profile", http.StatusFound)
}
