package routes

import (
	"encoding/json"
	"log"
	"net/http"

	"github.com/wunderio/silta-dashboard/backend/services"
)

// Namespace<PERSON>ist<PERSON><PERSON><PERSON> is the handler for the /namespaces route
// Swagger documentation block:
// @Summary List user namespaces
// @Description List user namespaces
// @Tags namespaces
// @Accept json
// @Produce json
// @Success 200 {array} string
// @Failure 401 {string} string "Unauthorized"
// @Router /api/namespaces [get]
// @Router /api/namespaces [post]
func (rh *RouteHandler) NamespaceListHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: namespace list")
		rh.StatusUnauthorized(w, r)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	log.Printf("[AUDIT] User '%s' listed namespaces", user.Name)

	namespaces := services.GetUserNamespaces(sessionToken)

	json.NewEncoder(w).Encode(namespaces)
}
