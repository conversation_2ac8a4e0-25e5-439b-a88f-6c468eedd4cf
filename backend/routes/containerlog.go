package routes

import (
	"io"
	"log"
	"net/http"

	"github.com/wunderio/silta-dashboard/backend/services"

	"github.com/alexedwards/flow"
)

// ClusterListHandler allows retrieval of container logs
// Swagger documentation block:
// @Summary Retrieve container logs
// @Description Retrieve container logs
// @Tags logs
// @Accept json
// @Produce json
// @Param clustername  path  string true "Cluster name"
// @Param namespacename path  string true "Namespace name"
// @Param podname path string true "Pod name"
// @Param containername path  string true "Container name"
// @Success 200 {string} string "ok"
// @Failure 401 {string} string "Unauthorized"
// @Router /api/logs/{clustername}/{namespacename}/{podname}/{containername} [get]
// @Router /api/logs/{clustername}/{namespacename}/{podname}/{containername} [post]
func (rh *RouteHandler) ContainerLogHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: container log")
		rh.StatusUnauthorized(w, r)
		return
	}

	clusterKey := flow.Param(r.Context(), "clustername")
	namespaceName := flow.Param(r.Context(), "namespacename")
	podName := flow.Param(r.Context(), "podname")
	containerName := flow.Param(r.Context(), "containername")

	// Check if user has access to namespace
	namespaceAccess := services.ValidateNamespaceAccess(sessionToken, namespaceName)
	if !namespaceAccess {
		log.Println("Unauthorized access attempt: release list (no access to project)")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	log.Printf("[AUDIT] User '%s' accessed container '%s/%s' log for namespace '%s'", user.Name, podName, containerName, namespaceName)

	logs := services.GetContainerLog(clusterKey, namespaceName, podName, containerName)

	// If no logs, return "no logs" string
	if logs == "" {
		logs = "no logs"
	}

	// return logs to frontend
	w.WriteHeader(http.StatusOK)
	io.WriteString(w, logs)
}
