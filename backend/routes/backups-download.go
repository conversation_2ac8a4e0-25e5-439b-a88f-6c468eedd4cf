package routes

import (
	"log"
	"net/http"

	"github.com/wunderio/silta-dashboard/backend/services"

	"github.com/alexedwards/flow"
)

// Resource allocation handler
// Swagger documentation block:
// @Summary Backups item download
// @Description Backups item download
// @Tags files
// @Accept json
// @Produce json
// @Param clustername  path  string true "Cluster name"
// @Param namespacename path  string true "Namespace name"
// @Param releasename  path  string true "Release name"
// @Param filename     path  string true "Filename"
// @Success 200 {string} string "ok"
// @Failure 401 {string} string "Unauthorized"
// @Router /api/backups/{clustername}/{namespacename}/{releasename}/{filename} [get]
func (rh *RouteHandler) BackupDownloadHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: backups download")
		rh.StatusUnauthorized(w, r)
		return
	}

	clusterKey := flow.Param(r.Context(), "clustername")
	namespaceName := flow.Param(r.Context(), "namespacename")
	releaseName := flow.Param(r.Context(), "releasename")

	filename := flow.Param(r.Context(), "...")

	// Check if user has access to namespace
	namespaceAccess := services.ValidateNamespaceAccess(sessionToken, namespaceName)
	if !namespaceAccess {
		log.Println("Unauthorized access attempt: backups download (no access to project)")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	log.Printf("[AUDIT] User '%s' requested backup download (%s) for '%s' deployment in '%s' namespace", user.Name, filename, releaseName, namespaceName)

	// Get helm release values
	releaseInfo, err := services.GetReleaseInfo(clusterKey, namespaceName, releaseName)
	if err != nil {
		log.Printf("Error getting release info: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// if chart name is neither drupal nor frontend, return empty json
	if releaseInfo.Chart.Name() != "drupal" && releaseInfo.Chart.Name() != "frontend" {
		w.Write([]byte("[]"))
		return
	}

	// Download backup itemr
	err = services.StartBackupItemDownload(clusterKey, namespaceName, releaseName, filename, w, r)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
}
