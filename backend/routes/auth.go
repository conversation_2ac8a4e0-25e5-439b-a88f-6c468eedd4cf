package routes

import (
	"encoding/json"
	"net/http"

	// Chainable login http.Hand<PERSON>'s for popular oauth providers.
	"github.com/dghubble/gologin/v2"
	"github.com/dghubble/gologin/v2/github"
	"github.com/dghubble/gologin/v2/google"
	"github.com/wunderio/silta-dashboard/backend/services"
)

// GithubLoginHandler is wrapper for the /auth/github handler
// Swagger documentation block:
// @Summary Login with Github
// @Description Login with Github
// @Tags auth
// @Router /auth/github [get]
// @Router /auth/github [post]
func (rh *RouteHandler) GithubLoginHandler(config gologin.CookieConfig, success http.Handler) http.Handler {

	return github.StateHandler(config, success)
}

// GoogleLoginHandler is wrapper for the /auth/google handler
// Swagger documentation block:
// @Summary Link Google account
// @Description Link Google account
// @Tags auth
// @Router /auth/google [get]
// @Router /auth/google [post]
func (rh *RouteHandler) GoogleLoginHandler(config gologin.CookieConfig, success http.Handler) http.Handler {

	return google.StateHandler(config, success)
}

// LoginMethodsHandler returns a list of enabled login methods
// Swagger documentation block:
// @Summary Get enabled login methods
// @Description Get enabled login methods
// @Tags auth
// @Produce  json
// @Success 200 {array} string
// @Router /auth/login/methods [get]
func (rh *RouteHandler) LoginMethodsHandler(w http.ResponseWriter, r *http.Request) {
	methods := []string{}
	if rh.Conf.PasswordLoginEnabled {
		methods = append(methods, "password")
	}
	if rh.Conf.GithubLoginEnabled {
		methods = append(methods, "github")
	}
	json.NewEncoder(w).Encode(methods)
}

// PasswordLoginHandler handles password login requests
// Swagger documentation block:
// @Summary Login with password
// @Description Login with password
// @Tags auth
// @Accept  json
// @Produce  json
// @Param password body string true "Password"
// @Success 200 {object} object
// @Failure 401 {object} object
// @Router /auth/password [post]
func (rh *RouteHandler) PasswordLoginHandler(w http.ResponseWriter, r *http.Request) {
	type PasswordLoginRequest struct {
		Password string `json:"password"`
	}
	var req PasswordLoginRequest
	err := json.NewDecoder(r.Body).Decode(&req)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	if rh.Conf.PasswordLoginEnabled && req.Password == rh.Conf.PasswordLoginPassword {
		// Create session token
		sessionToken, err := services.CreatePasswordUserSession()
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		// Add user to the session
		rh.Session.Put(r.Context(), "session_token", sessionToken)
		w.WriteHeader(http.StatusOK)
	} else {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(map[string]string{"message": "Incorrect password"})
	}
}
