package routes

import (
	"context"
	"encoding/json"
	"log"
	"net/http"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/db"
	"github.com/wunderio/silta-dashboard/backend/services"
	"go.mongodb.org/mongo-driver/bson"
)

type ComponentRelease struct {
	Cluster   string `json:"cluster"`
	Namespace string `json:"namespace"`
	Release   string `json:"release"`
}

type ComponentNamespace struct {
	Namespace string `json:"namespace"`
	Cluster   string `json:"cluster"`
}

type ComponentInventoryResponse struct {
	Name          string               `json:"name"`
	ParsedVersion string               `json:"parsedVersion"`
	Source        string               `json:"source"`
	Images        []string             `json:"images"`
	Namespaces    []ComponentNamespace `json:"namespaces"`
	Releases      []ComponentRelease   `json:"releases"`
}

// InventoryComponentsHandler returns aggregated component inventory
// Swagger documentation block:
// @Summary List components inventory
// @Description Get aggregated list of components from all releases
// @Tags inventory
// @Accept json
// @Produce json
// @Success 200 {array} ComponentInventoryResponse
// @Failure 401 {string} string "Unauthorized"
// @Router /api/inventory/components [get]
func (rh *RouteHandler) InventoryComponentsHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: inventory components")
		rh.StatusUnauthorized(w, r)
		return
	}

	// Check if user has admin role
	user, err := services.GetUserBySessionToken(sessionToken)
	if err != nil {
		log.Printf("Error getting user: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// Check if user is admin
	isAdmin := false
	if user.Github.OrgAdmin {
		isAdmin = true
	}

	// Also check for local-user (password login)
	if conf.PasswordLoginEnabled && user.Name == "local-user" {
		isAdmin = true
	}

	if !isAdmin {
		log.Printf("User %s does not have admin role", user.Name)
		w.WriteHeader(http.StatusForbidden)
		return
	}

	// Log access
	log.Printf("[AUDIT] User '%s' requested inventory components list", user.Name)

	// Get query parameters for filtering
	serviceName := r.URL.Query().Get("serviceName")
	namespaceName := r.URL.Query().Get("namespaceName")
	releaseName := r.URL.Query().Get("releaseName")

	// Build match stage for filtering
	matchStage := bson.M{}
	if namespaceName != "" {
		matchStage["namespace"] = bson.M{"$regex": namespaceName, "$options": "i"}
	}
	if releaseName != "" {
		matchStage["name"] = bson.M{"$regex": releaseName, "$options": "i"}
	}

	// Build aggregation pipeline
	pipeline := []bson.M{
		// First match stage for release-level filters
		{"$match": matchStage},
		// Unwind components array
		{"$unwind": "$components"},
		// Match only enabled components
		{
			"$match": bson.M{
				"components.enabled": true,
			},
		},
	}

	// Add service name filter if provided
	if serviceName != "" {
		pipeline = append(pipeline, bson.M{
			"$match": bson.M{
				"components.name": bson.M{"$regex": serviceName, "$options": "i"},
			},
		})
	}

	// Add a field to create unique release identifier
	pipeline = append(pipeline, bson.M{
		"$addFields": bson.M{
			"releaseKey": bson.M{
				"$concat": []interface{}{"$cluster", "/", "$namespace", "/", "$name"},
			},
		},
	})

	// Group by component name, version, and source
	pipeline = append(pipeline, bson.M{
		"$group": bson.M{
			"_id": bson.M{
				"name":          "$components.name",
				"parsedVersion": "$components.parsedVersion",
				"source":        "$components.source",
			},
			"images": bson.M{"$addToSet": "$components.image"},
			"namespaces": bson.M{
				"$addToSet": bson.M{
					"namespace": "$namespace",
					"cluster":   "$cluster",
				},
			},
			"releases": bson.M{
				"$addToSet": bson.M{
					"cluster":   "$cluster",
					"namespace": "$namespace",
					"release":   "$name",
					"key":       "$releaseKey",
				},
			},
		},
	})

	// Project to flatten the structure
	pipeline = append(pipeline, bson.M{
		"$project": bson.M{
			"_id":           0,
			"name":          "$_id.name",
			"parsedVersion": "$_id.parsedVersion",
			"source":        "$_id.source",
			"images":        1,
			"namespaces":    1,
			"releases":      1,
		},
	})

	// Sort by name by default
	pipeline = append(pipeline, bson.M{
		"$sort": bson.M{"name": 1},
	})

	// Execute aggregation
	cursor, err := db.InventoryReleases.Aggregate(context.TODO(), pipeline)
	if err != nil {
		log.Printf("Error aggregating components from MongoDB: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	defer cursor.Close(context.TODO())

	// Parse results
	var components []ComponentInventoryResponse
	for cursor.Next(context.TODO()) {
		var result bson.M
		if err := cursor.Decode(&result); err != nil {
			log.Printf("Error decoding component: %s", err.Error())
			continue
		}

		component := ComponentInventoryResponse{
			Name:          getString(result, "name"),
			ParsedVersion: getString(result, "parsedVersion"),
			Source:        getString(result, "source"),
			Images:        getStringArray(result, "images"),
			Namespaces:    getNamespaceArray(result, "namespaces"),
			Releases:      getReleaseArray(result, "releases"),
		}

		components = append(components, component)
	}

	// Return components list
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(components)
}

// Helper function to safely extract string array values from bson.M
func getStringArray(m bson.M, key string) []string {
	if val, ok := m[key]; ok {
		if arr, ok := val.(bson.A); ok {
			result := make([]string, 0, len(arr))
			for _, item := range arr {
				if str, ok := item.(string); ok {
					result = append(result, str)
				}
			}
			return result
		}
	}
	return []string{}
}

// Helper function to safely extract namespace array values from bson.M
func getNamespaceArray(m bson.M, key string) []ComponentNamespace {
	if val, ok := m[key]; ok {
		if arr, ok := val.(bson.A); ok {
			result := make([]ComponentNamespace, 0, len(arr))
			for _, item := range arr {
				if nsMap, ok := item.(bson.M); ok {
					ns := ComponentNamespace{
						Namespace: getString(nsMap, "namespace"),
						Cluster:   getString(nsMap, "cluster"),
					}
					result = append(result, ns)
				}
			}
			return result
		}
	}
	return []ComponentNamespace{}
}

// Helper function to safely extract release array values from bson.M
func getReleaseArray(m bson.M, key string) []ComponentRelease {
	if val, ok := m[key]; ok {
		if arr, ok := val.(bson.A); ok {
			result := make([]ComponentRelease, 0, len(arr))
			for _, item := range arr {
				if releaseMap, ok := item.(bson.M); ok {
					release := ComponentRelease{
						Cluster:   getString(releaseMap, "cluster"),
						Namespace: getString(releaseMap, "namespace"),
						Release:   getString(releaseMap, "release"),
					}
					result = append(result, release)
				}
			}
			return result
		}
	}
	return []ComponentRelease{}
}
