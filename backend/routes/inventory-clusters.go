package routes

import (
	"context"
	"encoding/json"
	"log"
	"net/http"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/db"
	"github.com/wunderio/silta-dashboard/backend/services"
	"go.mongodb.org/mongo-driver/bson"
)

// ClusterInventoryResponse represents the cluster inventory data returned to the frontend
type ClusterInventoryResponse struct {
	Name                 string `json:"name"`
	Enabled              bool   `json:"enabled"`
	Server               string `json:"server"`
	KubernetesVersion    string `json:"kubernetesVersion"`
	SiltaClusterVersion  string `json:"siltaClusterVersion"`
	SiltaClusterDeployed string `json:"siltaClusterDeployed"`
	Updated              string `json:"updated"`
	LabelColor           string `json:"labelColor"`
}

// InventoryClustersHandler returns a list of clusters with inventory information
// Swagger documentation block:
// @Summary List clusters inventory
// @Description Get list of clusters with inventory information
// @Tags inventory
// @Accept json
// @Produce json
// @Success 200 {array} ClusterInventoryResponse
// @Failure 401 {string} string "Unauthorized"
// @Router /api/inventory/clusters [get]
func (rh *RouteHandler) InventoryClustersHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: inventory clusters")
		rh.StatusUnauthorized(w, r)
		return
	}

	// Check if user has admin role
	user, err := services.GetUserBySessionToken(sessionToken)
	if err != nil {
		log.Printf("Error getting user: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// Check if user is admin
	isAdmin := false
	if user.Github.OrgAdmin {
		isAdmin = true
	}

	// Also check for local-user (password login)
	if conf.PasswordLoginEnabled && user.Name == "local-user" {
		isAdmin = true
	}

	if !isAdmin {
		log.Printf("User %s does not have admin role", user.Name)
		w.WriteHeader(http.StatusForbidden)
		return
	}

	// Log access
	log.Printf("[AUDIT] User '%s' requested inventory clusters list", user.Name)

	// Fetch inventory data from MongoDB
	inventoryMap := make(map[string]bson.M)
	cursor, err := db.Clusters.Find(context.TODO(), bson.M{})
	if err != nil {
		log.Printf("Error fetching clusters from MongoDB: %s", err.Error())
	} else {
		defer cursor.Close(context.TODO())
		for cursor.Next(context.TODO()) {
			var result bson.M
			if err := cursor.Decode(&result); err != nil {
				log.Printf("Error decoding cluster: %s", err.Error())
				continue
			}
			if name := getString(result, "name"); name != "" {
				inventoryMap[name] = result
			}
		}
	}

	// Fetch silta-cluster release information for each cluster
	siltaClusterMap := make(map[string]bson.M)
	releaseCursor, err := db.InventoryReleases.Find(context.TODO(), bson.M{
		"namespace": "silta-cluster",
		"name":      "silta-cluster",
	})
	if err != nil {
		log.Printf("Error fetching silta-cluster releases from MongoDB: %s", err.Error())
	} else {
		defer releaseCursor.Close(context.TODO())
		for releaseCursor.Next(context.TODO()) {
			var result bson.M
			if err := releaseCursor.Decode(&result); err != nil {
				log.Printf("Error decoding silta-cluster release: %s", err.Error())
				continue
			}
			if cluster := getString(result, "cluster"); cluster != "" {
				siltaClusterMap[cluster] = result
			}
		}
	}

	// Merge with actual cluster configuration from conf
	var clusters []ClusterInventoryResponse
	for clusterName, clusterConf := range conf.KubeClusters {
		cluster := ClusterInventoryResponse{
			Name:       clusterName,
			Enabled:    clusterConf.Enabled,
			Server:     clusterConf.Cluster.Server,
			LabelColor: clusterConf.LabelColor,
		}

		// Merge with inventory data if available
		if inventoryData, exists := inventoryMap[clusterName]; exists {
			// Extract inventory data
			if inventory, ok := inventoryData["inventory"].(bson.M); ok {
				cluster.KubernetesVersion = getString(inventory, "kubernetesVersion")
				cluster.Updated = getString(inventory, "updated")
			}
		}

		// Merge with silta-cluster release data if available
		if siltaClusterData, exists := siltaClusterMap[clusterName]; exists {
			cluster.SiltaClusterVersion = getString(siltaClusterData, "chartVersion")
			cluster.SiltaClusterDeployed = getString(siltaClusterData, "deployed")
		}

		clusters = append(clusters, cluster)
	}

	// Return clusters list
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(clusters)
}

// Helper functions to safely extract values from bson.M
func getString(m bson.M, key string) string {
	if val, ok := m[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

func getBool(m bson.M, key string) bool {
	if val, ok := m[key]; ok {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return false
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
