package routes

import (
	"encoding/json"
	"log"
	"net/http"

	"github.com/wunderio/silta-dashboard/backend/services"

	"github.com/alexedwards/flow"
)

// Resource allocation handler
// Swagger documentation block:
// @Summary Release backup start
// @Description Release backup start
// @Tags release
// @Accept json
// @Produce json
// @Param clustername  path  string true "Cluster name"
// @Param namespacename path  string true "Namespace name"
// @Param releasename  path  string true "Release name"
// @Success 200 {string} string "ok"
// @Failure 401 {string} string "Unauthorized"
// @Router /api/release/{clustername}/{namespacename}/{releasename}/backups/start [post]
func (rh *RouteHandler) BackupStartHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: backups start")
		rh.StatusUnauthorized(w, r)
		return
	}

	clusterKey := flow.Param(r.Context(), "clustername")
	namespaceName := flow.Param(r.Context(), "namespacename")
	releaseName := flow.Param(r.Context(), "releasename")

	// Check if user has access to namespace
	namespaceAccess := services.ValidateNamespaceAccess(sessionToken, namespaceName)
	if !namespaceAccess {
		log.Println("Unauthorized access attempt: backups start (no access to project)")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	log.Printf("[AUDIT] User '%s' requested backup start for '%s' deployment in '%s' namespace", user.Name, releaseName, namespaceName)

	// Get helm release values
	releaseInfo, err := services.GetReleaseInfo(clusterKey, namespaceName, releaseName)
	if err != nil {
		log.Printf("Error getting release info: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// if chart name is neither drupal nor frontend, return empty json
	if releaseInfo.Chart.Name() != "drupal" && releaseInfo.Chart.Name() != "frontend" {
		w.WriteHeader(http.StatusNotFound)
		return
	}

	jobName, err := services.StartBackup(clusterKey, namespaceName, releaseName)
	if err != nil {
		log.Printf("Error starting backup: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// return job name
	resp := map[string]string{
		"status": "ok",
		"job":    jobName,
	}

	jsonResp, err := json.Marshal(resp)
	if err != nil {
		log.Printf("Error marshalling json: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	w.Write(jsonResp)
}
