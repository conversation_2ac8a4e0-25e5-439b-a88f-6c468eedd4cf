package routes

import (
	"encoding/json"
	"log"
	"net/http"

	"github.com/wunderio/silta-dashboard/backend/services"

	"github.com/alexedwards/flow"
)

// PodsListHandler returns a list of pods related to a release
// Swagger documentation block:
// @Summary List pods
// @Description List pods related to a release
// @Tags   release
// @Accept   json
// @Produce  json
// @Param   clustername  path  string true "Cluster name"
// @Param   namespacename path  string true "Namespace name"
// @Param   releasename  path  string true "Release name"
// @Success  200    {object} []apiPodsResponseStruct
// @Failure 401    {string} string "Unauthorized"
// @Router /api/release/{clustername}/{namespacename}/{releasename}/pods [get]
// @Router /api/release/{clustername}/{namespacename}/{releasename}/pods [post]
func (rh *RouteHandler) PodsListHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: pod list")
		rh.StatusUnauthorized(w, r)
		return
	}

	clusterKey := flow.Param(r.Context(), "clustername")
	namespaceName := flow.Param(r.Context(), "namespacename")
	releaseName := flow.Param(r.Context(), "releasename")

	// Check if user has access to namespace
	namespaceAccess := services.ValidateNamespaceAccess(sessionToken, namespaceName)
	if !namespaceAccess {
		log.Println("Unauthorized access attempt: release list (no access to project)")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	log.Printf("[AUDIT] User '%s' listed pods in namespace '%s'", user.Name, namespaceName)

	pods := services.GetReleasePods(clusterKey, namespaceName, releaseName)

	json.NewEncoder(w).Encode(pods)
}
