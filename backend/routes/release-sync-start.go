package routes

import (
	"encoding/json"
	"io"
	"log"
	"net/http"
	"strings"

	"golang.org/x/exp/slices"

	"github.com/alexedwards/flow"
	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/services"
)

func (rh *RouteHandler) ReleaseSyncStartHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: start release sync")
		rh.StatusUnauthorized(w, r)
		return
	}

	// Get params from POST request
	err := r.ParseForm()
	if err != nil {
		log.Printf("Error parsing request body: %+v", err)
		w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusBadRequest)
		io.WriteString(w, "Missing parameters")
		return
	}

	clusterKey := flow.Param(r.Context(), "clustername")
	namespaceName := flow.Param(r.Context(), "namespacename")
	releaseName := flow.Param(r.Context(), "releasename")
	sourceRelease := r.FormValue("sourceRelease")
	syncDatabase := r.FormValue("syncDatabase")
	syncFiles := r.FormValue("syncFiles")

	// Extract cluster, namespace and release name from sourceRelease variable																					"
	sourceCluster, sourceNamespace, sourceReleaseName := services.ParseReleaseName(sourceRelease)

	// If any variable is empty, return error
	if clusterKey == "" || namespaceName == "" || releaseName == "" || sourceCluster == "" || sourceNamespace == "" || sourceReleaseName == "" || syncDatabase == "" || syncFiles == "" {
		log.Println("Unsuccessful release sync start request: missing parameters")
		w.WriteHeader(http.StatusBadRequest)
		io.WriteString(w, "Missing parameters")
		return
	}

	// if namespace differs, return error (something fishy is going on)
	if namespaceName != sourceNamespace {
		log.Println("Unsuccessful release sync start request: namespace mismatch")
		w.WriteHeader(http.StatusBadRequest)
		io.WriteString(w, "Namespace mismatch")
		return
	}

	// Check if user has access to namespace
	namespaceAccess := services.ValidateNamespaceAccess(sessionToken, namespaceName)
	if !namespaceAccess {
		log.Println("Unauthorized access attempt: release sync start (no access to project)")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Check release name, disable sync to selected deployments
	denyReleases := []string{"production", "prod", "no-release"}
	protectedRelease := false

	// Check if namespace is restricted
	if slices.Contains(conf.RestrictedNamespaces, namespaceName) {
		protectedRelease = true
	}

	// Check if release name is restricted
	for _, denyReleaseName := range denyReleases {
		if denyReleaseName == releaseName {
			protectedRelease = true
			break
		}
		// if releaseName starts with "<denyReleaseName>-", deny sync
		if strings.HasPrefix(releaseName, denyReleaseName+"-") {
			protectedRelease = true
			break
		}
	}

	// If release is protected, return error
	if protectedRelease {
		log.Printf("[ReleaseSyncStartHandler] Unauthorized access attempt: release sync (protected release '%s')", releaseName)
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	// Log source release
	log.Printf("[AUDIT] User '%s' requested release sync start [Database=%s, Files=%s] from '%s/%s/%s' to '%s/%s/%s'", user.Name, syncDatabase, syncFiles, sourceCluster, sourceNamespace, sourceReleaseName, clusterKey, namespaceName, releaseName)

	// Check if release sync is already in progress
	releaseSync, err := services.GetReleaseSync(clusterKey, namespaceName, releaseName)
	if err != nil {
		log.Printf("Error getting release sync status: %+v", err)
	}

	// If release sync is in progress, return status
	if releaseSync.Status != "" && releaseSync.Status != "completed" && releaseSync.Status != "canceled" && releaseSync.Status != "failed" {
		log.Printf("Release sync already in progress: %+v", releaseSync.Status)
		w.WriteHeader(http.StatusOK)
		io.WriteString(w, releaseSync.Status)
		return
	}

	// Start release sync
	syncStatus, err := services.StartReleaseSync(sourceCluster, sourceNamespace, sourceReleaseName, clusterKey, namespaceName, releaseName, syncDatabase, syncFiles)

	if err != nil {
		log.Printf("Error starting release sync: %+v", err)
		w.WriteHeader(http.StatusInternalServerError)
		io.WriteString(w, "Error starting release sync")
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(syncStatus)
	}

	// Return status
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(syncStatus)
}
