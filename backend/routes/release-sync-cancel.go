package routes

import (
	"encoding/json"
	"io"
	"log"
	"net/http"

	"github.com/alexedwards/flow"
	"github.com/wunderio/silta-dashboard/backend/services"
)

func (rh *RouteHandler) ReleaseSyncCancelHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: cancel release sync")
		rh.StatusUnauthorized(w, r)
		return
	}

	// Get params from POST request
	err := r.ParseForm()
	if err != nil {
		log.Printf("Error parsing request body: %+v", err)
		w.WriteHeader(http.StatusBadRequest)
		io.WriteString(w, "Missing parameters")
		return
	}

	clusterKey := flow.Param(r.Context(), "clustername")
	namespaceName := flow.Param(r.Context(), "namespacename")
	releaseName := flow.Param(r.Context(), "releasename")

	// If any variable is empty, return error
	if clusterKey == "" || namespaceName == "" || releaseName == "" {
		log.Println("Unsuccessful release sync cancel request: missing parameters")
		w.WriteHeader(http.StatusBadRequest)
		io.WriteString(w, "Missing parameters")
		return
	}

	// Check if user has access to namespace
	namespaceAccess := services.ValidateNamespaceAccess(sessionToken, namespaceName)
	if !namespaceAccess {
		log.Println("Unauthorized access attempt: release sync cancel (no access to project)")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	log.Printf("[AUDIT] User '%s' requested release '%s' sync cancel in namespace '%s'", user.Name, releaseName, namespaceName)

	err = services.CancelReleaseSync(clusterKey, namespaceName, releaseName)
	if err != nil {
		log.Printf("Error cancelling release sync: %+v", err)
		w.WriteHeader(http.StatusInternalServerError)
		io.WriteString(w, "Error cancelling release sync")
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode("Release sync canceled")

}
