package routes

import (
	"context"
	"encoding/json"
	"log"
	"net/http"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/db"
	"github.com/wunderio/silta-dashboard/backend/services"
	"go.mongodb.org/mongo-driver/bson"
)

// ImageInventoryResponse represents the image inventory data returned to the frontend
type ImageInventoryResponse struct {
	Cluster           string `json:"cluster"`
	Namespace         string `json:"namespace"`
	ResourceName      string `json:"resourceName"`
	ParsedReleaseName string `json:"parsedReleaseName"`
	Url               string `json:"url"`
	Image             string `json:"image"`
	Tag               string `json:"tag"`
	Source            string `json:"source"`
	Updated           string `json:"updated"`
}

// PopularImageResponse represents aggregated image data with count
type PopularImageResponse struct {
	Url   string `json:"url"`
	Count int    `json:"count"`
}

// InventoryImagesHandler returns a list of images with inventory information
// Swagger documentation block:
// @Summary List images inventory
// @Description Get list of container images with inventory information
// @Tags inventory
// @Accept json
// @Produce json
// @Success 200 {array} ImageInventoryResponse
// @Failure 401 {string} string "Unauthorized"
// @Router /api/inventory/images [get]
func (rh *RouteHandler) InventoryImagesHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: inventory images")
		rh.StatusUnauthorized(w, r)
		return
	}

	// Check if user has admin role
	user, err := services.GetUserBySessionToken(sessionToken)
	if err != nil {
		log.Printf("Error getting user: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// Check if user is admin
	isAdmin := false
	if user.Github.OrgAdmin {
		isAdmin = true
	}

	// Also check for local-user (password login)
	if conf.PasswordLoginEnabled && user.Name == "local-user" {
		isAdmin = true
	}

	if !isAdmin {
		log.Printf("User %s does not have admin role", user.Name)
		w.WriteHeader(http.StatusForbidden)
		return
	}

	// Log access
	log.Printf("[AUDIT] User '%s' requested inventory - images list", user.Name)

	// Fetch images from MongoDB
	cursor, err := db.InventoryImages.Find(context.TODO(), bson.M{})
	if err != nil {
		log.Printf("Error fetching images from MongoDB: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	defer cursor.Close(context.TODO())

	// Parse results
	var images []ImageInventoryResponse
	for cursor.Next(context.TODO()) {
		var result bson.M
		if err := cursor.Decode(&result); err != nil {
			log.Printf("Error decoding image: %s", err.Error())
			continue
		}

		image := ImageInventoryResponse{
			Cluster:           getString(result, "cluster"),
			Namespace:         getString(result, "namespace"),
			ResourceName:      getString(result, "resourceName"),
			ParsedReleaseName: getString(result, "parsedReleaseName"),
			Url:               getString(result, "url"),
			Image:             getString(result, "image"),
			Tag:               getString(result, "tag"),
			Source:            getString(result, "source"),
			Updated:           getString(result, "updated"),
		}

		images = append(images, image)
	}

	// Return images list
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(images)
}

// InventoryImagesPopularHandler returns aggregated image counts
// Swagger documentation block:
// @Summary List popular images
// @Description Get list of most popular container images with counts
// @Tags inventory
// @Accept json
// @Produce json
// @Success 200 {array} PopularImageResponse
// @Failure 401 {string} string "Unauthorized"
// @Router /api/inventory/images/popular [get]
func (rh *RouteHandler) InventoryImagesPopularHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: inventory images popular")
		rh.StatusUnauthorized(w, r)
		return
	}

	// Check if user has admin role
	user, err := services.GetUserBySessionToken(sessionToken)
	if err != nil {
		log.Printf("Error getting user: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// Check if user is admin
	isAdmin := false
	if user.Github.OrgAdmin {
		isAdmin = true
	}

	// Also check for local-user (password login)
	if conf.PasswordLoginEnabled && user.Name == "local-user" {
		isAdmin = true
	}

	if !isAdmin {
		log.Printf("User %s does not have admin role", user.Name)
		w.WriteHeader(http.StatusForbidden)
		return
	}

	// Log access
	log.Printf("[AUDIT] User '%s' requested inventory - popular images list", user.Name)

	// Aggregate images by image name and count
	pipeline := []bson.M{
		{
			"$group": bson.M{
				"_id":   "$url",
				"count": bson.M{"$sum": 1},
			},
		},
		{
			"$sort": bson.M{"count": -1},
		},
	}

	cursor, err := db.InventoryImages.Aggregate(context.TODO(), pipeline)
	if err != nil {
		log.Printf("Error aggregating images from MongoDB: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	defer cursor.Close(context.TODO())

	// Parse results
	var popularImages []PopularImageResponse
	for cursor.Next(context.TODO()) {
		var result bson.M
		if err := cursor.Decode(&result); err != nil {
			log.Printf("Error decoding popular image: %s", err.Error())
			continue
		}

		url := PopularImageResponse{
			Url:   getString(result, "_id"),
			Count: getInt(result, "count"),
		}

		popularImages = append(popularImages, url)
	}

	// Return popular images list
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(popularImages)
}

// Helper function to safely extract int values from bson.M
func getInt(m bson.M, key string) int {
	if val, ok := m[key]; ok {
		if i, ok := val.(int); ok {
			return i
		}
		if i32, ok := val.(int32); ok {
			return int(i32)
		}
		if i64, ok := val.(int64); ok {
			return int(i64)
		}
	}
	return 0
}
