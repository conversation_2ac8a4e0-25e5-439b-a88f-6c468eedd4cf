package routes

import (
	"encoding/json"
	"log"
	"net/http"
	"time"

	"github.com/wunderio/silta-dashboard/backend/schemas"
	"github.com/wunderio/silta-dashboard/backend/services"

	"github.com/alexedwards/flow"
)

// Release<PERSON>ist<PERSON><PERSON><PERSON> is the handler for the /releases route
// Swagger documentation block:
// @Summary List releases in namespace
// @Description List releases in namespace
// @Tags release
// @Accept json
// @Produce json
// @Param namespacename path string true "Namespace name"
// @Success 200 {array} schemas.ReleaseResponseStruct
// @Failure 401 {string} string "Unauthorized"
// @Router /api/namespace/{namespacename}/releases [get]
// @Router /api/namespace/{namespacename}/releases [post]
func (rh *RouteHandler) ReleaseListHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: release list")
		rh.StatusUnauthorized(w, r)
		return
	}

	namespaceName := flow.Param(r.Context(), "namespacename")

	// Check if user has access to namespace
	namespaceAccess := services.ValidateNamespaceAccess(sessionToken, namespaceName)
	if !namespaceAccess {
		log.Println("Unauthorized access attempt: release list (no access to project)")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	log.Printf("[AUDIT] User '%s' listed releases in namespace '%s'", user.Name, namespaceName)

	releases := []schemas.ReleaseResponseStruct{}

	// Let's cache releases for couple minutes so we don't have to query them every time

	// Get user from session
	user, err := services.GetUserBySessionToken(sessionToken)
	if err != nil {
		log.Printf("[ReleaseListHandler] Error getting user from session: %s\n", err)
	} else {
		releases, err = services.GetUserNamespaceCache(user.Uuid, namespaceName)

		// There's an error or cache is expired
		if err != nil {

			// Get releases from cluster
			releases = services.GetNamespaceReleases("", namespaceName)

			// Store releases in cache, but only if there are any. We don't want to cache fetch failures
			if len(releases) > 0 {

				cacheExpires := time.Now().Add(2 * time.Minute).Format(time.RFC3339)
				namespaceCache := schemas.NamespaceCache{
					Name:        namespaceName,
					CacheExpire: cacheExpires,
					Releases:    releases,
				}

				err2 := services.SetUserNamespaceCache(user.Uuid, namespaceCache)
				if err2 != nil {
					log.Printf("[ReleaseListHandler] Cache entry creation error: %s\n", err2)
				}
			}
		}
	}

	json.NewEncoder(w).Encode(releases)
}
