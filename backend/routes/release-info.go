package routes

import (
	"encoding/json"
	"log"
	"net/http"

	"github.com/wunderio/silta-dashboard/backend/services"

	"github.com/alexedwards/flow"
)

// ReleaseInfoHandler returns release information
// Swagger documentation block:
// @Summary Release information
// @Description Get release information
// @Tags release
// @Accept json
// @Produce json
// @Param clustername path string true "Cluster name"
// @Param namespacename path string true "Namespace name"
// @Param releasename path string true "Release name"
// @Success 200 {array} schemas.ReleaseResponseStruct
// @Failure 401 {string} string "Unauthorized"
// @Router /api/release/{clustername}/{namespacename}/{releasename} [get]
// @Router /api/release/{clustername}/{namespacename}/{releasename} [post]
func (rh *RouteHandler) ReleaseInfoHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: release info")
		rh.StatusUnauthorized(w, r)
		return
	}

	clusterName := flow.Param(r.Context(), "clustername")
	namespaceName := flow.Param(r.Context(), "namespacename")
	releaseName := flow.Param(r.Context(), "releasename")

	// Check if user has access to namespace
	namespaceAccess := services.ValidateNamespaceAccess(sessionToken, namespaceName)
	if !namespaceAccess {
		log.Println("Unauthorized access attempt: release info (no access to project)")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	log.Printf("[AUDIT] User '%s' requested release info '%s/%s'", user.Name, namespaceName, releaseName)

	releaseInfo, err := services.GetReleaseInfo(clusterName, namespaceName, releaseName)
	if err != nil {
		log.Printf("Error getting release info: %s", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// Return release info
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	json.NewEncoder(w).Encode(releaseInfo)
}
