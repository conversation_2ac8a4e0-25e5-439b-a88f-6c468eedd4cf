package routes

import (
	"io"
	"log"
	"net/http"

	"github.com/wunderio/silta-dashboard/backend/services"

	"github.com/alexedwards/flow"
)

// EventLogHandler allows retrieval of pod events. Pod name is optional.
// Swagger documentation block:
// @Summary Retrieve pod events
// @Description Retrieve pod events
// @Tags logs
// @Accept json
// @Produce json
// @Param clustername  path  string true "Cluster name"
// @Param namespacename path  string true "Namespace name"
// @Param podname path  string true "Pod name"
// @Success 200 {string} string "ok"
// @Failure 401 {string} string "Unauthorized"
// @Router /api/events/{clustername}/{namespacename}/ [get]
// @Router /api/events/{clustername}/{namespacename}/ [post]
// @Router /api/events/{clustername}/{namespacename}/{podname} [get]
// @Router /api/events/{clustername}/{namespacename}/{podname} [post]
func (rh *RouteHandler) EventLogHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: event log")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	clusterKey := flow.Param(r.Context(), "clustername")
	namespaceName := flow.Param(r.Context(), "namespacename")
	podName := flow.Param(r.Context(), "podname")

	// Check if user has access to namespace
	namespaceAccess := services.ValidateNamespaceAccess(sessionToken, namespaceName)
	if !namespaceAccess {
		log.Println("Unauthorized access attempt: release list (no access to project)")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	log.Printf("[AUDIT] User '%s' accessed pod '%s' events for namespace '%s'", user.Name, podName, namespaceName)

	events := services.GetEventLog(clusterKey, namespaceName, podName)

	if events == "" {
		events = "no events"
	}

	// return events to frontend
	w.WriteHeader(http.StatusOK)
	io.WriteString(w, events)
}
