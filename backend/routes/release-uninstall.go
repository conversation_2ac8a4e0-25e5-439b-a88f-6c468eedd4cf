package routes

import (
	"io"
	"log"
	"net/http"
	"strings"

	"golang.org/x/exp/slices"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/services"
)

// ReleaseUninstallHandler removes a release from a namespace
// @Summary Uninstall release
// @Description Uninstall release from namespace
// @Tags release
// @Accept json
// @Produce json
// @Param cluster  formData string true "Cluster name"
// @Param namespace formData string true "Namespace name"
// @Param release  formData string true "Release name"
// @Success 200 {string} string "ok"
// @Failure 400 {string} string "Missing parameters"
// @Failure 401 {string} string "Unauthorized"
// @Router /api/release/uninstall [post]
func (rh *RouteHandler) ReleaseUninstallHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: uninstall release")
		rh.StatusUnauthorized(w, r)
		return
	}

	// Get params from POST request
	err := r.ParseForm()
	if err != nil {
		log.Printf("Error parsing request body: %+v", err)
		w.WriteHeader(http.StatusBadRequest)
		io.WriteString(w, "Missing parameters")
		return
	}

	clusterKey := r.FormValue("cluster")
	namespaceName := r.FormValue("namespace")
	releaseName := r.FormValue("release")

	// If any variable is empty, return error
	if clusterKey == "" || namespaceName == "" || releaseName == "" {
		log.Println("Unsuccessful release removal: missing parameters")
		w.WriteHeader(http.StatusBadRequest)
		io.WriteString(w, "Missing parameters")
		return
	}

	// Check if user has access to namespace
	namespaceAccess := services.ValidateNamespaceAccess(sessionToken, namespaceName)
	if !namespaceAccess {
		log.Println("Unauthorized access attempt: uninstall release (no access to project)")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Check release name, disable removal of production and master deployments
	// Also, match namespace to restricted namespace list, to prevent accidental removal of system deployments
	denyReleases := []string{"production", "prod", "master", "main", "staging", "stage"}
	protectedRelease := false

	// Check if namespace is restricted
	if slices.Contains(conf.RestrictedNamespaces, namespaceName) {
		protectedRelease = true
	}

	// Check if release name is restricted
	for _, denyReleaseName := range denyReleases {
		if denyReleaseName == releaseName {
			protectedRelease = true
			break
		}
		// if releaseName starts with "<denyReleaseName>-", deny removal
		if strings.HasPrefix(releaseName, denyReleaseName+"-") {
			protectedRelease = true
			break
		}
	}

	// If release is protected, return error
	if protectedRelease {
		log.Printf("[UninstallReleaseHandler] Unauthorized access attempt: release uninstall (protected release '%s')", releaseName)
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	log.Printf("[AUDIT] User '%s' requested release '%s' removal in namespace '%s'", user.Name, releaseName, namespaceName)

	// Uninstall release
	err = services.UninstallHelmRelease(clusterKey, namespaceName, releaseName)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		io.WriteString(w, err.Error())
		return
	}

	// Invalidate namespace cache
	services.DeleteUserNamespaceCache(user.Uuid, namespaceName)

	// return events to frontend
	w.WriteHeader(http.StatusOK)
	io.WriteString(w, "ok")
}
