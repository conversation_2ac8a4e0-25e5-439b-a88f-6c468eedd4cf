package routes

import (
	"io"
	"log"
	"net/http"

	"github.com/alexedwards/flow"
	"github.com/wunderio/silta-dashboard/backend/services"
)

// ReleaseNotesHandler returns helm release notes for a release
// Swagger documentation block:
// @Summary Get release notes
// @Description Get release notes
// @Tags release
// @Accept json
// @Produce json
// @Param clustername  path  string true "Cluster name"
// @Param namespacename path  string true "Namespace name"
// @Param releasename  path  string true "Release name"
// @Success 200 {string} string "ok"
// @Failure 401 {string} string "Unauthorized"
// @Router /api/release/{clustername}/{namespacename}/{releasename}/notes [get]
// @Router /api/release/{clustername}/{namespacename}/{releasename}/notes [post]
func (rh *RouteHandler) ReleaseNotesHandler(w http.ResponseWriter, r *http.Request) {

	// Validate user
	sessionToken := rh.Session.GetString(r.Context(), "session_token")
	validSession := services.ValidateSession(sessionToken)

	// User is not correctly authenticated, return 401
	if !validSession {
		log.Println("Unauthorized access attempt: release notes (no valid session)")
		rh.StatusUnauthorized(w, r)
		return
	}

	clusterKey := flow.Param(r.Context(), "clustername")
	namespaceName := flow.Param(r.Context(), "namespacename")
	releaseName := flow.Param(r.Context(), "releasename")

	// If any variable is empty, return error
	if clusterKey == "" || namespaceName == "" || releaseName == "" {
		log.Println("Unsuccessful release notes: missing parameters")
		w.WriteHeader(http.StatusBadRequest)
		io.WriteString(w, "Missing parameters")
		return
	}

	// Check if user has access to namespace
	namespaceAccess := services.ValidateNamespaceAccess(sessionToken, namespaceName)
	if !namespaceAccess {
		log.Println("Unauthorized access attempt: release notes (no access to project)")
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Log access
	user, _ := services.GetUserBySessionToken(sessionToken)
	log.Printf("[AUDIT] User '%s' retrieved release notes for '%s' in namespace '%s'", user.Name, releaseName, namespaceName)

	release, err := services.GetReleaseInfo(clusterKey, namespaceName, releaseName)

	if err != nil {
		log.Printf("Error getting release info: %s\n", err)
		w.Write([]byte(""))
	}

	w.Write([]byte(release.Info.Notes))
}
