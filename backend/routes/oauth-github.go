package routes

import (
	"context"
	"log"
	"net/http"
	"time"

	"github.com/wunderio/silta-dashboard/backend/conf"
	"github.com/wunderio/silta-dashboard/backend/db"
	"github.com/wunderio/silta-dashboard/backend/schemas"
	"github.com/wunderio/silta-dashboard/backend/services"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/dghubble/gologin/v2/github"
	oauth2Login "github.com/dghubble/gologin/v2/oauth2"
)

// gitHubCallback provides a http.Handler that handles GitHub login callbacks.
// Swagger documentation block:
// @Summary Handle GitHub login callback
// @Description Handle GitHub login callback
// @Tags auth
// @Router /auth/github/callback [get]
// @Router /auth/github/callback [post]
func (rh *RouteHandler) GitHubCallbackHandler() http.Handler {
	fn := func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		githubUser, err := github.UserFromContext(ctx)
		if err != nil {
			log.Printf("[GitHubCallbackHandler] Error: %s\n", err)
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		log.Printf("[GitHubCallbackHandler] Github User '%s' retrieved.\n", *githubUser.Login)

		// Connect to Github API, get list of contributors (internal and external), and check if user is in the list
		orgScope := services.GetGithubOrgCollaboratorScope(conf.GithubOrg, githubUser.ID)
		if orgScope == "" {
			log.Printf("[GitHubCallbackHandler] User '%s' is not a collaborator in any of the Github organizations.\n", *githubUser.Login)
			http.Error(w, "Member or collaborator access required! This request is logged.", http.StatusUnauthorized)
			return
		}

		// Connect to Github API, get list of organisation administrators, and check if user is in the list
		orgAdmins, err := services.GetGithubOrgMembers(conf.GithubOrg, "admin")
		if err != nil {
			log.Printf("[GitHubCallbackHandler] Error: %s\n", err)
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		isOrgAdmin := false
		for _, admin := range orgAdmins {
			if admin.ID == *githubUser.ID {
				isOrgAdmin = true
				break
			}
		}

		// Create session token
		sessionToken := services.GenerateAccessToken()

		// Create user object
		token, _ := oauth2Login.TokenFromContext(ctx)
		apiAccessToken := token.AccessToken

		// User data: github.User{
		// 	Login:"Username",
		// 	ID:123456,
		// 	NodeID:"ABCDEF==",
		// 	AvatarURL:"https://avatars.githubusercontent.com/u/123456?v=4",
		// 	HTMLURL:"https://github.com/Username",
		// 	GravatarID:"",
		// 	Name:"Name Surname",
		// 	Company:"Wunder",
		// 	Blog:"",
		// 	Location:"Latvia, Valmiera",
		// 	Email:"<EMAIL>",
		// 	PublicRepos:15,
		// 	PublicGists:0,
		// 	Followers:4,
		// 	Following:2,
		// 	CreatedAt:github.Timestamp{2011-02-24 08:29:13 +0000 UTC},
		// 	UpdatedAt:github.Timestamp{2023-02-06 20:10:12 +0000 UTC},
		// 	Type:"User",
		// 	SiteAdmin:false,
		// 	URL:"https://api.github.com/users/Username",
		// 	EventsURL:"https://api.github.com/users/Username/events{/privacy}",
		// 	FollowingURL:"https://api.github.com/users/Username/following{/other_user}",
		// 	FollowersURL:"https://api.github.com/users/Username/followers",
		// 	GistsURL:"https://api.github.com/users/Username/gists{/gist_id}",
		// 	OrganizationsURL:"https://api.github.com/users/Username/orgs",
		// 	ReceivedEventsURL:"https://api.github.com/users/Username/received_events",
		// 	ReposURL:"https://api.github.com/users/Username/repos",
		// 	StarredURL:"https://api.github.com/users/Username/starred{/owner}{/repo}",
		// 	SubscriptionsURL:"https://api.github.com/users/Username/subscriptions"
		// }

		sessionExpires := time.Now().Add(conf.SessionLifetime).Format(time.RFC3339)
		githubUserName := *githubUser.Login
		if githubUser.Name != nil {
			githubUserName = *githubUser.Name
		}
		newUser := schemas.User{
			Name:           githubUserName,
			SessionToken:   sessionToken,
			SessionExpires: sessionExpires,
			Github: schemas.Github{
				Name:            githubUser.Name,
				ScreenName:      githubUser.Login,
				GithubId:        githubUser.ID,
				ProfileImageUrl: githubUser.AvatarURL,
				ApiAccessToken:  apiAccessToken,
				OrgScope:        orgScope,
				OrgAdmin:        isOrgAdmin,
			},
		}

		// Try to find existing user by Github ID
		filter := bson.M{"github.githubId": githubUser.ID}
		var getUser schemas.User
		err = db.Users.FindOne(context.TODO(), filter).Decode(&getUser)
		if err != nil {
			log.Println("[GitHubCallbackHandler] User not found, creating one")

			// Generate unique uuid for user
			newUser.Uuid, err = services.CreateUserUUID()
			if err != nil {
				log.Printf("[GitHubCallbackHandler] User UUID creation error: %s\n", err)
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}

			// Create user
			_, err = db.Users.InsertOne(ctx, newUser)
			if err != nil {
				log.Printf("[GitHubCallbackHandler] User creation error: %s\n", err)
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}

		} else {
			log.Println("[GitHubCallbackHandler] User found, updating user")

			// Update user object
			getUser.Name = newUser.Name
			getUser.Github = newUser.Github
			getUser.SessionToken = newUser.SessionToken
			getUser.SessionExpires = newUser.SessionExpires

			// Update user
			filter := bson.M{"github.githubId": githubUser.ID}
			_, err = db.Users.UpdateOne(context.TODO(), filter, bson.M{"$set": getUser})
			if err != nil {
				log.Printf("[GitHubCallbackHandler] User update error: %s\n", err)
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}

			// Remove user cache for this user
			services.RemoveUserCache(getUser.Uuid)
		}

		// Store session token in session
		rh.Session.Put(r.Context(), "session_token", sessionToken)

		http.Redirect(w, r, conf.FrontendURL+conf.FrontendRoutePrefix+"/namespaces", http.StatusFound)
	}
	return http.HandlerFunc(fn)
}
