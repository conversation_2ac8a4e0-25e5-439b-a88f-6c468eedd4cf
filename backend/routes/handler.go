package routes

import (
	"encoding/json"
	"net/http"

	"github.com/alexedwards/scs/v2"
	"github.com/wunderio/silta-dashboard/backend/conf"
)

type RouteHandler struct {
	Session *scs.SessionManager
	Conf    *conf.Config
}

func (rh RouteHandler) StatusUnauthorized(w http.ResponseWriter, r *http.Request) {

	type apiAuthResponseStruct struct {
		Authenticated bool   `json:"authenticated"`
		Message       string `json:"message"`
	}

	resp := apiAuthResponseStruct{
		Authenticated: false,
		Message:       "user has not been authenticated",
	}

	// Return StatusUnauthorized with json body
	w.<PERSON>er().Set("Content-Type", "application/json")
	w.<PERSON>rite<PERSON>eader(http.StatusUnauthorized)
	json.NewEncoder(w).Encode(resp)
}

// Index page handler
func (rh *RouteHandler) IndexHandler(w http.ResponseWriter, r *http.Request) {
	http.Redirect(w, r, conf.FrontendURL+conf.FrontendRoutePrefix, http.StatusFound)
}
