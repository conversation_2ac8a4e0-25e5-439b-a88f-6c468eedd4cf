# Dashboard for silta cluster

This project allows viewing container logs in [silta cluster](https://github.com/wunderio/charts/tree/master/silta-cluster).

## Quickstart
See "Local cluster" section for isolated development environment. Run `docker compose up` when cluster and configuration is set up.

## Environment setup
Steps 1-2 are not relevant if service accounts and Github Oauth App are already created.
1. Create cluster service account and token. See `keys/make-service-account.sh`.
2. Create at least Github Oauth App, see "Oauth Login" section.
3. Rename `backend/conf/example.conf.json` to `backend/conf/conf.json` and edit the file. Alternative: ask
a colleague to securely provide local configuration file. See Backend setup for more details.
5. Run `docker-compose up` (or `docker-compose up --build` to rebuild locally cached images)

## Setting up

**Oauth login**
Dashboard requires Github Oauth application setup for dashboard login and Google Oauth application 
setup for account mapping, cluster RBAC creation. 

See [Oauth setup](docs/oauth.md) for instructions.

**CircleCI access provisioning**

Optionally, Dash<PERSON> can use CircleCI API to inject access credentials to CircleCI projects. 
You'll need to set up personal access token for CircleCI API. 

See [CircleCI API token](docs/circleci.md) for instructions. 

**MongoDB:**
 - Project requires MongoDB instance for session storage.

**Backend:**
 - Add clusters to dashboard configuration (`APP_CONFIG_JSON`) by adding cluster service account and token. See `keys/make-service-account.sh`.
 - Provide following environment variables to backend:
  - `APP_CONFIG_JSON`: Application variables/secrets (oauth secrets, cluster connection secrets, etc.)
    - Option A (Silta hosted): Create encrypted file that defines `services.backend.env.APP_CONFIG_JSON: |`
    - Option B (used for local) Rename `backend/conf/example.config.json` to `backend/conf/config.json` and edit the file.
  - `MONGODB_URI`: Mongodb connection URI, i.e. `mongodb://localhost:27017`
  - `FRONTEND_PUBLIC_URL`: Public url of frontend, i.e. `http://localhost:3000`
  - `BACKEND_PUBLIC_URL`: Public url of backend, i.e. `https://localhost:4000`
  - `SYNC_STORAGE_URL`: Public url of storage, i.e. `https://localhost:7000`
  - `FRONTEND_ROUTE_PREFIX`: Optional, when backend is hosted as subpath of frontend (i.e. /frontend)
  - `BACKEND_ROUTE_PREFIX`: Optional, when backend is hosted as subpath of frontend (i.e. /backend)
  - `SYNC_STORAGE_ROUTE_PREFIX`: Optional, when storage is hosted as subpath of frontend (i.e. /sync-storage)

**Frontend:**
 - Provide `REACT_APP_BACKEND_URL` environment variable that points to public url of backend. Backend will handle github login, user sessions and api calls to kubernetes cluster.

## Optional: Local cluster

This installs a local kubernetes cluster in minikube, sets up silta-cluster and deploys drupal, frontend and simple charts.

1. Navigate to `test` directory and run this to set up cluster and deploy drupal, frontend and simple charts

```
./0-all.sh
```

2. Create dashboard service account in cluster.

Create a dashboard service account and kubeconfig file (i.e. `k8s-silta-dashboard-sa-kube-system-conf`) that you'll need to add to configuration in next step.
Note: make sure you are on correct cluster (minikube)
```
keys/make-service-account.sh
```

3. Create dashboard configuration file.

- Copy `backend/conf/example.conf.json` to `backend/conf/conf.json`
- Edit values in clusters section
```
"cluster": {
  "type": "minikube",
  "name": "local",
  "caData": "LS0tLS1...S0tCg==",
  "server": "https://************:8443",
  "insecureSkipTLSVerify": false
},
"user": {
  "name": "silta-dashboard-sa-kube-system-minikube",
  "token": "eyJhbGc...ZInA"
},
```
 - `caData`: value from `clusters[0].cluster.certificate-authority-data`
 - `server`: 
   - For Linux users: value from `clusters[0].cluster.server`
   - For MaxOS users: use value from `clusters[0].cluster.server` but replace server IP with `host.docker.internal`, keep the port number. I.e: `https://host.docker.internal:54302`
 - `user.name`: value from `users[0].name`
 - `user.token`: value from `users[0].user.token`

Create value for `password_login_password` and set `password_login_enabled` to `true`, this will rely on static password authentication instead of external oauth. USE THIS ONLY FOR DEVELOPMENT! 
  
## API documentation

API documentation available in [docs/](docs/) folder.

Backend API available via Swagger UI at [http://localhost:4000/swagger/](http://localhost:4000/swagger/). Requires logged in user.
Note for developers: update swagger documentation by running `swag init --parseDependency  --parseInternal --quiet` in `backend/` folder.