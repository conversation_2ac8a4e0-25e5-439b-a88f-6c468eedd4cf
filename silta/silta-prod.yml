
# Values in this file override the default values of our helm chart.
#
# See https://github.com/wunderio/charts/blob/master/frontend/values.yaml
# for all possible options.

silta-release:
  downscaler:
    enabled: false

exposeDomains:
  prod:
    hostname: dashboard.silta.wdr.io
    ssl:
      enabled: true
      issuer: letsencrypt

services:
  backend:
    env:
      FRONTEND_PUBLIC_URL: https://dashboard.silta.wdr.io
      BACKEND_PUBLIC_URL: https://dashboard.silta.wdr.io
      SYNC_STORAGE_URL: https://dashboard.silta.wdr.io
    nodeSelector:
      cloud.google.com/gke-nodepool: static-ip
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 4
    resources:
      requests:
        cpu: 300m
        memory: 256Mi
      limits:
        cpu: 1
        memory: 2Gi
    cron:
      reloadallrbac:
        # Nightly RBAC reload for GH and CircleCI (and possible CircleCI rotation if secret if older than month)
        command: |
          /app/backend --recreate-gh-rbac --recreate-cci-rbac
        schedule: "~ 2 * * *"
      reloadghrbac:
        # Run RBAC reload every hour (github users only)
        command: |
          /app/backend --recreate-gh-rbac
        schedule: "0 * * * *"
      clusterinventory:
        # Gather cluster inventory
        command: |
          /app/backend --cluster-inventory
        schedule: "~ 1 * * *"
  syncstorage:
    resources:
      requests:
        cpu: 500m
        memory: 512Mi
      limits:
        cpu: 2
        memory: 2Gi

nginx:
  noauthips:
    # https://wunder.slack.com/archives/C468AG2CS/p1711543313290549?thread_ts=1708516569.545469&cid=C468AG2CS
    # https://wunder.slack.com/archives/C02JY8H9WPJ/p1711541836692539?thread_ts=1711540954.581419&cid=C02JY8H9WPJ
    inderes-office: *************
    # Siili Solutions VPN, they develop Aalto-sites hosted on Silta
    siili-vpn: ************
    
    
mongodb:
  # auth credentials in secrets
  resources:
    limits:
      memory: 5Gi
      cpu: 2
    requests:
      memory: 3Gi
      cpu: 500m
