
# Values in this file override the default values of our helm chart.
#
# See https://github.com/wunderio/charts/blob/master/frontend/values.yaml
# for all possible options.

nginx:
  basicauth:
    enabled: true
    credentials:
      username: wunder
      password: kDPG1JIxqrFxkDPG1JIxqrFx

  noauthips:
    gke-internal: 10.0.0.0/8
    vpn-telia: **************/32
    # allows webdav mounts
    test-cluster: ************/32
    dev-cluster: **************/32
    production-a: ***********/32
    # note: sync pods on prod cluster need static ip

  resources:
    requests:
      cpu: 50m
      memory: 50Mi

services: 
  web:
    exposedRoute: '/'
    port: 80
    #env:
    #  Moved to circleci build process
    #  REACT_APP_BACKEND_URL: '/backend'
    resources:
      requests:
        cpu: 100m
        memory: 256Mi
      limits:
        cpu: 1000m
        memory: 1Gi

  backend:
    exposedRoute: '/backend'
    port: 4000
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 1000m
        memory: 1Gi
    env:
      BACKEND_ROUTE_PREFIX: '/backend'
      SYNC_STORAGE_ROUTE_PREFIX: '/sync-storage'

  syncstorage:
    exposedRoute: '/sync-storage'
    port: 7000
    mounts:
      - syncstorage
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 1000m
        memory: 1Gi
    env:
      SYNC_STORAGE_ROUTE_PREFIX: '/sync-storage'
    
    nginx:
      locationExtraConfig: |
        client_max_body_size 0;
      # allows .htaccess file upload
      denyDotFiles: false

mounts:
  syncstorage:
    enabled: true
    storage: 10G
    mountPath: /app/mount
    storageClassName: silta-shared
    csiDriverName: csi-rclone
    accessModes: ReadWriteMany

mongodb:
  enabled: true
  auth:
    # credentials in secrets
    enabled: true
  resources:
    limits:
      memory: 512Mi
      cpu: 1
    requests:
      memory: 256Mi
      cpu: 100m
  persistence:
    size: 2Gi

ingress:
  default:
    extraAnnotations:
      # Rate limits removed due to sync storage (upload) failures. Access is limited to certain IP addresses anyway.
      nginx.ingress.kubernetes.io/limit-rps: "0"
      nginx.ingress.kubernetes.io/limit-rpm: "0"
      nginx.ingress.kubernetes.io/limit-connections: "0"
