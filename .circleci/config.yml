version: 2.1

orbs:
  silta: silta/silta@dev:master


executors:
  node22:
    docker:
      - image: wunderio/silta-cicd:circleci-php8.3-node22-composer2-v1

workflows:
  version: 2
  build_deploy:
    jobs:
      - silta/frontend-build-deploy: &build-deploy
          name: 'Silta build & deploy'
          executor: node22
          codebase-build:
            - silta/npm-install-build:
                path: './web'
                build-command: ''
            - silta/decrypt-files:
                files: silta/secret-master
                secret_key_env: PROJECT_SECRET_KEY
          image_build_steps:
            - silta/build-docker-image:
                dockerfile: 'silta/web.Dockerfile'
                path: '.'
                identifier: 'web'
            - silta/build-docker-image:
                dockerfile: 'silta/backend.Dockerfile'
                path: '.'
                identifier: 'backend'
            - silta/build-docker-image:
                dockerfile: 'silta/sync-storage.Dockerfile'
                path: '.'
                identifier: 'syncstorage'
          context: 'silta_dev'
          
          # Reuse secrets from master environment
          silta_config: silta/silta.yml,silta/secret-master
          filters:
            branches:
              ignore:
                - 'production'
                - 'master'

      - silta/frontend-build-deploy: 
          name: 'Silta build & deploy master'
          <<: *build-deploy
          codebase-build:
            - silta/npm-install-build:
                path: './web'
                build-command: ''
            - silta/decrypt-files:
                files: silta/secret-master
                secret_key_env: PROJECT_SECRET_KEY
          silta_config: silta/silta.yml,silta/silta-master.yml,silta/secret-master
          filters:
            branches:
              only: 'master'

      - silta/frontend-build-deploy: 
          name: 'Silta build & deploy production'
          <<: *build-deploy
          codebase-build:
            - silta/npm-install-build:
                path: './web'
                build-command: ''
            - silta/decrypt-files:
                files: silta/secret-prod
                secret_key_env: PROJECT_SECRET_KEY
          context: 'silta_finland'
          silta_config: silta/silta.yml,silta/silta-prod.yml,silta/secret-prod
          filters:
            branches:
              only: 'production'
